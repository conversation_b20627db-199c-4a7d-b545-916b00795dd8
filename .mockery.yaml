dir: '{{.InterfaceDir}}/mocks/'
structname: '{{.InterfaceName}}'
pkgname: mocks
template: testify
filename: mocks.go
template-data:
  boilerplate-file: .mockery.header.txt
packages:
  github.com/jaegertracing/jaeger/cmd/collector/app/sanitizer/cache:
    interfaces:
      ServiceAliasMappingExternalSource: {}
      ServiceAliasMappingStorage: {}
  github.com/jaegertracing/jaeger/cmd/ingester/app/consumer:
    interfaces:
      Message: {}
  github.com/jaegertracing/jaeger/cmd/ingester/app/processor:
    interfaces:
      SpanProcessor: {}
  github.com/jaegertracing/jaeger/crossdock/services:
    interfaces:
      CollectorService: {}
      QueryService: {}
  github.com/jaegertracing/jaeger/internal/distributedlock:
    config:
      all: true
  github.com/jaegertracing/jaeger/internal/leaderelection:
    config:
      all: true
  github.com/jaegertracing/jaeger/internal/proto-gen/storage_v1:
    config:
      all: true
  github.com/jaegertracing/jaeger/internal/storage/cassandra:
    config:
      template-data:
        unroll-variadic: false
    interfaces:
      Iterator: {}
      Query: {}
      Session: {}
  github.com/jaegertracing/jaeger/internal/storage/elasticsearch:
    config:
      all: true
  github.com/jaegertracing/jaeger/internal/storage/elasticsearch/client:
    config:
      all: true
  github.com/jaegertracing/jaeger/internal/storage/kafka/consumer:
    interfaces:
      Consumer: {}
  github.com/jaegertracing/jaeger/internal/storage/v1:
    config:
      all: true
  github.com/jaegertracing/jaeger/internal/storage/v1/api/dependencystore:
    interfaces:
      Reader: {}
  github.com/jaegertracing/jaeger/internal/storage/v1/api/metricstore:
    config:
      all: true
  github.com/jaegertracing/jaeger/internal/storage/v1/api/samplingstore:
    config:
      all: true
  github.com/jaegertracing/jaeger/internal/storage/v1/api/spanstore:
    config:
      all: true
  github.com/jaegertracing/jaeger/internal/storage/v2/elasticsearch/depstore:
    interfaces:
      CoreDependencyStore: {}
  github.com/jaegertracing/jaeger/internal/storage/v1/elasticsearch/spanstore:
    interfaces:
      CoreSpanReader: {}
      CoreSpanWriter: {}
  github.com/jaegertracing/jaeger/internal/storage/v1/grpc/shared:
    interfaces:
      PluginCapabilities: {}
  github.com/jaegertracing/jaeger/internal/storage/v1/kafka:
    interfaces:
      Marshaller: {}
      Unmarshaller: {}
  github.com/jaegertracing/jaeger/internal/storage/v2/api/depstore:
    config:
      all: true
  github.com/jaegertracing/jaeger/internal/storage/v2/api/tracestore:
    config:
      all: true
