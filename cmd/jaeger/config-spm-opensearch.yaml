service:
  extensions: [jaeger_storage, jaeger_query]
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch]
      exporters: [jaeger_storage_exporter]

extensions:
  jaeger_query:
    storage:
      traces: opensearch_trace_storage
      metrics: opensearch_trace_storage
  jaeger_storage:
    backends:
      opensearch_trace_storage: &opensearch_config
        opensearch:
          server_urls:
            - http://opensearch:9200
    metric_backends:
      opensearch_trace_storage: *opensearch_config

receivers:
  otlp:
    protocols:
      grpc:
      http:
        endpoint: "0.0.0.0:4318"

processors:
  batch:

exporters:
  jaeger_storage_exporter:
    trace_storage: opensearch_trace_storage