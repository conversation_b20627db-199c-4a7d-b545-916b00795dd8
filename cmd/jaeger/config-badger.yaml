service:
  extensions: [jaeger_storage, jaeger_query, healthcheckv2]
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch]
      exporters: [jaeger_storage_exporter]
  telemetry:
    resource:
      service.name: jaeger
    metrics:
      level: detailed
      readers:
        - pull:
            exporter:
              prometheus:
                host: 0.0.0.0
                port: 8888
    logs:
      level: info
    # TODO Initialize telemetry tracer once OTEL released new feature.
    # https://github.com/open-telemetry/opentelemetry-collector/issues/10663

extensions:
  healthcheckv2:
    use_v2: true
    http:

  jaeger_query:
    storage:
      traces: some_store
      traces_archive: another_store
    ui:
      config_file: ./cmd/jaeger/config-ui.json

  jaeger_storage:
    backends:
      some_store:
        badger:
          directories:
            keys: "/tmp/jaeger/"
            values: "/tmp/jaeger/"
          ephemeral: false
          ttl:
            spans: 48h # 2 days
      another_store:
        badger:
          directories:
            keys: "/tmp/jaeger_archive/"
            values: "/tmp/jaeger_archive/"
          ephemeral: false
          ttl:
            spans: 720h # 30 days

receivers:
  otlp:
    protocols:
      grpc:
      http:

processors:
  batch:

exporters:
  jaeger_storage_exporter:
    trace_storage: some_store
