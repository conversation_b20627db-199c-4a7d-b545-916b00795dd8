{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:best-practices", ":gitSignOff"], "labels": ["changelog:dependencies"], "postUpdateOptions": ["gomodTidy", "gomodUpdateImportPaths"], "suppressNotifications": ["prEditedNotification"], "schedule": ["on the first day of the month"], "packageRules": [{"matchFileNames": ["docker-compose/**/docker-compose.y*ml"], "matchUpdateTypes": ["major", "patch", "digest"], "enabled": false}, {"matchManagers": ["github-actions"], "groupName": "github-actions deps"}, {"matchManagers": ["github-actions"], "matchUpdateTypes": ["patch", "digest"], "enabled": false}, {"groupName": "All OTEL SDK + contrib packages", "groupSlug": "go-otel-sdk", "matchDatasources": ["go"], "matchPackageNames": ["go.opentelemetry.io/otel/**", "go.opentelemetry.io/contrib/**", "github.com/open-telemetry/opentelemetry-go-contrib/**"]}, {"groupName": "All OTEL Collector packages", "matchManagers": ["gomod"], "matchPackageNames": ["go.opentelemetry.io/collector{/,}**"]}, {"groupName": "All OTEL Collector contrib packages", "matchManagers": ["gomod"], "matchPackageNames": ["github.com/open-telemetry/opentelemetry-collector-contrib{/,}**"]}, {"groupName": "All google.golang.org packages", "matchManagers": ["gomod"], "matchSourceUrls": ["google.golang.org{/,}**"]}, {"groupName": "All golang.org/x packages", "matchManagers": ["gomod"], "matchPackageNames": ["golang.org/x{/,}**"]}, {"groupName": "All github.com/prometheus packages", "matchManagers": ["gomod"], "matchPackageNames": ["github.com/prometheus{/,}**"]}, {"groupName": "Exclude frequent tools upgrades", "matchDatasources": ["go"], "matchPackageNames": ["github.com/vektra/mockery/**"], "matchUpdateTypes": ["patch"], "enabled": false}]}