// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: query_service.proto

package api_v3

import (
	context "context"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	_ "github.com/gogo/protobuf/types"
	github_com_gogo_protobuf_types "github.com/gogo/protobuf/types"
	v1 "github.com/jaegertracing/jaeger/internal/jptrace"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	io "io"
	math "math"
	math_bits "math/bits"
	time "time"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf
var _ = time.Kitchen

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// Request object to get a trace.
type GetTraceRequest struct {
	// Hex encoded 64 or 128 bit trace ID.
	TraceId string `protobuf:"bytes,1,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	// Optional. The start time to search trace ID.
	StartTime time.Time `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3,stdtime" json:"start_time"`
	// Optional. The end time to search trace ID.
	EndTime time.Time `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3,stdtime" json:"end_time"`
	// Optional. If set to true, the response will not include any
	// enrichments to the trace, such as clock skew adjustment.
	// Instead, the trace will be returned exactly as stored.
	RawTraces            bool     `protobuf:"varint,4,opt,name=raw_traces,json=rawTraces,proto3" json:"raw_traces,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTraceRequest) Reset()         { *m = GetTraceRequest{} }
func (m *GetTraceRequest) String() string { return proto.CompactTextString(m) }
func (*GetTraceRequest) ProtoMessage()    {}
func (*GetTraceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5fcb6756dc1afb8d, []int{0}
}
func (m *GetTraceRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetTraceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetTraceRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetTraceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTraceRequest.Merge(m, src)
}
func (m *GetTraceRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetTraceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTraceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTraceRequest proto.InternalMessageInfo

func (m *GetTraceRequest) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *GetTraceRequest) GetStartTime() time.Time {
	if m != nil {
		return m.StartTime
	}
	return time.Time{}
}

func (m *GetTraceRequest) GetEndTime() time.Time {
	if m != nil {
		return m.EndTime
	}
	return time.Time{}
}

func (m *GetTraceRequest) GetRawTraces() bool {
	if m != nil {
		return m.RawTraces
	}
	return false
}

// Query parameters to find traces.
//
// All fields form a conjunction (e.g., "service_name='X' AND operation_name='Y' AND ..."),
// except for `search_depth` and `raw_traces`.
//
// Fields are matched against individual spans, not the trace level. The results include
// traces with at least one matching span.
//
// The results have no guaranteed ordering.
type TraceQueryParameters struct {
	// service_name filters spans generated by a specific service.
	ServiceName string `protobuf:"bytes,1,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// operation_name filters spans by a specific operation / span name.
	OperationName string `protobuf:"bytes,2,opt,name=operation_name,json=operationName,proto3" json:"operation_name,omitempty"`
	// attributes contains key-value pairs where the key is the attribute name
	// and the value is its string representation. Attributes are matched against
	// span and resource attributes. At least one span must match all specified attributes.
	Attributes map[string]string `protobuf:"bytes,3,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// start_time_min is the start of the time interval (inclusive) for the query.
	// Only traces with spans that started on or after this time will be returned.
	//
	// The HTTP API uses RFC-3339ns format.
	//
	// This field is required.
	StartTimeMin time.Time `protobuf:"bytes,4,opt,name=start_time_min,json=startTimeMin,proto3,stdtime" json:"start_time_min"`
	// start_time_max is the end of the time interval (exclusive) for the query.
	// Only traces with spans that started before this time will be returned.
	//
	// The HTTP API uses RFC-3339ns format.
	//
	// This field is required.
	StartTimeMax time.Time `protobuf:"bytes,5,opt,name=start_time_max,json=startTimeMax,proto3,stdtime" json:"start_time_max"`
	// duration_min is the minimum duration of a span in the trace.
	// Only traces with spans that lasted at least this long will be returned.
	//
	// The HTTP API uses Golang's time format (e.g., "10s").
	DurationMin time.Duration `protobuf:"bytes,6,opt,name=duration_min,json=durationMin,proto3,stdduration" json:"duration_min"`
	// duration_max is the maximum duration of a span in the trace.
	// Only traces with spans that lasted at most this long will be returned.
	//
	// The HTTP API uses Golang's time format (e.g., "10s").
	DurationMax time.Duration `protobuf:"bytes,7,opt,name=duration_max,json=durationMax,proto3,stdduration" json:"duration_max"`
	// search_depth defines the maximum search depth. Depending on the backend storage implementation,
	// this may behave like an SQL `LIMIT` clause. However, some implementations might not support
	// precise limits, and a larger value generally results in more traces being returned.
	SearchDepth int32 `protobuf:"varint,8,opt,name=search_depth,json=searchDepth,proto3" json:"search_depth,omitempty"`
	// If set to true, the response will exclude any enrichments to the trace, such as clock skew adjustments.
	// The trace will be returned exactly as stored.
	//
	// This field is optional.
	RawTraces            bool     `protobuf:"varint,9,opt,name=raw_traces,json=rawTraces,proto3" json:"raw_traces,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TraceQueryParameters) Reset()         { *m = TraceQueryParameters{} }
func (m *TraceQueryParameters) String() string { return proto.CompactTextString(m) }
func (*TraceQueryParameters) ProtoMessage()    {}
func (*TraceQueryParameters) Descriptor() ([]byte, []int) {
	return fileDescriptor_5fcb6756dc1afb8d, []int{1}
}
func (m *TraceQueryParameters) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TraceQueryParameters) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TraceQueryParameters.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TraceQueryParameters) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TraceQueryParameters.Merge(m, src)
}
func (m *TraceQueryParameters) XXX_Size() int {
	return m.Size()
}
func (m *TraceQueryParameters) XXX_DiscardUnknown() {
	xxx_messageInfo_TraceQueryParameters.DiscardUnknown(m)
}

var xxx_messageInfo_TraceQueryParameters proto.InternalMessageInfo

func (m *TraceQueryParameters) GetServiceName() string {
	if m != nil {
		return m.ServiceName
	}
	return ""
}

func (m *TraceQueryParameters) GetOperationName() string {
	if m != nil {
		return m.OperationName
	}
	return ""
}

func (m *TraceQueryParameters) GetAttributes() map[string]string {
	if m != nil {
		return m.Attributes
	}
	return nil
}

func (m *TraceQueryParameters) GetStartTimeMin() time.Time {
	if m != nil {
		return m.StartTimeMin
	}
	return time.Time{}
}

func (m *TraceQueryParameters) GetStartTimeMax() time.Time {
	if m != nil {
		return m.StartTimeMax
	}
	return time.Time{}
}

func (m *TraceQueryParameters) GetDurationMin() time.Duration {
	if m != nil {
		return m.DurationMin
	}
	return 0
}

func (m *TraceQueryParameters) GetDurationMax() time.Duration {
	if m != nil {
		return m.DurationMax
	}
	return 0
}

func (m *TraceQueryParameters) GetSearchDepth() int32 {
	if m != nil {
		return m.SearchDepth
	}
	return 0
}

func (m *TraceQueryParameters) GetRawTraces() bool {
	if m != nil {
		return m.RawTraces
	}
	return false
}

// Request object to search traces.
type FindTracesRequest struct {
	Query                *TraceQueryParameters `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *FindTracesRequest) Reset()         { *m = FindTracesRequest{} }
func (m *FindTracesRequest) String() string { return proto.CompactTextString(m) }
func (*FindTracesRequest) ProtoMessage()    {}
func (*FindTracesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5fcb6756dc1afb8d, []int{2}
}
func (m *FindTracesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FindTracesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FindTracesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FindTracesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FindTracesRequest.Merge(m, src)
}
func (m *FindTracesRequest) XXX_Size() int {
	return m.Size()
}
func (m *FindTracesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FindTracesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FindTracesRequest proto.InternalMessageInfo

func (m *FindTracesRequest) GetQuery() *TraceQueryParameters {
	if m != nil {
		return m.Query
	}
	return nil
}

// Request object to get service names.
type GetServicesRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetServicesRequest) Reset()         { *m = GetServicesRequest{} }
func (m *GetServicesRequest) String() string { return proto.CompactTextString(m) }
func (*GetServicesRequest) ProtoMessage()    {}
func (*GetServicesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5fcb6756dc1afb8d, []int{3}
}
func (m *GetServicesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetServicesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetServicesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetServicesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetServicesRequest.Merge(m, src)
}
func (m *GetServicesRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetServicesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetServicesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetServicesRequest proto.InternalMessageInfo

// Response object to get service names.
type GetServicesResponse struct {
	Services             []string `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetServicesResponse) Reset()         { *m = GetServicesResponse{} }
func (m *GetServicesResponse) String() string { return proto.CompactTextString(m) }
func (*GetServicesResponse) ProtoMessage()    {}
func (*GetServicesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5fcb6756dc1afb8d, []int{4}
}
func (m *GetServicesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetServicesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetServicesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetServicesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetServicesResponse.Merge(m, src)
}
func (m *GetServicesResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetServicesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetServicesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetServicesResponse proto.InternalMessageInfo

func (m *GetServicesResponse) GetServices() []string {
	if m != nil {
		return m.Services
	}
	return nil
}

// Request object to get operation names.
type GetOperationsRequest struct {
	// Required service name.
	Service string `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// Optional span kind.
	SpanKind             string   `protobuf:"bytes,2,opt,name=span_kind,json=spanKind,proto3" json:"span_kind,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOperationsRequest) Reset()         { *m = GetOperationsRequest{} }
func (m *GetOperationsRequest) String() string { return proto.CompactTextString(m) }
func (*GetOperationsRequest) ProtoMessage()    {}
func (*GetOperationsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5fcb6756dc1afb8d, []int{5}
}
func (m *GetOperationsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetOperationsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetOperationsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetOperationsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperationsRequest.Merge(m, src)
}
func (m *GetOperationsRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetOperationsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperationsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperationsRequest proto.InternalMessageInfo

func (m *GetOperationsRequest) GetService() string {
	if m != nil {
		return m.Service
	}
	return ""
}

func (m *GetOperationsRequest) GetSpanKind() string {
	if m != nil {
		return m.SpanKind
	}
	return ""
}

// Operation encapsulates information about operation.
type Operation struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	SpanKind             string   `protobuf:"bytes,2,opt,name=span_kind,json=spanKind,proto3" json:"span_kind,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Operation) Reset()         { *m = Operation{} }
func (m *Operation) String() string { return proto.CompactTextString(m) }
func (*Operation) ProtoMessage()    {}
func (*Operation) Descriptor() ([]byte, []int) {
	return fileDescriptor_5fcb6756dc1afb8d, []int{6}
}
func (m *Operation) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Operation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Operation.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Operation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Operation.Merge(m, src)
}
func (m *Operation) XXX_Size() int {
	return m.Size()
}
func (m *Operation) XXX_DiscardUnknown() {
	xxx_messageInfo_Operation.DiscardUnknown(m)
}

var xxx_messageInfo_Operation proto.InternalMessageInfo

func (m *Operation) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Operation) GetSpanKind() string {
	if m != nil {
		return m.SpanKind
	}
	return ""
}

// Response object to get operation names.
type GetOperationsResponse struct {
	Operations           []*Operation `protobuf:"bytes,1,rep,name=operations,proto3" json:"operations,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetOperationsResponse) Reset()         { *m = GetOperationsResponse{} }
func (m *GetOperationsResponse) String() string { return proto.CompactTextString(m) }
func (*GetOperationsResponse) ProtoMessage()    {}
func (*GetOperationsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5fcb6756dc1afb8d, []int{7}
}
func (m *GetOperationsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetOperationsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetOperationsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetOperationsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperationsResponse.Merge(m, src)
}
func (m *GetOperationsResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetOperationsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperationsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperationsResponse proto.InternalMessageInfo

func (m *GetOperationsResponse) GetOperations() []*Operation {
	if m != nil {
		return m.Operations
	}
	return nil
}

// GRPCGatewayError is the type returned when GRPC server returns an error.
// Example: {"error":{"grpcCode":2,"httpCode":500,"message":"...","httpStatus":"text..."}}.
type GRPCGatewayError struct {
	Error                *GRPCGatewayError_GRPCGatewayErrorDetails `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                  `json:"-"`
	XXX_unrecognized     []byte                                    `json:"-"`
	XXX_sizecache        int32                                     `json:"-"`
}

func (m *GRPCGatewayError) Reset()         { *m = GRPCGatewayError{} }
func (m *GRPCGatewayError) String() string { return proto.CompactTextString(m) }
func (*GRPCGatewayError) ProtoMessage()    {}
func (*GRPCGatewayError) Descriptor() ([]byte, []int) {
	return fileDescriptor_5fcb6756dc1afb8d, []int{8}
}
func (m *GRPCGatewayError) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GRPCGatewayError) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GRPCGatewayError.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GRPCGatewayError) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GRPCGatewayError.Merge(m, src)
}
func (m *GRPCGatewayError) XXX_Size() int {
	return m.Size()
}
func (m *GRPCGatewayError) XXX_DiscardUnknown() {
	xxx_messageInfo_GRPCGatewayError.DiscardUnknown(m)
}

var xxx_messageInfo_GRPCGatewayError proto.InternalMessageInfo

func (m *GRPCGatewayError) GetError() *GRPCGatewayError_GRPCGatewayErrorDetails {
	if m != nil {
		return m.Error
	}
	return nil
}

type GRPCGatewayError_GRPCGatewayErrorDetails struct {
	GrpcCode             int32    `protobuf:"varint,1,opt,name=grpcCode,proto3" json:"grpcCode,omitempty"`
	HttpCode             int32    `protobuf:"varint,2,opt,name=httpCode,proto3" json:"httpCode,omitempty"`
	Message              string   `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	HttpStatus           string   `protobuf:"bytes,4,opt,name=httpStatus,proto3" json:"httpStatus,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GRPCGatewayError_GRPCGatewayErrorDetails) Reset() {
	*m = GRPCGatewayError_GRPCGatewayErrorDetails{}
}
func (m *GRPCGatewayError_GRPCGatewayErrorDetails) String() string { return proto.CompactTextString(m) }
func (*GRPCGatewayError_GRPCGatewayErrorDetails) ProtoMessage()    {}
func (*GRPCGatewayError_GRPCGatewayErrorDetails) Descriptor() ([]byte, []int) {
	return fileDescriptor_5fcb6756dc1afb8d, []int{8, 0}
}
func (m *GRPCGatewayError_GRPCGatewayErrorDetails) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GRPCGatewayError_GRPCGatewayErrorDetails) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GRPCGatewayError_GRPCGatewayErrorDetails.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GRPCGatewayError_GRPCGatewayErrorDetails) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GRPCGatewayError_GRPCGatewayErrorDetails.Merge(m, src)
}
func (m *GRPCGatewayError_GRPCGatewayErrorDetails) XXX_Size() int {
	return m.Size()
}
func (m *GRPCGatewayError_GRPCGatewayErrorDetails) XXX_DiscardUnknown() {
	xxx_messageInfo_GRPCGatewayError_GRPCGatewayErrorDetails.DiscardUnknown(m)
}

var xxx_messageInfo_GRPCGatewayError_GRPCGatewayErrorDetails proto.InternalMessageInfo

func (m *GRPCGatewayError_GRPCGatewayErrorDetails) GetGrpcCode() int32 {
	if m != nil {
		return m.GrpcCode
	}
	return 0
}

func (m *GRPCGatewayError_GRPCGatewayErrorDetails) GetHttpCode() int32 {
	if m != nil {
		return m.HttpCode
	}
	return 0
}

func (m *GRPCGatewayError_GRPCGatewayErrorDetails) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *GRPCGatewayError_GRPCGatewayErrorDetails) GetHttpStatus() string {
	if m != nil {
		return m.HttpStatus
	}
	return ""
}

// GRPCGatewayWrapper wraps streaming responses from GetTrace/FindTraces for HTTP.
// Today there is always only one response because internally the HTTP server gets
// data from QueryService that does not support multiple responses. But in the
// future the server may return multiple responeses using Transfer-Encoding: chunked.
// In case of errors, GRPCGatewayError above is used.
//
// Example:
//     {"result": {"resourceSpans": ...}}
//
// See https://github.com/grpc-ecosystem/grpc-gateway/issues/2189
//
type GRPCGatewayWrapper struct {
	Result               *v1.TracesData `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GRPCGatewayWrapper) Reset()         { *m = GRPCGatewayWrapper{} }
func (m *GRPCGatewayWrapper) String() string { return proto.CompactTextString(m) }
func (*GRPCGatewayWrapper) ProtoMessage()    {}
func (*GRPCGatewayWrapper) Descriptor() ([]byte, []int) {
	return fileDescriptor_5fcb6756dc1afb8d, []int{9}
}
func (m *GRPCGatewayWrapper) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GRPCGatewayWrapper) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GRPCGatewayWrapper.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GRPCGatewayWrapper) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GRPCGatewayWrapper.Merge(m, src)
}
func (m *GRPCGatewayWrapper) XXX_Size() int {
	return m.Size()
}
func (m *GRPCGatewayWrapper) XXX_DiscardUnknown() {
	xxx_messageInfo_GRPCGatewayWrapper.DiscardUnknown(m)
}

var xxx_messageInfo_GRPCGatewayWrapper proto.InternalMessageInfo

func (m *GRPCGatewayWrapper) GetResult() *v1.TracesData {
	if m != nil {
		return m.Result
	}
	return nil
}

func init() {
	proto.RegisterType((*GetTraceRequest)(nil), "jaeger.api_v3.GetTraceRequest")
	proto.RegisterType((*TraceQueryParameters)(nil), "jaeger.api_v3.TraceQueryParameters")
	proto.RegisterMapType((map[string]string)(nil), "jaeger.api_v3.TraceQueryParameters.AttributesEntry")
	proto.RegisterType((*FindTracesRequest)(nil), "jaeger.api_v3.FindTracesRequest")
	proto.RegisterType((*GetServicesRequest)(nil), "jaeger.api_v3.GetServicesRequest")
	proto.RegisterType((*GetServicesResponse)(nil), "jaeger.api_v3.GetServicesResponse")
	proto.RegisterType((*GetOperationsRequest)(nil), "jaeger.api_v3.GetOperationsRequest")
	proto.RegisterType((*Operation)(nil), "jaeger.api_v3.Operation")
	proto.RegisterType((*GetOperationsResponse)(nil), "jaeger.api_v3.GetOperationsResponse")
	proto.RegisterType((*GRPCGatewayError)(nil), "jaeger.api_v3.GRPCGatewayError")
	proto.RegisterType((*GRPCGatewayError_GRPCGatewayErrorDetails)(nil), "jaeger.api_v3.GRPCGatewayError.GRPCGatewayErrorDetails")
	proto.RegisterType((*GRPCGatewayWrapper)(nil), "jaeger.api_v3.GRPCGatewayWrapper")
}

func init() { proto.RegisterFile("query_service.proto", fileDescriptor_5fcb6756dc1afb8d) }

var fileDescriptor_5fcb6756dc1afb8d = []byte{
	// 871 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x55, 0xdd, 0x72, 0xdb, 0x44,
	0x14, 0x8e, 0xec, 0x38, 0xb1, 0x8e, 0x93, 0xb6, 0x6c, 0xcd, 0x54, 0x15, 0x83, 0xe3, 0xaa, 0x30,
	0xe3, 0x1b, 0x64, 0xe2, 0x5c, 0x50, 0x18, 0x18, 0xa0, 0x49, 0xeb, 0x01, 0x26, 0xa5, 0x55, 0x3a,
	0x85, 0x61, 0x3a, 0xa3, 0xd9, 0x44, 0x07, 0x47, 0xd4, 0x96, 0xd4, 0xdd, 0x95, 0x63, 0x3f, 0x03,
	0x37, 0x5c, 0xf2, 0x48, 0xbd, 0x84, 0x17, 0x28, 0x4c, 0x5e, 0x80, 0x17, 0xe0, 0x82, 0xd9, 0x1f,
	0xa9, 0xb6, 0x4c, 0x33, 0x69, 0xaf, 0xbc, 0xe7, 0xec, 0x77, 0x3e, 0x9f, 0x9f, 0xef, 0xac, 0xe0,
	0xfa, 0xf3, 0x1c, 0xd9, 0x3c, 0xe4, 0xc8, 0xa6, 0xf1, 0x09, 0xfa, 0x19, 0x4b, 0x45, 0x4a, 0xb6,
	0x7f, 0xa1, 0x38, 0x42, 0xe6, 0xd3, 0x2c, 0x0e, 0xa7, 0x7b, 0x6e, 0x67, 0x94, 0xa6, 0xa3, 0x31,
	0xf6, 0xd5, 0xe5, 0x71, 0xfe, 0x73, 0x3f, 0xca, 0x19, 0x15, 0x71, 0x9a, 0x68, 0xb8, 0xdb, 0x1e,
	0xa5, 0xa3, 0x54, 0x1d, 0xfb, 0xf2, 0x64, 0xbc, 0x3b, 0xd5, 0x28, 0x11, 0x4f, 0x90, 0x0b, 0x3a,
	0xc9, 0x0c, 0xa0, 0x97, 0x66, 0x98, 0x08, 0x1c, 0xe3, 0x04, 0x05, 0x9b, 0x6b, 0x5c, 0x5f, 0x30,
	0x7a, 0x82, 0xfd, 0xe9, 0xae, 0x3e, 0x68, 0xa4, 0xf7, 0xa7, 0x05, 0x57, 0x87, 0x28, 0x1e, 0x4b,
	0x57, 0x80, 0xcf, 0x73, 0xe4, 0x82, 0xdc, 0x84, 0xa6, 0x82, 0x84, 0x71, 0xe4, 0x58, 0x5d, 0xab,
	0x67, 0x07, 0x9b, 0xca, 0xfe, 0x26, 0x22, 0xfb, 0x00, 0x5c, 0x50, 0x26, 0x42, 0xf9, 0x8f, 0x4e,
	0xad, 0x6b, 0xf5, 0x5a, 0x03, 0xd7, 0xd7, 0xe9, 0xf8, 0x45, 0x3a, 0xfe, 0xe3, 0x22, 0x9d, 0xbb,
	0xcd, 0x17, 0x2f, 0x77, 0xd6, 0x7e, 0xfb, 0x6b, 0xc7, 0x0a, 0x6c, 0x15, 0x27, 0x6f, 0xc8, 0x97,
	0xd0, 0xc4, 0x24, 0xd2, 0x14, 0xf5, 0x37, 0xa0, 0xd8, 0xc4, 0x24, 0x52, 0x04, 0xef, 0x03, 0x30,
	0x7a, 0x16, 0xaa, 0xa4, 0xb8, 0xb3, 0xde, 0xb5, 0x7a, 0xcd, 0xc0, 0x66, 0xf4, 0x4c, 0x55, 0xc1,
	0xbd, 0x97, 0xeb, 0xd0, 0x56, 0xc7, 0x47, 0x72, 0x00, 0x0f, 0x29, 0xa3, 0x13, 0x14, 0xc8, 0x38,
	0xb9, 0x05, 0x5b, 0x66, 0x1a, 0x61, 0x42, 0x27, 0x68, 0x8a, 0x6b, 0x19, 0xdf, 0x03, 0x3a, 0x41,
	0xf2, 0x21, 0x5c, 0x49, 0x33, 0xd4, 0x33, 0xd0, 0xa0, 0x9a, 0x02, 0x6d, 0x97, 0x5e, 0x05, 0x3b,
	0x02, 0xa0, 0x42, 0xb0, 0xf8, 0x38, 0x17, 0xc8, 0x9d, 0x7a, 0xb7, 0xde, 0x6b, 0x0d, 0xf6, 0xfc,
	0xa5, 0xd9, 0xfa, 0xff, 0x97, 0x82, 0xff, 0x75, 0x19, 0x75, 0x2f, 0x11, 0x6c, 0x1e, 0x2c, 0xd0,
	0x90, 0x6f, 0xe1, 0xca, 0xab, 0xe6, 0x86, 0x93, 0x38, 0x51, 0xa5, 0x5d, 0xb6, 0x3b, 0x5b, 0x65,
	0x83, 0x0f, 0xe3, 0xa4, 0xca, 0x45, 0x67, 0x4e, 0xe3, 0xed, 0xb8, 0xe8, 0x8c, 0xdc, 0x87, 0xad,
	0x42, 0x96, 0x2a, 0xab, 0x0d, 0xc5, 0x74, 0x73, 0x85, 0xe9, 0xc0, 0x80, 0x34, 0xd1, 0xef, 0x92,
	0xa8, 0x55, 0x04, 0xca, 0x9c, 0x96, 0x78, 0xe8, 0xcc, 0xd9, 0x7c, 0x1b, 0x1e, 0x3a, 0xd3, 0x63,
	0xa4, 0xec, 0xe4, 0x34, 0x8c, 0x30, 0x13, 0xa7, 0x4e, 0xb3, 0x6b, 0xf5, 0x1a, 0x72, 0x8c, 0xd2,
	0x77, 0x20, 0x5d, 0x15, 0x85, 0xd8, 0x15, 0x85, 0xb8, 0x5f, 0xc0, 0xd5, 0xca, 0x20, 0xc8, 0x35,
	0xa8, 0x3f, 0xc3, 0xb9, 0x91, 0x84, 0x3c, 0x92, 0x36, 0x34, 0xa6, 0x74, 0x9c, 0x17, 0x0a, 0xd0,
	0xc6, 0x67, 0xb5, 0x3b, 0x96, 0xf7, 0x00, 0xde, 0xb9, 0x1f, 0x27, 0x91, 0x26, 0x2b, 0xb6, 0xe6,
	0x53, 0x68, 0xa8, 0x85, 0x57, 0x14, 0xad, 0xc1, 0xed, 0x4b, 0xa8, 0x21, 0xd0, 0x11, 0x5e, 0x1b,
	0xc8, 0x10, 0xc5, 0x91, 0x96, 0x61, 0x41, 0xe8, 0xed, 0xc2, 0xf5, 0x25, 0x2f, 0xcf, 0xd2, 0x84,
	0x23, 0x71, 0xa1, 0x69, 0x04, 0xcb, 0x1d, 0xab, 0x5b, 0xef, 0xd9, 0x41, 0x69, 0x7b, 0x87, 0xd0,
	0x1e, 0xa2, 0xf8, 0xbe, 0x90, 0x6a, 0x99, 0x9b, 0x03, 0x9b, 0x06, 0x53, 0x2c, 0xb4, 0x31, 0xc9,
	0x7b, 0x60, 0xf3, 0x8c, 0x26, 0xe1, 0xb3, 0x38, 0x89, 0x4c, 0xa1, 0x4d, 0xe9, 0xf8, 0x2e, 0x4e,
	0x22, 0xef, 0x73, 0xb0, 0x4b, 0x2e, 0x42, 0x60, 0x7d, 0x61, 0x69, 0xd4, 0xf9, 0xe2, 0xe8, 0x47,
	0xf0, 0x6e, 0x25, 0x19, 0x53, 0xc1, 0x1d, 0x80, 0x72, 0x9b, 0x74, 0x0d, 0xad, 0x81, 0x53, 0x69,
	0x57, 0x19, 0x16, 0x2c, 0x60, 0xbd, 0x7f, 0x2c, 0xb8, 0x36, 0x0c, 0x1e, 0xee, 0x0f, 0xa9, 0xc0,
	0x33, 0x3a, 0xbf, 0xc7, 0x58, 0xca, 0xc8, 0x21, 0x34, 0x50, 0x1e, 0x4c, 0xe3, 0x3f, 0xa9, 0x30,
	0x55, 0xf1, 0x2b, 0x8e, 0x03, 0x14, 0x34, 0x1e, 0xf3, 0x40, 0xb3, 0xb8, 0xbf, 0x5a, 0x70, 0xe3,
	0x35, 0x10, 0xd9, 0xfb, 0x11, 0xcb, 0x4e, 0xf6, 0xd3, 0x48, 0xf7, 0xa1, 0x11, 0x94, 0xb6, 0xbc,
	0x3b, 0x15, 0x22, 0x53, 0x77, 0x35, 0x7d, 0x57, 0xd8, 0xb2, 0xff, 0x13, 0xe4, 0x9c, 0x8e, 0xf4,
	0x83, 0x67, 0x07, 0x85, 0x49, 0x3a, 0x00, 0x12, 0x75, 0x24, 0xa8, 0xc8, 0xf5, 0x53, 0x66, 0x07,
	0x0b, 0x1e, 0xef, 0x09, 0x90, 0x85, 0x64, 0x7e, 0x60, 0x34, 0xcb, 0x90, 0x91, 0xaf, 0x60, 0x83,
	0x21, 0xcf, 0xc7, 0xc2, 0xd4, 0xdc, 0xf3, 0x97, 0x1e, 0x7c, 0xbd, 0x4a, 0xbe, 0x7e, 0xe7, 0xa7,
	0xbb, 0x5a, 0x7b, 0xfc, 0x80, 0x0a, 0x1a, 0x98, 0xb8, 0xc1, 0xbf, 0x35, 0xd8, 0x52, 0x6a, 0x34,
	0xfa, 0x22, 0x3f, 0x42, 0xb3, 0xf8, 0x0e, 0x90, 0x4e, 0xb5, 0x85, 0xcb, 0x1f, 0x08, 0xf7, 0xd2,
	0x7f, 0xe7, 0xad, 0x7d, 0x6c, 0x91, 0xa7, 0x00, 0xaf, 0xb6, 0x85, 0x74, 0x2b, 0xdc, 0x2b, 0x8b,
	0xf4, 0x86, 0xec, 0x4f, 0xa0, 0xb5, 0xb0, 0x25, 0xe4, 0xd6, 0x6a, 0xea, 0x95, 0xbd, 0x72, 0xbd,
	0x8b, 0x20, 0x5a, 0xa2, 0xde, 0x1a, 0x79, 0x0a, 0xdb, 0x4b, 0xea, 0x25, 0xb7, 0x57, 0xc3, 0x56,
	0x16, 0xcd, 0xfd, 0xe0, 0x62, 0x50, 0xc1, 0x7e, 0xf7, 0xa3, 0x17, 0xe7, 0x1d, 0xeb, 0x8f, 0xf3,
	0x8e, 0xf5, 0xf7, 0x79, 0xc7, 0x82, 0x1b, 0x71, 0x6a, 0xe2, 0x64, 0x95, 0x71, 0x32, 0x32, 0xe1,
	0x3f, 0x6d, 0xe8, 0xdf, 0xe3, 0x0d, 0xd5, 0x83, 0xbd, 0xff, 0x02, 0x00, 0x00, 0xff, 0xff, 0x1f,
	0x1c, 0x84, 0x3f, 0x53, 0x08, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// QueryServiceClient is the client API for QueryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type QueryServiceClient interface {
	// GetTrace returns a single trace.
	// Note that the JSON response over HTTP is wrapped into result envelope "{"result": ...}"
	// It means that the JSON response cannot be directly unmarshalled using JSONPb.
	// This can be fixed by first parsing into user-defined envelope with standard JSON library
	// or string manipulation to remove the envelope. Alternatively generate objects using OpenAPI.
	GetTrace(ctx context.Context, in *GetTraceRequest, opts ...grpc.CallOption) (QueryService_GetTraceClient, error)
	// FindTraces searches for traces.
	// See GetTrace for JSON unmarshalling.
	FindTraces(ctx context.Context, in *FindTracesRequest, opts ...grpc.CallOption) (QueryService_FindTracesClient, error)
	// GetServices returns service names.
	GetServices(ctx context.Context, in *GetServicesRequest, opts ...grpc.CallOption) (*GetServicesResponse, error)
	// GetOperations returns operation names.
	GetOperations(ctx context.Context, in *GetOperationsRequest, opts ...grpc.CallOption) (*GetOperationsResponse, error)
}

type queryServiceClient struct {
	cc *grpc.ClientConn
}

func NewQueryServiceClient(cc *grpc.ClientConn) QueryServiceClient {
	return &queryServiceClient{cc}
}

func (c *queryServiceClient) GetTrace(ctx context.Context, in *GetTraceRequest, opts ...grpc.CallOption) (QueryService_GetTraceClient, error) {
	stream, err := c.cc.NewStream(ctx, &_QueryService_serviceDesc.Streams[0], "/jaeger.api_v3.QueryService/GetTrace", opts...)
	if err != nil {
		return nil, err
	}
	x := &queryServiceGetTraceClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type QueryService_GetTraceClient interface {
	Recv() (*v1.TracesData, error)
	grpc.ClientStream
}

type queryServiceGetTraceClient struct {
	grpc.ClientStream
}

func (x *queryServiceGetTraceClient) Recv() (*v1.TracesData, error) {
	m := new(v1.TracesData)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *queryServiceClient) FindTraces(ctx context.Context, in *FindTracesRequest, opts ...grpc.CallOption) (QueryService_FindTracesClient, error) {
	stream, err := c.cc.NewStream(ctx, &_QueryService_serviceDesc.Streams[1], "/jaeger.api_v3.QueryService/FindTraces", opts...)
	if err != nil {
		return nil, err
	}
	x := &queryServiceFindTracesClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type QueryService_FindTracesClient interface {
	Recv() (*v1.TracesData, error)
	grpc.ClientStream
}

type queryServiceFindTracesClient struct {
	grpc.ClientStream
}

func (x *queryServiceFindTracesClient) Recv() (*v1.TracesData, error) {
	m := new(v1.TracesData)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *queryServiceClient) GetServices(ctx context.Context, in *GetServicesRequest, opts ...grpc.CallOption) (*GetServicesResponse, error) {
	out := new(GetServicesResponse)
	err := c.cc.Invoke(ctx, "/jaeger.api_v3.QueryService/GetServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *queryServiceClient) GetOperations(ctx context.Context, in *GetOperationsRequest, opts ...grpc.CallOption) (*GetOperationsResponse, error) {
	out := new(GetOperationsResponse)
	err := c.cc.Invoke(ctx, "/jaeger.api_v3.QueryService/GetOperations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// QueryServiceServer is the server API for QueryService service.
type QueryServiceServer interface {
	// GetTrace returns a single trace.
	// Note that the JSON response over HTTP is wrapped into result envelope "{"result": ...}"
	// It means that the JSON response cannot be directly unmarshalled using JSONPb.
	// This can be fixed by first parsing into user-defined envelope with standard JSON library
	// or string manipulation to remove the envelope. Alternatively generate objects using OpenAPI.
	GetTrace(*GetTraceRequest, QueryService_GetTraceServer) error
	// FindTraces searches for traces.
	// See GetTrace for JSON unmarshalling.
	FindTraces(*FindTracesRequest, QueryService_FindTracesServer) error
	// GetServices returns service names.
	GetServices(context.Context, *GetServicesRequest) (*GetServicesResponse, error)
	// GetOperations returns operation names.
	GetOperations(context.Context, *GetOperationsRequest) (*GetOperationsResponse, error)
}

// UnimplementedQueryServiceServer can be embedded to have forward compatible implementations.
type UnimplementedQueryServiceServer struct {
}

func (*UnimplementedQueryServiceServer) GetTrace(req *GetTraceRequest, srv QueryService_GetTraceServer) error {
	return status.Errorf(codes.Unimplemented, "method GetTrace not implemented")
}
func (*UnimplementedQueryServiceServer) FindTraces(req *FindTracesRequest, srv QueryService_FindTracesServer) error {
	return status.Errorf(codes.Unimplemented, "method FindTraces not implemented")
}
func (*UnimplementedQueryServiceServer) GetServices(ctx context.Context, req *GetServicesRequest) (*GetServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServices not implemented")
}
func (*UnimplementedQueryServiceServer) GetOperations(ctx context.Context, req *GetOperationsRequest) (*GetOperationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOperations not implemented")
}

func RegisterQueryServiceServer(s *grpc.Server, srv QueryServiceServer) {
	s.RegisterService(&_QueryService_serviceDesc, srv)
}

func _QueryService_GetTrace_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetTraceRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(QueryServiceServer).GetTrace(m, &queryServiceGetTraceServer{stream})
}

type QueryService_GetTraceServer interface {
	Send(*v1.TracesData) error
	grpc.ServerStream
}

type queryServiceGetTraceServer struct {
	grpc.ServerStream
}

func (x *queryServiceGetTraceServer) Send(m *v1.TracesData) error {
	return x.ServerStream.SendMsg(m)
}

func _QueryService_FindTraces_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(FindTracesRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(QueryServiceServer).FindTraces(m, &queryServiceFindTracesServer{stream})
}

type QueryService_FindTracesServer interface {
	Send(*v1.TracesData) error
	grpc.ServerStream
}

type queryServiceFindTracesServer struct {
	grpc.ServerStream
}

func (x *queryServiceFindTracesServer) Send(m *v1.TracesData) error {
	return x.ServerStream.SendMsg(m)
}

func _QueryService_GetServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueryServiceServer).GetServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jaeger.api_v3.QueryService/GetServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueryServiceServer).GetServices(ctx, req.(*GetServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _QueryService_GetOperations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOperationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QueryServiceServer).GetOperations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jaeger.api_v3.QueryService/GetOperations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QueryServiceServer).GetOperations(ctx, req.(*GetOperationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _QueryService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "jaeger.api_v3.QueryService",
	HandlerType: (*QueryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetServices",
			Handler:    _QueryService_GetServices_Handler,
		},
		{
			MethodName: "GetOperations",
			Handler:    _QueryService_GetOperations_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetTrace",
			Handler:       _QueryService_GetTrace_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "FindTraces",
			Handler:       _QueryService_FindTraces_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "query_service.proto",
}

func (m *GetTraceRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTraceRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetTraceRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.RawTraces {
		i--
		if m.RawTraces {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	n1, err1 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.EndTime, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.EndTime):])
	if err1 != nil {
		return 0, err1
	}
	i -= n1
	i = encodeVarintQueryService(dAtA, i, uint64(n1))
	i--
	dAtA[i] = 0x1a
	n2, err2 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.StartTime, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTime):])
	if err2 != nil {
		return 0, err2
	}
	i -= n2
	i = encodeVarintQueryService(dAtA, i, uint64(n2))
	i--
	dAtA[i] = 0x12
	if len(m.TraceId) > 0 {
		i -= len(m.TraceId)
		copy(dAtA[i:], m.TraceId)
		i = encodeVarintQueryService(dAtA, i, uint64(len(m.TraceId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TraceQueryParameters) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TraceQueryParameters) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TraceQueryParameters) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.RawTraces {
		i--
		if m.RawTraces {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x48
	}
	if m.SearchDepth != 0 {
		i = encodeVarintQueryService(dAtA, i, uint64(m.SearchDepth))
		i--
		dAtA[i] = 0x40
	}
	n3, err3 := github_com_gogo_protobuf_types.StdDurationMarshalTo(m.DurationMax, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdDuration(m.DurationMax):])
	if err3 != nil {
		return 0, err3
	}
	i -= n3
	i = encodeVarintQueryService(dAtA, i, uint64(n3))
	i--
	dAtA[i] = 0x3a
	n4, err4 := github_com_gogo_protobuf_types.StdDurationMarshalTo(m.DurationMin, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdDuration(m.DurationMin):])
	if err4 != nil {
		return 0, err4
	}
	i -= n4
	i = encodeVarintQueryService(dAtA, i, uint64(n4))
	i--
	dAtA[i] = 0x32
	n5, err5 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.StartTimeMax, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTimeMax):])
	if err5 != nil {
		return 0, err5
	}
	i -= n5
	i = encodeVarintQueryService(dAtA, i, uint64(n5))
	i--
	dAtA[i] = 0x2a
	n6, err6 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.StartTimeMin, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTimeMin):])
	if err6 != nil {
		return 0, err6
	}
	i -= n6
	i = encodeVarintQueryService(dAtA, i, uint64(n6))
	i--
	dAtA[i] = 0x22
	if len(m.Attributes) > 0 {
		for k := range m.Attributes {
			v := m.Attributes[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintQueryService(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintQueryService(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintQueryService(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.OperationName) > 0 {
		i -= len(m.OperationName)
		copy(dAtA[i:], m.OperationName)
		i = encodeVarintQueryService(dAtA, i, uint64(len(m.OperationName)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.ServiceName) > 0 {
		i -= len(m.ServiceName)
		copy(dAtA[i:], m.ServiceName)
		i = encodeVarintQueryService(dAtA, i, uint64(len(m.ServiceName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *FindTracesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindTracesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FindTracesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Query != nil {
		{
			size, err := m.Query.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintQueryService(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetServicesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetServicesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetServicesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	return len(dAtA) - i, nil
}

func (m *GetServicesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetServicesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetServicesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Services) > 0 {
		for iNdEx := len(m.Services) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Services[iNdEx])
			copy(dAtA[i:], m.Services[iNdEx])
			i = encodeVarintQueryService(dAtA, i, uint64(len(m.Services[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetOperationsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOperationsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetOperationsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.SpanKind) > 0 {
		i -= len(m.SpanKind)
		copy(dAtA[i:], m.SpanKind)
		i = encodeVarintQueryService(dAtA, i, uint64(len(m.SpanKind)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Service) > 0 {
		i -= len(m.Service)
		copy(dAtA[i:], m.Service)
		i = encodeVarintQueryService(dAtA, i, uint64(len(m.Service)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Operation) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Operation) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Operation) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.SpanKind) > 0 {
		i -= len(m.SpanKind)
		copy(dAtA[i:], m.SpanKind)
		i = encodeVarintQueryService(dAtA, i, uint64(len(m.SpanKind)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintQueryService(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetOperationsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOperationsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetOperationsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Operations) > 0 {
		for iNdEx := len(m.Operations) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Operations[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintQueryService(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GRPCGatewayError) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GRPCGatewayError) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GRPCGatewayError) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Error != nil {
		{
			size, err := m.Error.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintQueryService(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GRPCGatewayError_GRPCGatewayErrorDetails) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GRPCGatewayError_GRPCGatewayErrorDetails) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GRPCGatewayError_GRPCGatewayErrorDetails) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.HttpStatus) > 0 {
		i -= len(m.HttpStatus)
		copy(dAtA[i:], m.HttpStatus)
		i = encodeVarintQueryService(dAtA, i, uint64(len(m.HttpStatus)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Message) > 0 {
		i -= len(m.Message)
		copy(dAtA[i:], m.Message)
		i = encodeVarintQueryService(dAtA, i, uint64(len(m.Message)))
		i--
		dAtA[i] = 0x1a
	}
	if m.HttpCode != 0 {
		i = encodeVarintQueryService(dAtA, i, uint64(m.HttpCode))
		i--
		dAtA[i] = 0x10
	}
	if m.GrpcCode != 0 {
		i = encodeVarintQueryService(dAtA, i, uint64(m.GrpcCode))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GRPCGatewayWrapper) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GRPCGatewayWrapper) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GRPCGatewayWrapper) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Result != nil {
		{
			size, err := m.Result.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintQueryService(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintQueryService(dAtA []byte, offset int, v uint64) int {
	offset -= sovQueryService(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *GetTraceRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.TraceId)
	if l > 0 {
		n += 1 + l + sovQueryService(uint64(l))
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTime)
	n += 1 + l + sovQueryService(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.EndTime)
	n += 1 + l + sovQueryService(uint64(l))
	if m.RawTraces {
		n += 2
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *TraceQueryParameters) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ServiceName)
	if l > 0 {
		n += 1 + l + sovQueryService(uint64(l))
	}
	l = len(m.OperationName)
	if l > 0 {
		n += 1 + l + sovQueryService(uint64(l))
	}
	if len(m.Attributes) > 0 {
		for k, v := range m.Attributes {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovQueryService(uint64(len(k))) + 1 + len(v) + sovQueryService(uint64(len(v)))
			n += mapEntrySize + 1 + sovQueryService(uint64(mapEntrySize))
		}
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTimeMin)
	n += 1 + l + sovQueryService(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTimeMax)
	n += 1 + l + sovQueryService(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdDuration(m.DurationMin)
	n += 1 + l + sovQueryService(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdDuration(m.DurationMax)
	n += 1 + l + sovQueryService(uint64(l))
	if m.SearchDepth != 0 {
		n += 1 + sovQueryService(uint64(m.SearchDepth))
	}
	if m.RawTraces {
		n += 2
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *FindTracesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Query != nil {
		l = m.Query.Size()
		n += 1 + l + sovQueryService(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetServicesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetServicesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Services) > 0 {
		for _, s := range m.Services {
			l = len(s)
			n += 1 + l + sovQueryService(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetOperationsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Service)
	if l > 0 {
		n += 1 + l + sovQueryService(uint64(l))
	}
	l = len(m.SpanKind)
	if l > 0 {
		n += 1 + l + sovQueryService(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Operation) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovQueryService(uint64(l))
	}
	l = len(m.SpanKind)
	if l > 0 {
		n += 1 + l + sovQueryService(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetOperationsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Operations) > 0 {
		for _, e := range m.Operations {
			l = e.Size()
			n += 1 + l + sovQueryService(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GRPCGatewayError) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Error != nil {
		l = m.Error.Size()
		n += 1 + l + sovQueryService(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GRPCGatewayError_GRPCGatewayErrorDetails) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.GrpcCode != 0 {
		n += 1 + sovQueryService(uint64(m.GrpcCode))
	}
	if m.HttpCode != 0 {
		n += 1 + sovQueryService(uint64(m.HttpCode))
	}
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovQueryService(uint64(l))
	}
	l = len(m.HttpStatus)
	if l > 0 {
		n += 1 + l + sovQueryService(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GRPCGatewayWrapper) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Result != nil {
		l = m.Result.Size()
		n += 1 + l + sovQueryService(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovQueryService(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozQueryService(x uint64) (n int) {
	return sovQueryService(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *GetTraceRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryService
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetTraceRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetTraceRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TraceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TraceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.StartTime, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.EndTime, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawTraces", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.RawTraces = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipQueryService(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthQueryService
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TraceQueryParameters) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryService
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TraceQueryParameters: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TraceQueryParameters: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ServiceName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ServiceName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OperationName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OperationName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Attributes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Attributes == nil {
				m.Attributes = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowQueryService
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowQueryService
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthQueryService
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthQueryService
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowQueryService
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthQueryService
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthQueryService
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipQueryService(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthQueryService
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Attributes[mapkey] = mapvalue
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTimeMin", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.StartTimeMin, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTimeMax", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.StartTimeMax, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DurationMin", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdDurationUnmarshal(&m.DurationMin, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DurationMax", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdDurationUnmarshal(&m.DurationMax, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SearchDepth", wireType)
			}
			m.SearchDepth = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SearchDepth |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RawTraces", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.RawTraces = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipQueryService(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthQueryService
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindTracesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryService
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FindTracesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FindTracesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Query", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Query == nil {
				m.Query = &TraceQueryParameters{}
			}
			if err := m.Query.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryService(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthQueryService
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetServicesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryService
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetServicesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetServicesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipQueryService(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthQueryService
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetServicesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryService
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetServicesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetServicesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Services", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Services = append(m.Services, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryService(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthQueryService
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOperationsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryService
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetOperationsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetOperationsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Service", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Service = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpanKind", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SpanKind = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryService(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthQueryService
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Operation) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryService
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Operation: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Operation: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpanKind", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SpanKind = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryService(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthQueryService
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOperationsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryService
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetOperationsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetOperationsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Operations", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Operations = append(m.Operations, &Operation{})
			if err := m.Operations[len(m.Operations)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryService(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthQueryService
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GRPCGatewayError) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryService
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GRPCGatewayError: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GRPCGatewayError: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Error", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Error == nil {
				m.Error = &GRPCGatewayError_GRPCGatewayErrorDetails{}
			}
			if err := m.Error.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryService(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthQueryService
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GRPCGatewayError_GRPCGatewayErrorDetails) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryService
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GRPCGatewayErrorDetails: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GRPCGatewayErrorDetails: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field GrpcCode", wireType)
			}
			m.GrpcCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GrpcCode |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HttpCode", wireType)
			}
			m.HttpCode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HttpCode |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HttpStatus", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.HttpStatus = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryService(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthQueryService
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GRPCGatewayWrapper) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowQueryService
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GRPCGatewayWrapper: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GRPCGatewayWrapper: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthQueryService
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthQueryService
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Result == nil {
				m.Result = &v1.TracesData{}
			}
			if err := m.Result.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipQueryService(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthQueryService
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipQueryService(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowQueryService
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowQueryService
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthQueryService
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupQueryService
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthQueryService
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthQueryService        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowQueryService          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupQueryService = fmt.Errorf("proto: unexpected end of group")
)
