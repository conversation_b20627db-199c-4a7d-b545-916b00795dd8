// Copyright (c) 2021 The Jaeger Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Based on: https://github.com/open-telemetry/opentelemetry-proto/blob/v0.8.0/opentelemetry/proto/trace/v1/trace.proto

syntax="proto3";

package jaeger.api_v2.metrics;

import "gogoproto/gogo.proto";

option go_package = "metrics";
option java_package = "io.jaegertracing.api_v2.metrics";

// Enable gogoprotobuf extensions (https://github.com/gogo/protobuf/blob/master/extensions.md).
// Enable custom Marshal method.
option (gogoproto.marshaler_all) = true;
// Enable custom Unmarshal method.
option (gogoproto.unmarshaler_all) = true;
// Enable custom Size method (Required by Marshal and Unmarshal).
option (gogoproto.sizer_all) = true;

// SpanKind is the type of span. Can be used to specify additional relationships between spans
// in addition to a parent/child relationship.
enum SpanKind {
  // Unspecified. Do NOT use as default.
  // Implementations MAY assume SpanKind to be INTERNAL when receiving UNSPECIFIED.
  SPAN_KIND_UNSPECIFIED = 0;

  // Indicates that the span represents an internal operation within an application,
  // as opposed to an operation happening at the boundaries. Default value.
  SPAN_KIND_INTERNAL = 1;

  // Indicates that the span covers server-side handling of an RPC or other
  // remote network request.
  SPAN_KIND_SERVER = 2;

  // Indicates that the span describes a request to some remote service.
  SPAN_KIND_CLIENT = 3;

  // Indicates that the span describes a producer sending a message to a broker.
  // Unlike CLIENT and SERVER, there is often no direct critical path latency relationship
  // between producer and consumer spans. A PRODUCER span ends when the message was accepted
  // by the broker while the logical processing of the message might span a much longer time.
  SPAN_KIND_PRODUCER = 4;

  // Indicates that the span describes consumer receiving a message from a broker.
  // Like the PRODUCER kind, there is often no direct critical path latency relationship
  // between producer and consumer spans.
  SPAN_KIND_CONSUMER = 5;
}
