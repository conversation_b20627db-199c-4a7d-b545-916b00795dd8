// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: dependency_storage.proto

package storage

import (
	context "context"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	_ "github.com/gogo/protobuf/types"
	github_com_gogo_protobuf_types "github.com/gogo/protobuf/types"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	io "io"
	math "math"
	math_bits "math/bits"
	time "time"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf
var _ = time.Kitchen

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type GetDependenciesRequest struct {
	// start_time is the start of the time interval to search for the dependencies.
	StartTime time.Time `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3,stdtime" json:"start_time"`
	// end_time is the end of the time interval to search for the dependencies.
	EndTime              time.Time `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3,stdtime" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetDependenciesRequest) Reset()         { *m = GetDependenciesRequest{} }
func (m *GetDependenciesRequest) String() string { return proto.CompactTextString(m) }
func (*GetDependenciesRequest) ProtoMessage()    {}
func (*GetDependenciesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_17393f3b58692e2b, []int{0}
}
func (m *GetDependenciesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetDependenciesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetDependenciesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetDependenciesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDependenciesRequest.Merge(m, src)
}
func (m *GetDependenciesRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetDependenciesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDependenciesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDependenciesRequest proto.InternalMessageInfo

func (m *GetDependenciesRequest) GetStartTime() time.Time {
	if m != nil {
		return m.StartTime
	}
	return time.Time{}
}

func (m *GetDependenciesRequest) GetEndTime() time.Time {
	if m != nil {
		return m.EndTime
	}
	return time.Time{}
}

// Dependency represents a relationship between two services.
type Dependency struct {
	// parent is the name of the caller service.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// child is the name of the service being called.
	Child string `protobuf:"bytes,2,opt,name=child,proto3" json:"child,omitempty"`
	// call_count is the number of times the parent service called the child service.
	CallCount uint64 `protobuf:"varint,3,opt,name=call_count,json=callCount,proto3" json:"call_count,omitempty"`
	// source contains the origin from where the dependency was extracted.
	Source               string   `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Dependency) Reset()         { *m = Dependency{} }
func (m *Dependency) String() string { return proto.CompactTextString(m) }
func (*Dependency) ProtoMessage()    {}
func (*Dependency) Descriptor() ([]byte, []int) {
	return fileDescriptor_17393f3b58692e2b, []int{1}
}
func (m *Dependency) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Dependency) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Dependency.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Dependency) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Dependency.Merge(m, src)
}
func (m *Dependency) XXX_Size() int {
	return m.Size()
}
func (m *Dependency) XXX_DiscardUnknown() {
	xxx_messageInfo_Dependency.DiscardUnknown(m)
}

var xxx_messageInfo_Dependency proto.InternalMessageInfo

func (m *Dependency) GetParent() string {
	if m != nil {
		return m.Parent
	}
	return ""
}

func (m *Dependency) GetChild() string {
	if m != nil {
		return m.Child
	}
	return ""
}

func (m *Dependency) GetCallCount() uint64 {
	if m != nil {
		return m.CallCount
	}
	return 0
}

func (m *Dependency) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

type GetDependenciesResponse struct {
	Dependencies         []*Dependency `protobuf:"bytes,1,rep,name=dependencies,proto3" json:"dependencies,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetDependenciesResponse) Reset()         { *m = GetDependenciesResponse{} }
func (m *GetDependenciesResponse) String() string { return proto.CompactTextString(m) }
func (*GetDependenciesResponse) ProtoMessage()    {}
func (*GetDependenciesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_17393f3b58692e2b, []int{2}
}
func (m *GetDependenciesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetDependenciesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetDependenciesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetDependenciesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDependenciesResponse.Merge(m, src)
}
func (m *GetDependenciesResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetDependenciesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDependenciesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetDependenciesResponse proto.InternalMessageInfo

func (m *GetDependenciesResponse) GetDependencies() []*Dependency {
	if m != nil {
		return m.Dependencies
	}
	return nil
}

func init() {
	proto.RegisterType((*GetDependenciesRequest)(nil), "jaeger.storage.v2.GetDependenciesRequest")
	proto.RegisterType((*Dependency)(nil), "jaeger.storage.v2.Dependency")
	proto.RegisterType((*GetDependenciesResponse)(nil), "jaeger.storage.v2.GetDependenciesResponse")
}

func init() { proto.RegisterFile("dependency_storage.proto", fileDescriptor_17393f3b58692e2b) }

var fileDescriptor_17393f3b58692e2b = []byte{
	// 349 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x51, 0xcd, 0x4e, 0xc2, 0x40,
	0x10, 0x76, 0x05, 0x81, 0x0e, 0x26, 0xea, 0x06, 0xb1, 0x69, 0xc2, 0x4f, 0x38, 0xa1, 0x87, 0x92,
	0xd4, 0x07, 0x30, 0x82, 0x89, 0xf7, 0xc6, 0x93, 0x31, 0x21, 0xa5, 0x1d, 0x0b, 0xa6, 0x74, 0xcb,
	0xee, 0xd6, 0x84, 0xc4, 0x87, 0xf0, 0x09, 0x7c, 0x1e, 0x8e, 0x3e, 0x81, 0x1a, 0x9e, 0xc4, 0x6c,
	0x97, 0x2a, 0x0a, 0x07, 0xbd, 0xcd, 0x7c, 0x33, 0xdf, 0xf7, 0xcd, 0xee, 0x07, 0x66, 0x80, 0x09,
	0xc6, 0x01, 0xc6, 0xfe, 0x7c, 0x28, 0x24, 0xe3, 0x5e, 0x88, 0x76, 0xc2, 0x99, 0x64, 0xf4, 0xe8,
	0xc1, 0xc3, 0x10, 0xb9, 0x9d, 0xa3, 0x8f, 0x8e, 0xd5, 0x0a, 0x19, 0x0b, 0x23, 0xec, 0x65, 0x0b,
	0xa3, 0xf4, 0xbe, 0x27, 0x27, 0x53, 0x14, 0xd2, 0x9b, 0x26, 0x9a, 0x63, 0xd5, 0x42, 0x16, 0xb2,
	0xac, 0xec, 0xa9, 0x4a, 0xa3, 0x9d, 0x17, 0x02, 0xf5, 0x6b, 0x94, 0x57, 0xb9, 0xd3, 0x04, 0x85,
	0x8b, 0xb3, 0x14, 0x85, 0xa4, 0x03, 0x00, 0x21, 0x3d, 0x2e, 0x87, 0x4a, 0xc9, 0x24, 0x6d, 0xd2,
	0xad, 0x3a, 0x96, 0xad, 0x6d, 0xec, 0xdc, 0xc6, 0xbe, 0xc9, 0x6d, 0xfa, 0x95, 0xc5, 0x5b, 0x6b,
	0xe7, 0xf9, 0xbd, 0x45, 0x5c, 0x23, 0xe3, 0xa9, 0x09, 0xbd, 0x80, 0x0a, 0xc6, 0x81, 0x96, 0xd8,
	0xfd, 0x87, 0x44, 0x19, 0xe3, 0x40, 0xe1, 0x9d, 0x19, 0xc0, 0xd7, 0x71, 0x73, 0x5a, 0x87, 0x52,
	0xe2, 0x71, 0x8c, 0x65, 0x76, 0x8f, 0xe1, 0xae, 0x3a, 0x5a, 0x83, 0x3d, 0x7f, 0x3c, 0x89, 0x82,
	0xcc, 0xc3, 0x70, 0x75, 0x43, 0x1b, 0x00, 0xbe, 0x17, 0x45, 0x43, 0x9f, 0xa5, 0xb1, 0x34, 0x0b,
	0x6d, 0xd2, 0x2d, 0xba, 0x86, 0x42, 0x06, 0x0a, 0x50, 0x62, 0x82, 0xa5, 0xdc, 0x47, 0xb3, 0xa8,
	0xc5, 0x74, 0xd7, 0xb9, 0x83, 0x93, 0x8d, 0x2f, 0x11, 0x09, 0x8b, 0x05, 0xd2, 0x4b, 0xd8, 0x0f,
	0xd6, 0x70, 0x93, 0xb4, 0x0b, 0xdd, 0xaa, 0xd3, 0xb0, 0x37, 0xf2, 0xb0, 0xbf, 0x8f, 0x76, 0x7f,
	0x50, 0x9c, 0x27, 0x38, 0x5c, 0x9b, 0xa1, 0x17, 0x20, 0xa7, 0x63, 0x38, 0xf8, 0xe5, 0x48, 0x4f,
	0xb7, 0x68, 0x6e, 0x0f, 0xca, 0x3a, 0xfb, 0xcb, 0xaa, 0x7e, 0x40, 0xff, 0x78, 0xb1, 0x6c, 0x92,
	0xd7, 0x65, 0x93, 0x7c, 0x2c, 0x9b, 0xe4, 0xb6, 0xbc, 0x62, 0x8c, 0x4a, 0x59, 0x18, 0xe7, 0x9f,
	0x01, 0x00, 0x00, 0xff, 0xff, 0xa9, 0xde, 0x4d, 0xdd, 0x73, 0x02, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// DependencyReaderClient is the client API for DependencyReader service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DependencyReaderClient interface {
	// GetDependencies loads service dependencies from storage.
	GetDependencies(ctx context.Context, in *GetDependenciesRequest, opts ...grpc.CallOption) (*GetDependenciesResponse, error)
}

type dependencyReaderClient struct {
	cc *grpc.ClientConn
}

func NewDependencyReaderClient(cc *grpc.ClientConn) DependencyReaderClient {
	return &dependencyReaderClient{cc}
}

func (c *dependencyReaderClient) GetDependencies(ctx context.Context, in *GetDependenciesRequest, opts ...grpc.CallOption) (*GetDependenciesResponse, error) {
	out := new(GetDependenciesResponse)
	err := c.cc.Invoke(ctx, "/jaeger.storage.v2.DependencyReader/GetDependencies", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DependencyReaderServer is the server API for DependencyReader service.
type DependencyReaderServer interface {
	// GetDependencies loads service dependencies from storage.
	GetDependencies(context.Context, *GetDependenciesRequest) (*GetDependenciesResponse, error)
}

// UnimplementedDependencyReaderServer can be embedded to have forward compatible implementations.
type UnimplementedDependencyReaderServer struct {
}

func (*UnimplementedDependencyReaderServer) GetDependencies(ctx context.Context, req *GetDependenciesRequest) (*GetDependenciesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDependencies not implemented")
}

func RegisterDependencyReaderServer(s *grpc.Server, srv DependencyReaderServer) {
	s.RegisterService(&_DependencyReader_serviceDesc, srv)
}

func _DependencyReader_GetDependencies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDependenciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DependencyReaderServer).GetDependencies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jaeger.storage.v2.DependencyReader/GetDependencies",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DependencyReaderServer).GetDependencies(ctx, req.(*GetDependenciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DependencyReader_serviceDesc = grpc.ServiceDesc{
	ServiceName: "jaeger.storage.v2.DependencyReader",
	HandlerType: (*DependencyReaderServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetDependencies",
			Handler:    _DependencyReader_GetDependencies_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dependency_storage.proto",
}

func (m *GetDependenciesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDependenciesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetDependenciesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	n1, err1 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.EndTime, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.EndTime):])
	if err1 != nil {
		return 0, err1
	}
	i -= n1
	i = encodeVarintDependencyStorage(dAtA, i, uint64(n1))
	i--
	dAtA[i] = 0x12
	n2, err2 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.StartTime, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTime):])
	if err2 != nil {
		return 0, err2
	}
	i -= n2
	i = encodeVarintDependencyStorage(dAtA, i, uint64(n2))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *Dependency) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Dependency) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Dependency) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Source) > 0 {
		i -= len(m.Source)
		copy(dAtA[i:], m.Source)
		i = encodeVarintDependencyStorage(dAtA, i, uint64(len(m.Source)))
		i--
		dAtA[i] = 0x22
	}
	if m.CallCount != 0 {
		i = encodeVarintDependencyStorage(dAtA, i, uint64(m.CallCount))
		i--
		dAtA[i] = 0x18
	}
	if len(m.Child) > 0 {
		i -= len(m.Child)
		copy(dAtA[i:], m.Child)
		i = encodeVarintDependencyStorage(dAtA, i, uint64(len(m.Child)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Parent) > 0 {
		i -= len(m.Parent)
		copy(dAtA[i:], m.Parent)
		i = encodeVarintDependencyStorage(dAtA, i, uint64(len(m.Parent)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetDependenciesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDependenciesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetDependenciesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Dependencies) > 0 {
		for iNdEx := len(m.Dependencies) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Dependencies[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintDependencyStorage(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func encodeVarintDependencyStorage(dAtA []byte, offset int, v uint64) int {
	offset -= sovDependencyStorage(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *GetDependenciesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTime)
	n += 1 + l + sovDependencyStorage(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.EndTime)
	n += 1 + l + sovDependencyStorage(uint64(l))
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Dependency) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Parent)
	if l > 0 {
		n += 1 + l + sovDependencyStorage(uint64(l))
	}
	l = len(m.Child)
	if l > 0 {
		n += 1 + l + sovDependencyStorage(uint64(l))
	}
	if m.CallCount != 0 {
		n += 1 + sovDependencyStorage(uint64(m.CallCount))
	}
	l = len(m.Source)
	if l > 0 {
		n += 1 + l + sovDependencyStorage(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetDependenciesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Dependencies) > 0 {
		for _, e := range m.Dependencies {
			l = e.Size()
			n += 1 + l + sovDependencyStorage(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovDependencyStorage(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozDependencyStorage(x uint64) (n int) {
	return sovDependencyStorage(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *GetDependenciesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDependencyStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetDependenciesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetDependenciesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDependencyStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.StartTime, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDependencyStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.EndTime, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDependencyStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Dependency) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDependencyStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Dependency: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Dependency: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Parent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDependencyStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Parent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Child", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDependencyStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Child = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CallCount", wireType)
			}
			m.CallCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDependencyStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CallCount |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDependencyStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Source = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDependencyStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDependenciesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDependencyStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetDependenciesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetDependenciesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dependencies", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDependencyStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Dependencies = append(m.Dependencies, &Dependency{})
			if err := m.Dependencies[len(m.Dependencies)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipDependencyStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDependencyStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipDependencyStorage(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowDependencyStorage
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDependencyStorage
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDependencyStorage
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthDependencyStorage
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupDependencyStorage
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthDependencyStorage
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthDependencyStorage        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowDependencyStorage          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupDependencyStorage = fmt.Errorf("proto: unexpected end of group")
)
