// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: zipkin.proto

package zipkin_proto3

import (
	context "context"
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// When present, kind clarifies timestamp, duration and remote_endpoint. When
// absent, the span is local or incomplete. Unlike client and server, there
// is no direct critical path latency relationship between producer and
// consumer spans.
type Span_Kind int32

const (
	// Default value interpreted as absent.
	Span_SPAN_KIND_UNSPECIFIED Span_Kind = 0
	// The span represents the client side of an RPC operation, implying the
	// following:
	//
	// timestamp is the moment a request was sent to the server.
	// duration is the delay until a response or an error was received.
	// remote_endpoint is the server.
	Span_CLIENT Span_Kind = 1
	// The span represents the server side of an RPC operation, implying the
	// following:
	//
	// timestamp is the moment a client request was received.
	// duration is the delay until a response was sent or an error.
	// remote_endpoint is the client.
	Span_SERVER Span_Kind = 2
	// The span represents production of a message to a remote broker, implying
	// the following:
	//
	// timestamp is the moment a message was sent to a destination.
	// duration is the delay sending the message, such as batching.
	// remote_endpoint is the broker.
	Span_PRODUCER Span_Kind = 3
	// The span represents consumption of a message from a remote broker, not
	// time spent servicing it. For example, a message processor would be an
	// in-process child span of a consumer. Consumer spans imply the following:
	//
	// timestamp is the moment a message was received from an origin.
	// duration is the delay consuming the message, such as from backlog.
	// remote_endpoint is the broker.
	Span_CONSUMER Span_Kind = 4
)

var Span_Kind_name = map[int32]string{
	0: "SPAN_KIND_UNSPECIFIED",
	1: "CLIENT",
	2: "SERVER",
	3: "PRODUCER",
	4: "CONSUMER",
}

var Span_Kind_value = map[string]int32{
	"SPAN_KIND_UNSPECIFIED": 0,
	"CLIENT":                1,
	"SERVER":                2,
	"PRODUCER":              3,
	"CONSUMER":              4,
}

func (x Span_Kind) String() string {
	return proto.EnumName(Span_Kind_name, int32(x))
}

func (Span_Kind) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ab863b5fa670a281, []int{0, 0}
}

// A span is a single-host view of an operation. A trace is a series of spans
// (often RPC calls) which nest to form a latency tree. Spans are in the same
// trace when they share the same trace ID. The parent_id field establishes the
// position of one span in the tree.
//
// The root span is where parent_id is Absent and usually has the longest
// duration in the trace. However, nested asynchronous work can materialize as
// child spans whose duration exceed the root span.
//
// Spans usually represent remote activity such as RPC calls, or messaging
// producers and consumers. However, they can also represent in-process
// activity in any position of the trace. For example, a root span could
// represent a server receiving an initial client request. A root span could
// also represent a scheduled job that has no remote context.
//
// Encoding notes:
//
// Epoch timestamp are encoded fixed64 as varint would also be 8 bytes, and more
// expensive to encode and size. Duration is stored uint64, as often the numbers
// are quite small.
//
// Default values are ok, as only natural numbers are used. For example, zero is
// an invalid timestamp and an invalid duration, false values for debug or shared
// are ignorable, and zero-length strings also coerce to null.
//
// The next id is 14.
//
// Note fields up to 15 take 1 byte to encode. Take care when adding new fields
// https://developers.google.com/protocol-buffers/docs/proto3#assigning-tags
type Span struct {
	// Randomly generated, unique identifier for a trace, set on all spans within
	// it.
	//
	// This field is required and encoded as 8 or 16 bytes, in big endian byte
	// order.
	TraceId []byte `protobuf:"bytes,1,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	// The parent span ID or absent if this the root span in a trace.
	ParentId []byte `protobuf:"bytes,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	// Unique identifier for this operation within the trace.
	//
	// This field is required and encoded as 8 opaque bytes.
	Id []byte `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	// When present, used to interpret remote_endpoint
	Kind Span_Kind `protobuf:"varint,4,opt,name=kind,proto3,enum=zipkin.proto3.Span_Kind" json:"kind,omitempty"`
	// The logical operation this span represents in lowercase (e.g. rpc method).
	// Leave absent if unknown.
	//
	// As these are lookup labels, take care to ensure names are low cardinality.
	// For example, do not embed variables into the name.
	Name string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	// Epoch microseconds of the start of this span, possibly absent if
	// incomplete.
	//
	// For example, 1502787600000000 corresponds to 2017-08-15 09:00 UTC
	//
	// This value should be set directly by instrumentation, using the most
	// precise value possible. For example, gettimeofday or multiplying epoch
	// millis by 1000.
	//
	// There are three known edge-cases where this could be reported absent.
	// - A span was allocated but never started (ex not yet received a timestamp)
	// - The span's start event was lost
	// - Data about a completed span (ex tags) were sent after the fact
	Timestamp uint64 `protobuf:"fixed64,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// Duration in microseconds of the critical path, if known. Durations of less
	// than one are rounded up. Duration of children can be longer than their
	// parents due to asynchronous operations.
	//
	// For example 150 milliseconds is 150000 microseconds.
	Duration uint64 `protobuf:"varint,7,opt,name=duration,proto3" json:"duration,omitempty"`
	// The host that recorded this span, primarily for query by service name.
	//
	// Instrumentation should always record this. Usually, absent implies late
	// data. The IP address corresponding to this is usually the site local or
	// advertised service address. When present, the port indicates the listen
	// port.
	LocalEndpoint *Endpoint `protobuf:"bytes,8,opt,name=local_endpoint,json=localEndpoint,proto3" json:"local_endpoint,omitempty"`
	// When an RPC (or messaging) span, indicates the other side of the
	// connection.
	//
	// By recording the remote endpoint, your trace will contain network context
	// even if the peer is not tracing. For example, you can record the IP from
	// the "X-Forwarded-For" header or the service name and socket of a remote
	// peer.
	RemoteEndpoint *Endpoint `protobuf:"bytes,9,opt,name=remote_endpoint,json=remoteEndpoint,proto3" json:"remote_endpoint,omitempty"`
	// Associates events that explain latency with the time they happened.
	Annotations []*Annotation `protobuf:"bytes,10,rep,name=annotations,proto3" json:"annotations,omitempty"`
	// Tags give your span context for search, viewing and analysis.
	//
	// For example, a key "your_app.version" would let you lookup traces by
	// version. A tag "sql.query" isn't searchable, but it can help in debugging
	// when viewing a trace.
	Tags map[string]string `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// True is a request to store this span even if it overrides sampling policy.
	//
	// This is true when the "X-B3-Flags" header has a value of 1.
	Debug bool `protobuf:"varint,12,opt,name=debug,proto3" json:"debug,omitempty"`
	// True if we are contributing to a span started by another tracer (ex on a
	// different host).
	Shared               bool     `protobuf:"varint,13,opt,name=shared,proto3" json:"shared,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Span) Reset()         { *m = Span{} }
func (m *Span) String() string { return proto.CompactTextString(m) }
func (*Span) ProtoMessage()    {}
func (*Span) Descriptor() ([]byte, []int) {
	return fileDescriptor_ab863b5fa670a281, []int{0}
}
func (m *Span) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Span.Unmarshal(m, b)
}
func (m *Span) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Span.Marshal(b, m, deterministic)
}
func (m *Span) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Span.Merge(m, src)
}
func (m *Span) XXX_Size() int {
	return xxx_messageInfo_Span.Size(m)
}
func (m *Span) XXX_DiscardUnknown() {
	xxx_messageInfo_Span.DiscardUnknown(m)
}

var xxx_messageInfo_Span proto.InternalMessageInfo

func (m *Span) GetTraceId() []byte {
	if m != nil {
		return m.TraceId
	}
	return nil
}

func (m *Span) GetParentId() []byte {
	if m != nil {
		return m.ParentId
	}
	return nil
}

func (m *Span) GetId() []byte {
	if m != nil {
		return m.Id
	}
	return nil
}

func (m *Span) GetKind() Span_Kind {
	if m != nil {
		return m.Kind
	}
	return Span_SPAN_KIND_UNSPECIFIED
}

func (m *Span) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Span) GetTimestamp() uint64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *Span) GetDuration() uint64 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *Span) GetLocalEndpoint() *Endpoint {
	if m != nil {
		return m.LocalEndpoint
	}
	return nil
}

func (m *Span) GetRemoteEndpoint() *Endpoint {
	if m != nil {
		return m.RemoteEndpoint
	}
	return nil
}

func (m *Span) GetAnnotations() []*Annotation {
	if m != nil {
		return m.Annotations
	}
	return nil
}

func (m *Span) GetTags() map[string]string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *Span) GetDebug() bool {
	if m != nil {
		return m.Debug
	}
	return false
}

func (m *Span) GetShared() bool {
	if m != nil {
		return m.Shared
	}
	return false
}

// The network context of a node in the service graph.
//
// The next id is 5.
type Endpoint struct {
	// Lower-case label of this node in the service graph, such as "favstar".
	// Leave absent if unknown.
	//
	// This is a primary label for trace lookup and aggregation, so it should be
	// intuitive and consistent. Many use a name from service discovery.
	ServiceName string `protobuf:"bytes,1,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	// 4 byte representation of the primary IPv4 address associated with this
	// connection. Absent if unknown.
	Ipv4 []byte `protobuf:"bytes,2,opt,name=ipv4,proto3" json:"ipv4,omitempty"`
	// 16 byte representation of the primary IPv6 address associated with this
	// connection. Absent if unknown.
	//
	// Prefer using the ipv4 field for mapped addresses.
	Ipv6 []byte `protobuf:"bytes,3,opt,name=ipv6,proto3" json:"ipv6,omitempty"`
	// Depending on context, this could be a listen port or the client-side of a
	// socket. Absent if unknown.
	Port                 int32    `protobuf:"varint,4,opt,name=port,proto3" json:"port,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Endpoint) Reset()         { *m = Endpoint{} }
func (m *Endpoint) String() string { return proto.CompactTextString(m) }
func (*Endpoint) ProtoMessage()    {}
func (*Endpoint) Descriptor() ([]byte, []int) {
	return fileDescriptor_ab863b5fa670a281, []int{1}
}
func (m *Endpoint) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Endpoint.Unmarshal(m, b)
}
func (m *Endpoint) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Endpoint.Marshal(b, m, deterministic)
}
func (m *Endpoint) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Endpoint.Merge(m, src)
}
func (m *Endpoint) XXX_Size() int {
	return xxx_messageInfo_Endpoint.Size(m)
}
func (m *Endpoint) XXX_DiscardUnknown() {
	xxx_messageInfo_Endpoint.DiscardUnknown(m)
}

var xxx_messageInfo_Endpoint proto.InternalMessageInfo

func (m *Endpoint) GetServiceName() string {
	if m != nil {
		return m.ServiceName
	}
	return ""
}

func (m *Endpoint) GetIpv4() []byte {
	if m != nil {
		return m.Ipv4
	}
	return nil
}

func (m *Endpoint) GetIpv6() []byte {
	if m != nil {
		return m.Ipv6
	}
	return nil
}

func (m *Endpoint) GetPort() int32 {
	if m != nil {
		return m.Port
	}
	return 0
}

// Associates an event that explains latency with a timestamp.
// Unlike log statements, annotations are often codes. Ex. "ws" for WireSend
//
// The next id is 3.
type Annotation struct {
	// Epoch microseconds of this event.
	//
	// For example, 1502787600000000 corresponds to 2017-08-15 09:00 UTC
	//
	// This value should be set directly by instrumentation, using the most
	// precise value possible. For example, gettimeofday or multiplying epoch
	// millis by 1000.
	Timestamp uint64 `protobuf:"fixed64,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// Usually a short tag indicating an event, like "error"
	//
	// While possible to add larger data, such as garbage collection details, low
	// cardinality event names both keep the size of spans down and also are easy
	// to search against.
	Value                string   `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Annotation) Reset()         { *m = Annotation{} }
func (m *Annotation) String() string { return proto.CompactTextString(m) }
func (*Annotation) ProtoMessage()    {}
func (*Annotation) Descriptor() ([]byte, []int) {
	return fileDescriptor_ab863b5fa670a281, []int{2}
}
func (m *Annotation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Annotation.Unmarshal(m, b)
}
func (m *Annotation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Annotation.Marshal(b, m, deterministic)
}
func (m *Annotation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Annotation.Merge(m, src)
}
func (m *Annotation) XXX_Size() int {
	return xxx_messageInfo_Annotation.Size(m)
}
func (m *Annotation) XXX_DiscardUnknown() {
	xxx_messageInfo_Annotation.DiscardUnknown(m)
}

var xxx_messageInfo_Annotation proto.InternalMessageInfo

func (m *Annotation) GetTimestamp() uint64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *Annotation) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

// A list of spans with possibly different trace ids, in no particular order.
//
// This is used for all transports: POST, Kafka messages etc. No other fields
// are expected, This message facilitates the mechanics of encoding a list, as
// a field number is required. The name of this type is the same in the OpenApi
// aka Swagger specification. https://zipkin.io/zipkin-api/#/default/post_spans
type ListOfSpans struct {
	Spans                []*Span  `protobuf:"bytes,1,rep,name=spans,proto3" json:"spans,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListOfSpans) Reset()         { *m = ListOfSpans{} }
func (m *ListOfSpans) String() string { return proto.CompactTextString(m) }
func (*ListOfSpans) ProtoMessage()    {}
func (*ListOfSpans) Descriptor() ([]byte, []int) {
	return fileDescriptor_ab863b5fa670a281, []int{3}
}
func (m *ListOfSpans) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListOfSpans.Unmarshal(m, b)
}
func (m *ListOfSpans) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListOfSpans.Marshal(b, m, deterministic)
}
func (m *ListOfSpans) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListOfSpans.Merge(m, src)
}
func (m *ListOfSpans) XXX_Size() int {
	return xxx_messageInfo_ListOfSpans.Size(m)
}
func (m *ListOfSpans) XXX_DiscardUnknown() {
	xxx_messageInfo_ListOfSpans.DiscardUnknown(m)
}

var xxx_messageInfo_ListOfSpans proto.InternalMessageInfo

func (m *ListOfSpans) GetSpans() []*Span {
	if m != nil {
		return m.Spans
	}
	return nil
}

// Response for SpanService/Report RPC. This response currently does not return
// any information beyond indicating that the request has finished. That said,
// it may be extended in the future.
type ReportResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportResponse) Reset()         { *m = ReportResponse{} }
func (m *ReportResponse) String() string { return proto.CompactTextString(m) }
func (*ReportResponse) ProtoMessage()    {}
func (*ReportResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_ab863b5fa670a281, []int{4}
}
func (m *ReportResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportResponse.Unmarshal(m, b)
}
func (m *ReportResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportResponse.Marshal(b, m, deterministic)
}
func (m *ReportResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportResponse.Merge(m, src)
}
func (m *ReportResponse) XXX_Size() int {
	return xxx_messageInfo_ReportResponse.Size(m)
}
func (m *ReportResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportResponse proto.InternalMessageInfo

func init() {
	proto.RegisterEnum("zipkin.proto3.Span_Kind", Span_Kind_name, Span_Kind_value)
	proto.RegisterType((*Span)(nil), "zipkin.proto3.Span")
	proto.RegisterMapType((map[string]string)(nil), "zipkin.proto3.Span.TagsEntry")
	proto.RegisterType((*Endpoint)(nil), "zipkin.proto3.Endpoint")
	proto.RegisterType((*Annotation)(nil), "zipkin.proto3.Annotation")
	proto.RegisterType((*ListOfSpans)(nil), "zipkin.proto3.ListOfSpans")
	proto.RegisterType((*ReportResponse)(nil), "zipkin.proto3.ReportResponse")
}

func init() { proto.RegisterFile("zipkin.proto", fileDescriptor_ab863b5fa670a281) }

var fileDescriptor_ab863b5fa670a281 = []byte{
	// 563 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x7c, 0x52, 0xdf, 0x6f, 0xd3, 0x3c,
	0x14, 0x9d, 0xdb, 0x34, 0x4b, 0x6e, 0xba, 0x7c, 0x91, 0x3f, 0x7e, 0x78, 0x05, 0xa4, 0x90, 0xa7,
	0x20, 0xa1, 0x4a, 0x14, 0x04, 0x13, 0x48, 0x68, 0xa3, 0x0b, 0x52, 0xb4, 0x91, 0x55, 0xce, 0xca,
	0x6b, 0xe5, 0x2d, 0x66, 0x58, 0x5b, 0x9d, 0x28, 0xf1, 0x26, 0x8d, 0x3f, 0x9d, 0x27, 0x64, 0x27,
	0x74, 0x5b, 0x55, 0xf1, 0x76, 0xce, 0xf1, 0xc9, 0xb1, 0x73, 0xef, 0x81, 0xe1, 0x2f, 0x51, 0x5d,
	0x0a, 0x39, 0xae, 0xea, 0x52, 0x95, 0x78, 0xe7, 0x3e, 0x7b, 0x1b, 0xfd, 0xb6, 0xc0, 0xca, 0x2b,
	0x26, 0xf1, 0x2e, 0x38, 0xaa, 0x66, 0xe7, 0x7c, 0x21, 0x0a, 0x82, 0x42, 0x14, 0x0f, 0xe9, 0xb6,
	0xe1, 0x69, 0x81, 0x9f, 0x81, 0x5b, 0xb1, 0x9a, 0x4b, 0xa5, 0xcf, 0x7a, 0xe6, 0xcc, 0x69, 0x85,
	0xb4, 0xc0, 0x3e, 0xf4, 0x44, 0x41, 0xfa, 0x46, 0xed, 0x89, 0x02, 0xbf, 0x06, 0xeb, 0x52, 0xc8,
	0x82, 0x58, 0x21, 0x8a, 0xfd, 0x09, 0x19, 0x3f, 0xb8, 0x6e, 0xac, 0xaf, 0x1a, 0x1f, 0x09, 0x59,
	0x50, 0xe3, 0xc2, 0x18, 0x2c, 0xc9, 0x96, 0x9c, 0x0c, 0x42, 0x14, 0xbb, 0xd4, 0x60, 0xfc, 0x1c,
	0x5c, 0x25, 0x96, 0xbc, 0x51, 0x6c, 0x59, 0x11, 0x3b, 0x44, 0xb1, 0x4d, 0xef, 0x04, 0x3c, 0x02,
	0xa7, 0xb8, 0xae, 0x99, 0x12, 0xa5, 0x24, 0xdb, 0x21, 0x8a, 0x2d, 0xba, 0xe2, 0xf8, 0x33, 0xf8,
	0x57, 0xe5, 0x39, 0xbb, 0x5a, 0x70, 0x59, 0x54, 0xa5, 0x90, 0x8a, 0x38, 0x21, 0x8a, 0xbd, 0xc9,
	0xd3, 0xb5, 0x57, 0x24, 0xdd, 0x31, 0xdd, 0x31, 0xf6, 0xbf, 0x14, 0xef, 0xc3, 0x7f, 0x35, 0x5f,
	0x96, 0x8a, 0xdf, 0x05, 0xb8, 0xff, 0x0e, 0xf0, 0x5b, 0xff, 0x2a, 0xe1, 0x13, 0x78, 0x4c, 0xca,
	0x52, 0x99, 0xf7, 0x34, 0x04, 0xc2, 0x7e, 0xec, 0x4d, 0x76, 0xd7, 0xbe, 0x3e, 0x58, 0x39, 0xe8,
	0x7d, 0x37, 0x7e, 0x03, 0x96, 0x62, 0x17, 0x0d, 0xf1, 0xcc, 0x57, 0x2f, 0x36, 0x8d, 0xee, 0x94,
	0x5d, 0x34, 0x89, 0x54, 0xf5, 0x2d, 0x35, 0x56, 0xfc, 0x08, 0x06, 0x05, 0x3f, 0xbb, 0xbe, 0x20,
	0xc3, 0x10, 0xc5, 0x0e, 0x6d, 0x09, 0x7e, 0x02, 0x76, 0xf3, 0x93, 0xd5, 0xbc, 0x20, 0x3b, 0x46,
	0xee, 0xd8, 0xe8, 0x03, 0xb8, 0xab, 0x00, 0x1c, 0x40, 0xff, 0x92, 0xdf, 0x9a, 0x5d, 0xbb, 0x54,
	0x43, 0x1d, 0x76, 0xc3, 0xae, 0xae, 0xb9, 0xd9, 0xb1, 0x4b, 0x5b, 0xf2, 0xb1, 0xb7, 0x87, 0xa2,
	0x39, 0x58, 0x7a, 0x69, 0x78, 0x17, 0x1e, 0xe7, 0xb3, 0x83, 0x6c, 0x71, 0x94, 0x66, 0x87, 0x8b,
	0x79, 0x96, 0xcf, 0x92, 0x69, 0xfa, 0x35, 0x4d, 0x0e, 0x83, 0x2d, 0x0c, 0x60, 0x4f, 0x8f, 0xd3,
	0x24, 0x3b, 0x0d, 0x90, 0xc6, 0x79, 0x42, 0xbf, 0x27, 0x34, 0xe8, 0xe1, 0x21, 0x38, 0x33, 0x7a,
	0x72, 0x38, 0x9f, 0x26, 0x34, 0xe8, 0x6b, 0x36, 0x3d, 0xc9, 0xf2, 0xf9, 0xb7, 0x84, 0x06, 0x56,
	0x24, 0xc0, 0x59, 0x4d, 0xee, 0x25, 0x0c, 0x1b, 0x5e, 0xdf, 0x88, 0x73, 0xbe, 0x30, 0x8d, 0x68,
	0xdf, 0xe5, 0x75, 0x5a, 0xa6, 0x8b, 0x81, 0xc1, 0x12, 0xd5, 0xcd, 0xbb, 0xae, 0x82, 0x06, 0x77,
	0xda, 0xfb, 0xae, 0x80, 0x06, 0x6b, 0xad, 0x2a, 0x6b, 0x65, 0x2a, 0x38, 0xa0, 0x06, 0x47, 0xfb,
	0x00, 0x77, 0x63, 0x7f, 0x58, 0x31, 0xb4, 0x5e, 0xb1, 0x8d, 0x73, 0x88, 0xf6, 0xc0, 0x3b, 0x16,
	0x8d, 0x3a, 0xf9, 0xa1, 0x17, 0xd1, 0xe0, 0x57, 0x30, 0x68, 0x34, 0x20, 0xc8, 0x6c, 0xeb, 0xff,
	0x0d, 0xdb, 0xa2, 0xad, 0x23, 0x0a, 0xc0, 0xa7, 0x5c, 0xbf, 0x82, 0xf2, 0xa6, 0x2a, 0x65, 0xc3,
	0x27, 0xa7, 0xe0, 0x69, 0x43, 0xde, 0xfe, 0x1c, 0x4e, 0xc0, 0x6e, 0x0d, 0x78, 0xb4, 0x16, 0x73,
	0xef, 0xc6, 0xd1, 0x7a, 0x21, 0x1e, 0x66, 0x46, 0x5b, 0x5f, 0x30, 0xf8, 0xad, 0x63, 0xd2, 0x59,
	0x66, 0xe8, 0xcc, 0x6e, 0xd1, 0x9f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xfc, 0x97, 0x2e, 0x7e, 0x05,
	0x04, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SpanServiceClient is the client API for SpanService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SpanServiceClient interface {
	// Report the provided spans to the collector. Analogous to the HTTP POST
	// /api/v2/spans endpoint. Spans are not required to be complete or belonging
	// to the same trace.
	Report(ctx context.Context, in *ListOfSpans, opts ...grpc.CallOption) (*ReportResponse, error)
}

type spanServiceClient struct {
	cc *grpc.ClientConn
}

func NewSpanServiceClient(cc *grpc.ClientConn) SpanServiceClient {
	return &spanServiceClient{cc}
}

func (c *spanServiceClient) Report(ctx context.Context, in *ListOfSpans, opts ...grpc.CallOption) (*ReportResponse, error) {
	out := new(ReportResponse)
	err := c.cc.Invoke(ctx, "/zipkin.proto3.SpanService/Report", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SpanServiceServer is the server API for SpanService service.
type SpanServiceServer interface {
	// Report the provided spans to the collector. Analogous to the HTTP POST
	// /api/v2/spans endpoint. Spans are not required to be complete or belonging
	// to the same trace.
	Report(context.Context, *ListOfSpans) (*ReportResponse, error)
}

// UnimplementedSpanServiceServer can be embedded to have forward compatible implementations.
type UnimplementedSpanServiceServer struct {
}

func (*UnimplementedSpanServiceServer) Report(ctx context.Context, req *ListOfSpans) (*ReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Report not implemented")
}

func RegisterSpanServiceServer(s *grpc.Server, srv SpanServiceServer) {
	s.RegisterService(&_SpanService_serviceDesc, srv)
}

func _SpanService_Report_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOfSpans)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpanServiceServer).Report(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/zipkin.proto3.SpanService/Report",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpanServiceServer).Report(ctx, req.(*ListOfSpans))
	}
	return interceptor(ctx, in, info, handler)
}

var _SpanService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "zipkin.proto3.SpanService",
	HandlerType: (*SpanServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Report",
			Handler:    _SpanService_Report_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "zipkin.proto",
}
