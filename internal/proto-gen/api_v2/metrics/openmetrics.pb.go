// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: openmetrics.proto

// The OpenMetrics protobuf schema which defines the protobuf wire format.
// Ensure to interpret "required" as semantically required for a valid message.
// All string fields MUST be UTF-8 encoded strings.

package metrics

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	types "github.com/gogo/protobuf/types"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

// The type of a Metric.
type MetricType int32

const (
	// Unknown must use unknown MetricPoint values.
	MetricType_UNKNOWN MetricType = 0
	// Gauge must use gauge MetricPoint values.
	MetricType_GAUGE MetricType = 1
	// Counter must use counter MetricPoint values.
	MetricType_COUNTER MetricType = 2
	// State set must use state set MetricPoint values.
	MetricType_STATE_SET MetricType = 3
	// Info must use info MetricPoint values.
	MetricType_INFO MetricType = 4
	// Histogram must use histogram value MetricPoint values.
	MetricType_HISTOGRAM MetricType = 5
	// Gauge histogram must use histogram value MetricPoint values.
	MetricType_GAUGE_HISTOGRAM MetricType = 6
	// Summary quantiles must use summary value MetricPoint values.
	MetricType_SUMMARY MetricType = 7
)

var MetricType_name = map[int32]string{
	0: "UNKNOWN",
	1: "GAUGE",
	2: "COUNTER",
	3: "STATE_SET",
	4: "INFO",
	5: "HISTOGRAM",
	6: "GAUGE_HISTOGRAM",
	7: "SUMMARY",
}

var MetricType_value = map[string]int32{
	"UNKNOWN":         0,
	"GAUGE":           1,
	"COUNTER":         2,
	"STATE_SET":       3,
	"INFO":            4,
	"HISTOGRAM":       5,
	"GAUGE_HISTOGRAM": 6,
	"SUMMARY":         7,
}

func (x MetricType) String() string {
	return proto.EnumName(MetricType_name, int32(x))
}

func (MetricType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{0}
}

// The top-level container type that is encoded and sent over the wire.
type MetricSet struct {
	// Each MetricFamily has one or more MetricPoints for a single Metric.
	MetricFamilies       []*MetricFamily `protobuf:"bytes,1,rep,name=metric_families,json=metricFamilies,proto3" json:"metric_families,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MetricSet) Reset()         { *m = MetricSet{} }
func (m *MetricSet) String() string { return proto.CompactTextString(m) }
func (*MetricSet) ProtoMessage()    {}
func (*MetricSet) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{0}
}
func (m *MetricSet) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MetricSet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MetricSet.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MetricSet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetricSet.Merge(m, src)
}
func (m *MetricSet) XXX_Size() int {
	return m.Size()
}
func (m *MetricSet) XXX_DiscardUnknown() {
	xxx_messageInfo_MetricSet.DiscardUnknown(m)
}

var xxx_messageInfo_MetricSet proto.InternalMessageInfo

func (m *MetricSet) GetMetricFamilies() []*MetricFamily {
	if m != nil {
		return m.MetricFamilies
	}
	return nil
}

// One or more Metrics for a single MetricFamily, where each Metric
// has one or more MetricPoints.
type MetricFamily struct {
	// Required.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Optional.
	Type MetricType `protobuf:"varint,2,opt,name=type,proto3,enum=jaeger.api_v2.metrics.MetricType" json:"type,omitempty"`
	// Optional.
	Unit string `protobuf:"bytes,3,opt,name=unit,proto3" json:"unit,omitempty"`
	// Optional.
	Help string `protobuf:"bytes,4,opt,name=help,proto3" json:"help,omitempty"`
	// Optional.
	Metrics              []*Metric `protobuf:"bytes,5,rep,name=metrics,proto3" json:"metrics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *MetricFamily) Reset()         { *m = MetricFamily{} }
func (m *MetricFamily) String() string { return proto.CompactTextString(m) }
func (*MetricFamily) ProtoMessage()    {}
func (*MetricFamily) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{1}
}
func (m *MetricFamily) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MetricFamily) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MetricFamily.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MetricFamily) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetricFamily.Merge(m, src)
}
func (m *MetricFamily) XXX_Size() int {
	return m.Size()
}
func (m *MetricFamily) XXX_DiscardUnknown() {
	xxx_messageInfo_MetricFamily.DiscardUnknown(m)
}

var xxx_messageInfo_MetricFamily proto.InternalMessageInfo

func (m *MetricFamily) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MetricFamily) GetType() MetricType {
	if m != nil {
		return m.Type
	}
	return MetricType_UNKNOWN
}

func (m *MetricFamily) GetUnit() string {
	if m != nil {
		return m.Unit
	}
	return ""
}

func (m *MetricFamily) GetHelp() string {
	if m != nil {
		return m.Help
	}
	return ""
}

func (m *MetricFamily) GetMetrics() []*Metric {
	if m != nil {
		return m.Metrics
	}
	return nil
}

// A single metric with a unique set of labels within a metric family.
type Metric struct {
	// Optional.
	Labels []*Label `protobuf:"bytes,1,rep,name=labels,proto3" json:"labels,omitempty"`
	// Optional.
	MetricPoints         []*MetricPoint `protobuf:"bytes,2,rep,name=metric_points,json=metricPoints,proto3" json:"metric_points,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *Metric) Reset()         { *m = Metric{} }
func (m *Metric) String() string { return proto.CompactTextString(m) }
func (*Metric) ProtoMessage()    {}
func (*Metric) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{2}
}
func (m *Metric) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Metric) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Metric.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Metric) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Metric.Merge(m, src)
}
func (m *Metric) XXX_Size() int {
	return m.Size()
}
func (m *Metric) XXX_DiscardUnknown() {
	xxx_messageInfo_Metric.DiscardUnknown(m)
}

var xxx_messageInfo_Metric proto.InternalMessageInfo

func (m *Metric) GetLabels() []*Label {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *Metric) GetMetricPoints() []*MetricPoint {
	if m != nil {
		return m.MetricPoints
	}
	return nil
}

// A name-value pair. These are used in multiple places: identifying
// timeseries, value of INFO metrics, and exemplars in Histograms.
type Label struct {
	// Required.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Required.
	Value                string   `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Label) Reset()         { *m = Label{} }
func (m *Label) String() string { return proto.CompactTextString(m) }
func (*Label) ProtoMessage()    {}
func (*Label) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{3}
}
func (m *Label) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Label) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Label.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Label) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Label.Merge(m, src)
}
func (m *Label) XXX_Size() int {
	return m.Size()
}
func (m *Label) XXX_DiscardUnknown() {
	xxx_messageInfo_Label.DiscardUnknown(m)
}

var xxx_messageInfo_Label proto.InternalMessageInfo

func (m *Label) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Label) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

// A MetricPoint in a Metric.
type MetricPoint struct {
	// Required.
	//
	// Types that are valid to be assigned to Value:
	//	*MetricPoint_UnknownValue
	//	*MetricPoint_GaugeValue
	//	*MetricPoint_CounterValue
	//	*MetricPoint_HistogramValue
	//	*MetricPoint_StateSetValue
	//	*MetricPoint_InfoValue
	//	*MetricPoint_SummaryValue
	Value isMetricPoint_Value `protobuf_oneof:"value"`
	// Optional.
	Timestamp            *types.Timestamp `protobuf:"bytes,8,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *MetricPoint) Reset()         { *m = MetricPoint{} }
func (m *MetricPoint) String() string { return proto.CompactTextString(m) }
func (*MetricPoint) ProtoMessage()    {}
func (*MetricPoint) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{4}
}
func (m *MetricPoint) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MetricPoint) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MetricPoint.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MetricPoint) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MetricPoint.Merge(m, src)
}
func (m *MetricPoint) XXX_Size() int {
	return m.Size()
}
func (m *MetricPoint) XXX_DiscardUnknown() {
	xxx_messageInfo_MetricPoint.DiscardUnknown(m)
}

var xxx_messageInfo_MetricPoint proto.InternalMessageInfo

type isMetricPoint_Value interface {
	isMetricPoint_Value()
	MarshalTo([]byte) (int, error)
	Size() int
}

type MetricPoint_UnknownValue struct {
	UnknownValue *UnknownValue `protobuf:"bytes,1,opt,name=unknown_value,json=unknownValue,proto3,oneof" json:"unknown_value,omitempty"`
}
type MetricPoint_GaugeValue struct {
	GaugeValue *GaugeValue `protobuf:"bytes,2,opt,name=gauge_value,json=gaugeValue,proto3,oneof" json:"gauge_value,omitempty"`
}
type MetricPoint_CounterValue struct {
	CounterValue *CounterValue `protobuf:"bytes,3,opt,name=counter_value,json=counterValue,proto3,oneof" json:"counter_value,omitempty"`
}
type MetricPoint_HistogramValue struct {
	HistogramValue *HistogramValue `protobuf:"bytes,4,opt,name=histogram_value,json=histogramValue,proto3,oneof" json:"histogram_value,omitempty"`
}
type MetricPoint_StateSetValue struct {
	StateSetValue *StateSetValue `protobuf:"bytes,5,opt,name=state_set_value,json=stateSetValue,proto3,oneof" json:"state_set_value,omitempty"`
}
type MetricPoint_InfoValue struct {
	InfoValue *InfoValue `protobuf:"bytes,6,opt,name=info_value,json=infoValue,proto3,oneof" json:"info_value,omitempty"`
}
type MetricPoint_SummaryValue struct {
	SummaryValue *SummaryValue `protobuf:"bytes,7,opt,name=summary_value,json=summaryValue,proto3,oneof" json:"summary_value,omitempty"`
}

func (*MetricPoint_UnknownValue) isMetricPoint_Value()   {}
func (*MetricPoint_GaugeValue) isMetricPoint_Value()     {}
func (*MetricPoint_CounterValue) isMetricPoint_Value()   {}
func (*MetricPoint_HistogramValue) isMetricPoint_Value() {}
func (*MetricPoint_StateSetValue) isMetricPoint_Value()  {}
func (*MetricPoint_InfoValue) isMetricPoint_Value()      {}
func (*MetricPoint_SummaryValue) isMetricPoint_Value()   {}

func (m *MetricPoint) GetValue() isMetricPoint_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *MetricPoint) GetUnknownValue() *UnknownValue {
	if x, ok := m.GetValue().(*MetricPoint_UnknownValue); ok {
		return x.UnknownValue
	}
	return nil
}

func (m *MetricPoint) GetGaugeValue() *GaugeValue {
	if x, ok := m.GetValue().(*MetricPoint_GaugeValue); ok {
		return x.GaugeValue
	}
	return nil
}

func (m *MetricPoint) GetCounterValue() *CounterValue {
	if x, ok := m.GetValue().(*MetricPoint_CounterValue); ok {
		return x.CounterValue
	}
	return nil
}

func (m *MetricPoint) GetHistogramValue() *HistogramValue {
	if x, ok := m.GetValue().(*MetricPoint_HistogramValue); ok {
		return x.HistogramValue
	}
	return nil
}

func (m *MetricPoint) GetStateSetValue() *StateSetValue {
	if x, ok := m.GetValue().(*MetricPoint_StateSetValue); ok {
		return x.StateSetValue
	}
	return nil
}

func (m *MetricPoint) GetInfoValue() *InfoValue {
	if x, ok := m.GetValue().(*MetricPoint_InfoValue); ok {
		return x.InfoValue
	}
	return nil
}

func (m *MetricPoint) GetSummaryValue() *SummaryValue {
	if x, ok := m.GetValue().(*MetricPoint_SummaryValue); ok {
		return x.SummaryValue
	}
	return nil
}

func (m *MetricPoint) GetTimestamp() *types.Timestamp {
	if m != nil {
		return m.Timestamp
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*MetricPoint) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*MetricPoint_UnknownValue)(nil),
		(*MetricPoint_GaugeValue)(nil),
		(*MetricPoint_CounterValue)(nil),
		(*MetricPoint_HistogramValue)(nil),
		(*MetricPoint_StateSetValue)(nil),
		(*MetricPoint_InfoValue)(nil),
		(*MetricPoint_SummaryValue)(nil),
	}
}

// Value for UNKNOWN MetricPoint.
type UnknownValue struct {
	// Required.
	//
	// Types that are valid to be assigned to Value:
	//	*UnknownValue_DoubleValue
	//	*UnknownValue_IntValue
	Value                isUnknownValue_Value `protobuf_oneof:"value"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UnknownValue) Reset()         { *m = UnknownValue{} }
func (m *UnknownValue) String() string { return proto.CompactTextString(m) }
func (*UnknownValue) ProtoMessage()    {}
func (*UnknownValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{5}
}
func (m *UnknownValue) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UnknownValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UnknownValue.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UnknownValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnknownValue.Merge(m, src)
}
func (m *UnknownValue) XXX_Size() int {
	return m.Size()
}
func (m *UnknownValue) XXX_DiscardUnknown() {
	xxx_messageInfo_UnknownValue.DiscardUnknown(m)
}

var xxx_messageInfo_UnknownValue proto.InternalMessageInfo

type isUnknownValue_Value interface {
	isUnknownValue_Value()
	MarshalTo([]byte) (int, error)
	Size() int
}

type UnknownValue_DoubleValue struct {
	DoubleValue float64 `protobuf:"fixed64,1,opt,name=double_value,json=doubleValue,proto3,oneof" json:"double_value,omitempty"`
}
type UnknownValue_IntValue struct {
	IntValue int64 `protobuf:"varint,2,opt,name=int_value,json=intValue,proto3,oneof" json:"int_value,omitempty"`
}

func (*UnknownValue_DoubleValue) isUnknownValue_Value() {}
func (*UnknownValue_IntValue) isUnknownValue_Value()    {}

func (m *UnknownValue) GetValue() isUnknownValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *UnknownValue) GetDoubleValue() float64 {
	if x, ok := m.GetValue().(*UnknownValue_DoubleValue); ok {
		return x.DoubleValue
	}
	return 0
}

func (m *UnknownValue) GetIntValue() int64 {
	if x, ok := m.GetValue().(*UnknownValue_IntValue); ok {
		return x.IntValue
	}
	return 0
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*UnknownValue) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*UnknownValue_DoubleValue)(nil),
		(*UnknownValue_IntValue)(nil),
	}
}

// Value for GAUGE MetricPoint.
type GaugeValue struct {
	// Required.
	//
	// Types that are valid to be assigned to Value:
	//	*GaugeValue_DoubleValue
	//	*GaugeValue_IntValue
	Value                isGaugeValue_Value `protobuf_oneof:"value"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GaugeValue) Reset()         { *m = GaugeValue{} }
func (m *GaugeValue) String() string { return proto.CompactTextString(m) }
func (*GaugeValue) ProtoMessage()    {}
func (*GaugeValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{6}
}
func (m *GaugeValue) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GaugeValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GaugeValue.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GaugeValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GaugeValue.Merge(m, src)
}
func (m *GaugeValue) XXX_Size() int {
	return m.Size()
}
func (m *GaugeValue) XXX_DiscardUnknown() {
	xxx_messageInfo_GaugeValue.DiscardUnknown(m)
}

var xxx_messageInfo_GaugeValue proto.InternalMessageInfo

type isGaugeValue_Value interface {
	isGaugeValue_Value()
	MarshalTo([]byte) (int, error)
	Size() int
}

type GaugeValue_DoubleValue struct {
	DoubleValue float64 `protobuf:"fixed64,1,opt,name=double_value,json=doubleValue,proto3,oneof" json:"double_value,omitempty"`
}
type GaugeValue_IntValue struct {
	IntValue int64 `protobuf:"varint,2,opt,name=int_value,json=intValue,proto3,oneof" json:"int_value,omitempty"`
}

func (*GaugeValue_DoubleValue) isGaugeValue_Value() {}
func (*GaugeValue_IntValue) isGaugeValue_Value()    {}

func (m *GaugeValue) GetValue() isGaugeValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *GaugeValue) GetDoubleValue() float64 {
	if x, ok := m.GetValue().(*GaugeValue_DoubleValue); ok {
		return x.DoubleValue
	}
	return 0
}

func (m *GaugeValue) GetIntValue() int64 {
	if x, ok := m.GetValue().(*GaugeValue_IntValue); ok {
		return x.IntValue
	}
	return 0
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*GaugeValue) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*GaugeValue_DoubleValue)(nil),
		(*GaugeValue_IntValue)(nil),
	}
}

// Value for COUNTER MetricPoint.
type CounterValue struct {
	// Required.
	//
	// Types that are valid to be assigned to Total:
	//	*CounterValue_DoubleValue
	//	*CounterValue_IntValue
	Total isCounterValue_Total `protobuf_oneof:"total"`
	// The time values began being collected for this counter.
	// Optional.
	Created *types.Timestamp `protobuf:"bytes,3,opt,name=created,proto3" json:"created,omitempty"`
	// Optional.
	Exemplar             *Exemplar `protobuf:"bytes,4,opt,name=exemplar,proto3" json:"exemplar,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *CounterValue) Reset()         { *m = CounterValue{} }
func (m *CounterValue) String() string { return proto.CompactTextString(m) }
func (*CounterValue) ProtoMessage()    {}
func (*CounterValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{7}
}
func (m *CounterValue) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CounterValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CounterValue.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CounterValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CounterValue.Merge(m, src)
}
func (m *CounterValue) XXX_Size() int {
	return m.Size()
}
func (m *CounterValue) XXX_DiscardUnknown() {
	xxx_messageInfo_CounterValue.DiscardUnknown(m)
}

var xxx_messageInfo_CounterValue proto.InternalMessageInfo

type isCounterValue_Total interface {
	isCounterValue_Total()
	MarshalTo([]byte) (int, error)
	Size() int
}

type CounterValue_DoubleValue struct {
	DoubleValue float64 `protobuf:"fixed64,1,opt,name=double_value,json=doubleValue,proto3,oneof" json:"double_value,omitempty"`
}
type CounterValue_IntValue struct {
	IntValue uint64 `protobuf:"varint,2,opt,name=int_value,json=intValue,proto3,oneof" json:"int_value,omitempty"`
}

func (*CounterValue_DoubleValue) isCounterValue_Total() {}
func (*CounterValue_IntValue) isCounterValue_Total()    {}

func (m *CounterValue) GetTotal() isCounterValue_Total {
	if m != nil {
		return m.Total
	}
	return nil
}

func (m *CounterValue) GetDoubleValue() float64 {
	if x, ok := m.GetTotal().(*CounterValue_DoubleValue); ok {
		return x.DoubleValue
	}
	return 0
}

func (m *CounterValue) GetIntValue() uint64 {
	if x, ok := m.GetTotal().(*CounterValue_IntValue); ok {
		return x.IntValue
	}
	return 0
}

func (m *CounterValue) GetCreated() *types.Timestamp {
	if m != nil {
		return m.Created
	}
	return nil
}

func (m *CounterValue) GetExemplar() *Exemplar {
	if m != nil {
		return m.Exemplar
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*CounterValue) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*CounterValue_DoubleValue)(nil),
		(*CounterValue_IntValue)(nil),
	}
}

// Value for HISTOGRAM or GAUGE_HISTOGRAM MetricPoint.
type HistogramValue struct {
	// Optional.
	//
	// Types that are valid to be assigned to Sum:
	//	*HistogramValue_DoubleValue
	//	*HistogramValue_IntValue
	Sum isHistogramValue_Sum `protobuf_oneof:"sum"`
	// Optional.
	Count uint64 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	// The time values began being collected for this histogram.
	// Optional.
	Created *types.Timestamp `protobuf:"bytes,4,opt,name=created,proto3" json:"created,omitempty"`
	// Optional.
	Buckets              []*HistogramValue_Bucket `protobuf:"bytes,5,rep,name=buckets,proto3" json:"buckets,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *HistogramValue) Reset()         { *m = HistogramValue{} }
func (m *HistogramValue) String() string { return proto.CompactTextString(m) }
func (*HistogramValue) ProtoMessage()    {}
func (*HistogramValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{8}
}
func (m *HistogramValue) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *HistogramValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_HistogramValue.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *HistogramValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HistogramValue.Merge(m, src)
}
func (m *HistogramValue) XXX_Size() int {
	return m.Size()
}
func (m *HistogramValue) XXX_DiscardUnknown() {
	xxx_messageInfo_HistogramValue.DiscardUnknown(m)
}

var xxx_messageInfo_HistogramValue proto.InternalMessageInfo

type isHistogramValue_Sum interface {
	isHistogramValue_Sum()
	MarshalTo([]byte) (int, error)
	Size() int
}

type HistogramValue_DoubleValue struct {
	DoubleValue float64 `protobuf:"fixed64,1,opt,name=double_value,json=doubleValue,proto3,oneof" json:"double_value,omitempty"`
}
type HistogramValue_IntValue struct {
	IntValue int64 `protobuf:"varint,2,opt,name=int_value,json=intValue,proto3,oneof" json:"int_value,omitempty"`
}

func (*HistogramValue_DoubleValue) isHistogramValue_Sum() {}
func (*HistogramValue_IntValue) isHistogramValue_Sum()    {}

func (m *HistogramValue) GetSum() isHistogramValue_Sum {
	if m != nil {
		return m.Sum
	}
	return nil
}

func (m *HistogramValue) GetDoubleValue() float64 {
	if x, ok := m.GetSum().(*HistogramValue_DoubleValue); ok {
		return x.DoubleValue
	}
	return 0
}

func (m *HistogramValue) GetIntValue() int64 {
	if x, ok := m.GetSum().(*HistogramValue_IntValue); ok {
		return x.IntValue
	}
	return 0
}

func (m *HistogramValue) GetCount() uint64 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *HistogramValue) GetCreated() *types.Timestamp {
	if m != nil {
		return m.Created
	}
	return nil
}

func (m *HistogramValue) GetBuckets() []*HistogramValue_Bucket {
	if m != nil {
		return m.Buckets
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*HistogramValue) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*HistogramValue_DoubleValue)(nil),
		(*HistogramValue_IntValue)(nil),
	}
}

// Bucket is the number of values for a bucket in the histogram
// with an optional exemplar.
type HistogramValue_Bucket struct {
	// Required.
	Count uint64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	// Optional.
	UpperBound float64 `protobuf:"fixed64,2,opt,name=upper_bound,json=upperBound,proto3" json:"upper_bound,omitempty"`
	// Optional.
	Exemplar             *Exemplar `protobuf:"bytes,3,opt,name=exemplar,proto3" json:"exemplar,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *HistogramValue_Bucket) Reset()         { *m = HistogramValue_Bucket{} }
func (m *HistogramValue_Bucket) String() string { return proto.CompactTextString(m) }
func (*HistogramValue_Bucket) ProtoMessage()    {}
func (*HistogramValue_Bucket) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{8, 0}
}
func (m *HistogramValue_Bucket) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *HistogramValue_Bucket) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_HistogramValue_Bucket.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *HistogramValue_Bucket) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HistogramValue_Bucket.Merge(m, src)
}
func (m *HistogramValue_Bucket) XXX_Size() int {
	return m.Size()
}
func (m *HistogramValue_Bucket) XXX_DiscardUnknown() {
	xxx_messageInfo_HistogramValue_Bucket.DiscardUnknown(m)
}

var xxx_messageInfo_HistogramValue_Bucket proto.InternalMessageInfo

func (m *HistogramValue_Bucket) GetCount() uint64 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *HistogramValue_Bucket) GetUpperBound() float64 {
	if m != nil {
		return m.UpperBound
	}
	return 0
}

func (m *HistogramValue_Bucket) GetExemplar() *Exemplar {
	if m != nil {
		return m.Exemplar
	}
	return nil
}

type Exemplar struct {
	// Required.
	Value float64 `protobuf:"fixed64,1,opt,name=value,proto3" json:"value,omitempty"`
	// Optional.
	Timestamp *types.Timestamp `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// Labels are additional information about the exemplar value (e.g. trace id).
	// Optional.
	Label                []*Label `protobuf:"bytes,3,rep,name=label,proto3" json:"label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Exemplar) Reset()         { *m = Exemplar{} }
func (m *Exemplar) String() string { return proto.CompactTextString(m) }
func (*Exemplar) ProtoMessage()    {}
func (*Exemplar) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{9}
}
func (m *Exemplar) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Exemplar) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Exemplar.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Exemplar) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Exemplar.Merge(m, src)
}
func (m *Exemplar) XXX_Size() int {
	return m.Size()
}
func (m *Exemplar) XXX_DiscardUnknown() {
	xxx_messageInfo_Exemplar.DiscardUnknown(m)
}

var xxx_messageInfo_Exemplar proto.InternalMessageInfo

func (m *Exemplar) GetValue() float64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *Exemplar) GetTimestamp() *types.Timestamp {
	if m != nil {
		return m.Timestamp
	}
	return nil
}

func (m *Exemplar) GetLabel() []*Label {
	if m != nil {
		return m.Label
	}
	return nil
}

// Value for STATE_SET MetricPoint.
type StateSetValue struct {
	// Optional.
	States               []*StateSetValue_State `protobuf:"bytes,1,rep,name=states,proto3" json:"states,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *StateSetValue) Reset()         { *m = StateSetValue{} }
func (m *StateSetValue) String() string { return proto.CompactTextString(m) }
func (*StateSetValue) ProtoMessage()    {}
func (*StateSetValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{10}
}
func (m *StateSetValue) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StateSetValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StateSetValue.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StateSetValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StateSetValue.Merge(m, src)
}
func (m *StateSetValue) XXX_Size() int {
	return m.Size()
}
func (m *StateSetValue) XXX_DiscardUnknown() {
	xxx_messageInfo_StateSetValue.DiscardUnknown(m)
}

var xxx_messageInfo_StateSetValue proto.InternalMessageInfo

func (m *StateSetValue) GetStates() []*StateSetValue_State {
	if m != nil {
		return m.States
	}
	return nil
}

type StateSetValue_State struct {
	// Required.
	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// Required.
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StateSetValue_State) Reset()         { *m = StateSetValue_State{} }
func (m *StateSetValue_State) String() string { return proto.CompactTextString(m) }
func (*StateSetValue_State) ProtoMessage()    {}
func (*StateSetValue_State) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{10, 0}
}
func (m *StateSetValue_State) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StateSetValue_State) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StateSetValue_State.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StateSetValue_State) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StateSetValue_State.Merge(m, src)
}
func (m *StateSetValue_State) XXX_Size() int {
	return m.Size()
}
func (m *StateSetValue_State) XXX_DiscardUnknown() {
	xxx_messageInfo_StateSetValue_State.DiscardUnknown(m)
}

var xxx_messageInfo_StateSetValue_State proto.InternalMessageInfo

func (m *StateSetValue_State) GetEnabled() bool {
	if m != nil {
		return m.Enabled
	}
	return false
}

func (m *StateSetValue_State) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// Value for INFO MetricPoint.
type InfoValue struct {
	// Optional.
	Info                 []*Label `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InfoValue) Reset()         { *m = InfoValue{} }
func (m *InfoValue) String() string { return proto.CompactTextString(m) }
func (*InfoValue) ProtoMessage()    {}
func (*InfoValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{11}
}
func (m *InfoValue) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *InfoValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_InfoValue.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *InfoValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InfoValue.Merge(m, src)
}
func (m *InfoValue) XXX_Size() int {
	return m.Size()
}
func (m *InfoValue) XXX_DiscardUnknown() {
	xxx_messageInfo_InfoValue.DiscardUnknown(m)
}

var xxx_messageInfo_InfoValue proto.InternalMessageInfo

func (m *InfoValue) GetInfo() []*Label {
	if m != nil {
		return m.Info
	}
	return nil
}

// Value for SUMMARY MetricPoint.
type SummaryValue struct {
	// Optional.
	//
	// Types that are valid to be assigned to Sum:
	//	*SummaryValue_DoubleValue
	//	*SummaryValue_IntValue
	Sum isSummaryValue_Sum `protobuf_oneof:"sum"`
	// Optional.
	Count uint64 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	// The time sum and count values began being collected for this summary.
	// Optional.
	Created *types.Timestamp `protobuf:"bytes,4,opt,name=created,proto3" json:"created,omitempty"`
	// Optional.
	Quantile             []*SummaryValue_Quantile `protobuf:"bytes,5,rep,name=quantile,proto3" json:"quantile,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *SummaryValue) Reset()         { *m = SummaryValue{} }
func (m *SummaryValue) String() string { return proto.CompactTextString(m) }
func (*SummaryValue) ProtoMessage()    {}
func (*SummaryValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{12}
}
func (m *SummaryValue) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SummaryValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SummaryValue.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SummaryValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SummaryValue.Merge(m, src)
}
func (m *SummaryValue) XXX_Size() int {
	return m.Size()
}
func (m *SummaryValue) XXX_DiscardUnknown() {
	xxx_messageInfo_SummaryValue.DiscardUnknown(m)
}

var xxx_messageInfo_SummaryValue proto.InternalMessageInfo

type isSummaryValue_Sum interface {
	isSummaryValue_Sum()
	MarshalTo([]byte) (int, error)
	Size() int
}

type SummaryValue_DoubleValue struct {
	DoubleValue float64 `protobuf:"fixed64,1,opt,name=double_value,json=doubleValue,proto3,oneof" json:"double_value,omitempty"`
}
type SummaryValue_IntValue struct {
	IntValue int64 `protobuf:"varint,2,opt,name=int_value,json=intValue,proto3,oneof" json:"int_value,omitempty"`
}

func (*SummaryValue_DoubleValue) isSummaryValue_Sum() {}
func (*SummaryValue_IntValue) isSummaryValue_Sum()    {}

func (m *SummaryValue) GetSum() isSummaryValue_Sum {
	if m != nil {
		return m.Sum
	}
	return nil
}

func (m *SummaryValue) GetDoubleValue() float64 {
	if x, ok := m.GetSum().(*SummaryValue_DoubleValue); ok {
		return x.DoubleValue
	}
	return 0
}

func (m *SummaryValue) GetIntValue() int64 {
	if x, ok := m.GetSum().(*SummaryValue_IntValue); ok {
		return x.IntValue
	}
	return 0
}

func (m *SummaryValue) GetCount() uint64 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *SummaryValue) GetCreated() *types.Timestamp {
	if m != nil {
		return m.Created
	}
	return nil
}

func (m *SummaryValue) GetQuantile() []*SummaryValue_Quantile {
	if m != nil {
		return m.Quantile
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*SummaryValue) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*SummaryValue_DoubleValue)(nil),
		(*SummaryValue_IntValue)(nil),
	}
}

type SummaryValue_Quantile struct {
	// Required.
	Quantile float64 `protobuf:"fixed64,1,opt,name=quantile,proto3" json:"quantile,omitempty"`
	// Required.
	Value                float64  `protobuf:"fixed64,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SummaryValue_Quantile) Reset()         { *m = SummaryValue_Quantile{} }
func (m *SummaryValue_Quantile) String() string { return proto.CompactTextString(m) }
func (*SummaryValue_Quantile) ProtoMessage()    {}
func (*SummaryValue_Quantile) Descriptor() ([]byte, []int) {
	return fileDescriptor_0b803df83757ec01, []int{12, 0}
}
func (m *SummaryValue_Quantile) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SummaryValue_Quantile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SummaryValue_Quantile.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SummaryValue_Quantile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SummaryValue_Quantile.Merge(m, src)
}
func (m *SummaryValue_Quantile) XXX_Size() int {
	return m.Size()
}
func (m *SummaryValue_Quantile) XXX_DiscardUnknown() {
	xxx_messageInfo_SummaryValue_Quantile.DiscardUnknown(m)
}

var xxx_messageInfo_SummaryValue_Quantile proto.InternalMessageInfo

func (m *SummaryValue_Quantile) GetQuantile() float64 {
	if m != nil {
		return m.Quantile
	}
	return 0
}

func (m *SummaryValue_Quantile) GetValue() float64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func init() {
	proto.RegisterEnum("jaeger.api_v2.metrics.MetricType", MetricType_name, MetricType_value)
	proto.RegisterType((*MetricSet)(nil), "jaeger.api_v2.metrics.MetricSet")
	proto.RegisterType((*MetricFamily)(nil), "jaeger.api_v2.metrics.MetricFamily")
	proto.RegisterType((*Metric)(nil), "jaeger.api_v2.metrics.Metric")
	proto.RegisterType((*Label)(nil), "jaeger.api_v2.metrics.Label")
	proto.RegisterType((*MetricPoint)(nil), "jaeger.api_v2.metrics.MetricPoint")
	proto.RegisterType((*UnknownValue)(nil), "jaeger.api_v2.metrics.UnknownValue")
	proto.RegisterType((*GaugeValue)(nil), "jaeger.api_v2.metrics.GaugeValue")
	proto.RegisterType((*CounterValue)(nil), "jaeger.api_v2.metrics.CounterValue")
	proto.RegisterType((*HistogramValue)(nil), "jaeger.api_v2.metrics.HistogramValue")
	proto.RegisterType((*HistogramValue_Bucket)(nil), "jaeger.api_v2.metrics.HistogramValue.Bucket")
	proto.RegisterType((*Exemplar)(nil), "jaeger.api_v2.metrics.Exemplar")
	proto.RegisterType((*StateSetValue)(nil), "jaeger.api_v2.metrics.StateSetValue")
	proto.RegisterType((*StateSetValue_State)(nil), "jaeger.api_v2.metrics.StateSetValue.State")
	proto.RegisterType((*InfoValue)(nil), "jaeger.api_v2.metrics.InfoValue")
	proto.RegisterType((*SummaryValue)(nil), "jaeger.api_v2.metrics.SummaryValue")
	proto.RegisterType((*SummaryValue_Quantile)(nil), "jaeger.api_v2.metrics.SummaryValue.Quantile")
}

func init() { proto.RegisterFile("openmetrics.proto", fileDescriptor_0b803df83757ec01) }

var fileDescriptor_0b803df83757ec01 = []byte{
	// 981 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x96, 0xdd, 0x6e, 0xe3, 0x44,
	0x14, 0xc7, 0xeb, 0xc4, 0xf9, 0x3a, 0x49, 0xda, 0x30, 0x2c, 0x92, 0x15, 0xb1, 0x6d, 0xf1, 0x82,
	0x54, 0xad, 0x90, 0x0b, 0x65, 0x17, 0x90, 0x80, 0x8b, 0x64, 0x49, 0x93, 0xc2, 0x36, 0x5d, 0x26,
	0x09, 0xa8, 0x70, 0x11, 0x39, 0xe9, 0xd4, 0x35, 0xeb, 0x2f, 0xec, 0xf1, 0x42, 0x05, 0xf7, 0x48,
	0x5c, 0xf0, 0x26, 0xbc, 0x00, 0x4f, 0xc0, 0x15, 0xe2, 0x0d, 0x40, 0xbd, 0xe7, 0x1d, 0xd0, 0x7c,
	0xc5, 0x0e, 0xda, 0x84, 0x2c, 0xda, 0x8b, 0xbd, 0x9b, 0x73, 0xfc, 0x3f, 0xbf, 0x99, 0x73, 0x7c,
	0x7c, 0xc6, 0xf0, 0x52, 0x18, 0x91, 0xc0, 0x27, 0x34, 0x76, 0xe7, 0x89, 0x15, 0xc5, 0x21, 0x0d,
	0xd1, 0x2b, 0x5f, 0xdb, 0xc4, 0x21, 0xb1, 0x65, 0x47, 0xee, 0xf4, 0xc9, 0x91, 0x25, 0x1f, 0xb6,
	0xf7, 0x9c, 0x30, 0x74, 0x3c, 0x72, 0xc8, 0x45, 0xb3, 0xf4, 0xf2, 0x90, 0xba, 0x3e, 0x49, 0xa8,
	0xed, 0x47, 0x22, 0xae, 0x7d, 0xcb, 0x09, 0x9d, 0x90, 0x2f, 0x0f, 0xd9, 0x4a, 0x78, 0xcd, 0x73,
	0xa8, 0x9d, 0x72, 0xc2, 0x88, 0x50, 0xf4, 0x10, 0x76, 0x04, 0x6e, 0x7a, 0x69, 0xfb, 0xae, 0xe7,
	0x92, 0xc4, 0xd0, 0xf6, 0x8b, 0x07, 0xf5, 0xa3, 0x3b, 0xd6, 0x53, 0x37, 0xb5, 0x44, 0xe8, 0x31,
	0x13, 0x5f, 0xe3, 0x6d, 0x3f, 0xb3, 0x5c, 0x92, 0x98, 0xbf, 0x6a, 0xd0, 0xc8, 0x0b, 0x10, 0x02,
	0x3d, 0xb0, 0x7d, 0x62, 0x68, 0xfb, 0xda, 0x41, 0x0d, 0xf3, 0x35, 0xba, 0x0f, 0x3a, 0xbd, 0x8e,
	0x88, 0x51, 0xd8, 0xd7, 0x0e, 0xb6, 0x8f, 0x5e, 0x5b, 0xbb, 0xcf, 0xf8, 0x3a, 0x22, 0x98, 0xcb,
	0x19, 0x2a, 0x0d, 0x5c, 0x6a, 0x14, 0x05, 0x8a, 0xad, 0x99, 0xef, 0x8a, 0x78, 0x91, 0xa1, 0x0b,
	0x1f, 0x5b, 0xa3, 0xf7, 0xa0, 0x22, 0x19, 0x46, 0x89, 0x67, 0x72, 0x7b, 0xed, 0x0e, 0x58, 0xa9,
	0xcd, 0x1f, 0x35, 0x28, 0x0b, 0x1f, 0xba, 0x07, 0x65, 0xcf, 0x9e, 0x11, 0x4f, 0x15, 0xe3, 0xd5,
	0x15, 0x88, 0x87, 0x4c, 0x84, 0xa5, 0x16, 0xf5, 0xa1, 0x29, 0x6b, 0x19, 0x85, 0x6e, 0x40, 0x13,
	0xa3, 0xc0, 0x83, 0xcd, 0xb5, 0xfb, 0x3f, 0x62, 0x52, 0xdc, 0xf0, 0x33, 0x23, 0x31, 0xdf, 0x86,
	0x12, 0x27, 0x3f, 0xb5, 0x7c, 0xb7, 0xa0, 0xf4, 0xc4, 0xf6, 0x52, 0x51, 0xbf, 0x1a, 0x16, 0x86,
	0xf9, 0xa7, 0x0e, 0xf5, 0x1c, 0x10, 0x7d, 0x02, 0xcd, 0x34, 0x78, 0x1c, 0x84, 0xdf, 0x06, 0x53,
	0xa1, 0x66, 0x88, 0xd5, 0x6f, 0x75, 0x22, 0xb4, 0x9f, 0x33, 0xe9, 0x60, 0x0b, 0x37, 0xd2, 0x9c,
	0x8d, 0x3e, 0x86, 0xba, 0x63, 0xa7, 0x0e, 0x99, 0x66, 0xfb, 0xd6, 0x57, 0xbe, 0xb7, 0x3e, 0x53,
	0x2a, 0x0e, 0x38, 0x0b, 0x8b, 0x9d, 0x68, 0x1e, 0xa6, 0x01, 0x25, 0xb1, 0xe4, 0x14, 0xd7, 0x9e,
	0xe8, 0x81, 0xd0, 0x2e, 0x4e, 0x34, 0xcf, 0xd9, 0xe8, 0x11, 0xec, 0x5c, 0xb9, 0x09, 0x0d, 0x9d,
	0xd8, 0xf6, 0x25, 0x4d, 0xe7, 0xb4, 0x37, 0x56, 0xd0, 0x06, 0x4a, 0xad, 0x78, 0xdb, 0x57, 0x4b,
	0x1e, 0x34, 0x84, 0x9d, 0x84, 0xda, 0x94, 0x4c, 0x13, 0x42, 0x25, 0xb1, 0xc4, 0x89, 0xaf, 0xaf,
	0x20, 0x8e, 0x98, 0x7a, 0x44, 0xa8, 0x02, 0x36, 0x93, 0xbc, 0x03, 0x75, 0x00, 0xdc, 0xe0, 0x32,
	0x94, 0xa8, 0x32, 0x47, 0xed, 0xaf, 0x40, 0x9d, 0x04, 0x97, 0xa1, 0xc2, 0xd4, 0x5c, 0x65, 0xb0,
	0x82, 0x25, 0xa9, 0xef, 0xdb, 0xf1, 0xb5, 0xa4, 0x54, 0xd6, 0x16, 0x6c, 0x24, 0xb4, 0x8b, 0x82,
	0x25, 0x39, 0x1b, 0xbd, 0x0f, 0xb5, 0xc5, 0x70, 0x30, 0xaa, 0x9c, 0xd3, 0xb6, 0xc4, 0xf8, 0xb0,
	0xd4, 0xf8, 0xb0, 0xc6, 0x4a, 0x81, 0x33, 0x71, 0xb7, 0x22, 0xdb, 0xcd, 0xfc, 0x0a, 0x1a, 0xf9,
	0x2e, 0x41, 0x77, 0xa0, 0x71, 0x11, 0xa6, 0x33, 0x8f, 0xe4, 0x1a, 0x4c, 0x1b, 0x6c, 0xe1, 0xba,
	0xf0, 0x0a, 0xd1, 0x6d, 0xa8, 0xb9, 0x01, 0xcd, 0x35, 0x4e, 0x71, 0xb0, 0x85, 0xab, 0x6e, 0x20,
	0xaa, 0x94, 0xc1, 0xcf, 0x01, 0xb2, 0xc6, 0x79, 0xbe, 0xe8, 0xdf, 0x35, 0x68, 0xe4, 0x9b, 0xe9,
	0x7f, 0xd2, 0xf5, 0x3c, 0x1d, 0xdd, 0x83, 0xca, 0x3c, 0x26, 0x36, 0x25, 0x17, 0xb2, 0x8d, 0xd7,
	0x55, 0x53, 0x49, 0xd1, 0x07, 0x50, 0x25, 0xdf, 0x11, 0x3f, 0xf2, 0xec, 0x58, 0xf6, 0xeb, 0xde,
	0x8a, 0x97, 0xd9, 0x93, 0x32, 0xbc, 0x08, 0x60, 0x09, 0xd1, 0x90, 0xda, 0x9e, 0xf9, 0x77, 0x01,
	0xb6, 0x97, 0xfb, 0xf9, 0x79, 0x14, 0x8c, 0xcd, 0x15, 0xfe, 0x8d, 0xf1, 0x84, 0x74, 0x2c, 0x8c,
	0x7c, 0xa2, 0xfa, 0xe6, 0x89, 0x1e, 0x43, 0x65, 0x96, 0xce, 0x1f, 0x13, 0xaa, 0x66, 0xf0, 0x9b,
	0x1b, 0x7d, 0x97, 0x56, 0x97, 0x07, 0x61, 0x15, 0xdc, 0xfe, 0x01, 0xca, 0xc2, 0x95, 0x9d, 0x4e,
	0xcb, 0x9f, 0x6e, 0x0f, 0xea, 0x69, 0x14, 0x91, 0x78, 0x3a, 0x0b, 0xd3, 0xe0, 0x82, 0x27, 0xa5,
	0x61, 0xe0, 0xae, 0x2e, 0xf3, 0x2c, 0x55, 0xbc, 0xf8, 0xac, 0x15, 0x2f, 0x41, 0x31, 0x49, 0x7d,
	0xf3, 0x67, 0x0d, 0xaa, 0xea, 0x69, 0x36, 0x7d, 0x79, 0x89, 0xe5, 0xf4, 0x5d, 0xfe, 0xbc, 0x0a,
	0xcf, 0xf0, 0x79, 0xa1, 0x23, 0x28, 0xf1, 0xdb, 0xc3, 0x28, 0x6e, 0x70, 0xd1, 0x08, 0xa9, 0xf9,
	0x93, 0x06, 0xcd, 0xa5, 0xf1, 0x83, 0xba, 0x50, 0xe6, 0xe3, 0x47, 0xdd, 0x57, 0x77, 0x37, 0x19,
	0x5a, 0xc2, 0xc2, 0x32, 0xb2, 0x7d, 0x1f, 0x4a, 0xdc, 0x81, 0x0c, 0xa8, 0x90, 0xc0, 0x9e, 0x79,
	0xe4, 0x82, 0x27, 0x59, 0xc5, 0xca, 0x5c, 0x5c, 0x47, 0x85, 0xec, 0x3a, 0x32, 0x3f, 0x82, 0xda,
	0x62, 0x7e, 0xa1, 0xb7, 0x40, 0x67, 0xf3, 0x6b, 0xa3, 0x5b, 0x93, 0x2b, 0xcd, 0x5f, 0x0a, 0xd0,
	0xc8, 0x4f, 0xae, 0x17, 0xae, 0x95, 0x07, 0x50, 0xfd, 0x26, 0xb5, 0x03, 0xea, 0x7a, 0xe4, 0x3f,
	0x7a, 0x39, 0x9f, 0x86, 0xf5, 0x99, 0x8c, 0xc1, 0x8b, 0xe8, 0xf6, 0x87, 0x50, 0x55, 0x5e, 0xd4,
	0xce, 0x51, 0x45, 0x27, 0x2d, 0xec, 0xe5, 0x0b, 0x5e, 0xb5, 0x98, 0x6c, 0xc6, 0xbb, 0xdf, 0x03,
	0x64, 0x7f, 0x46, 0xa8, 0x0e, 0x95, 0xc9, 0xf0, 0xd3, 0xe1, 0xd9, 0x17, 0xc3, 0xd6, 0x16, 0xaa,
	0x41, 0xa9, 0xdf, 0x99, 0xf4, 0x7b, 0x2d, 0x8d, 0xf9, 0x1f, 0x9c, 0x4d, 0x86, 0xe3, 0x1e, 0x6e,
	0x15, 0x50, 0x13, 0x6a, 0xa3, 0x71, 0x67, 0xdc, 0x9b, 0x8e, 0x7a, 0xe3, 0x56, 0x11, 0x55, 0x41,
	0x3f, 0x19, 0x1e, 0x9f, 0xb5, 0x74, 0xf6, 0x60, 0x70, 0x32, 0x1a, 0x9f, 0xf5, 0x71, 0xe7, 0xb4,
	0x55, 0x42, 0x2f, 0xc3, 0x0e, 0x8f, 0x9f, 0x66, 0xce, 0x32, 0x23, 0x8d, 0x26, 0xa7, 0xa7, 0x1d,
	0x7c, 0xde, 0xaa, 0x74, 0xdf, 0xfd, 0xed, 0x66, 0x57, 0xfb, 0xe3, 0x66, 0x57, 0xfb, 0xeb, 0x66,
	0x57, 0x83, 0x3d, 0x37, 0x94, 0x95, 0xa0, 0xb1, 0x3d, 0x77, 0x03, 0xe7, 0x5f, 0x05, 0xf9, 0x52,
	0xfd, 0x59, 0xcd, 0xca, 0xbc, 0xc0, 0xef, 0xfc, 0x13, 0x00, 0x00, 0xff, 0xff, 0x0a, 0x75, 0xe1,
	0xe3, 0xdb, 0x0a, 0x00, 0x00,
}

func (m *MetricSet) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MetricSet) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricSet) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.MetricFamilies) > 0 {
		for iNdEx := len(m.MetricFamilies) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.MetricFamilies[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *MetricFamily) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MetricFamily) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricFamily) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Metrics) > 0 {
		for iNdEx := len(m.Metrics) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Metrics[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.Help) > 0 {
		i -= len(m.Help)
		copy(dAtA[i:], m.Help)
		i = encodeVarintOpenmetrics(dAtA, i, uint64(len(m.Help)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.Unit) > 0 {
		i -= len(m.Unit)
		copy(dAtA[i:], m.Unit)
		i = encodeVarintOpenmetrics(dAtA, i, uint64(len(m.Unit)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Type != 0 {
		i = encodeVarintOpenmetrics(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintOpenmetrics(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Metric) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Metric) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Metric) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.MetricPoints) > 0 {
		for iNdEx := len(m.MetricPoints) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.MetricPoints[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Labels) > 0 {
		for iNdEx := len(m.Labels) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Labels[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *Label) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Label) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Label) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintOpenmetrics(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintOpenmetrics(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *MetricPoint) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MetricPoint) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricPoint) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Timestamp != nil {
		{
			size, err := m.Timestamp.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x42
	}
	if m.Value != nil {
		{
			size := m.Value.Size()
			i -= size
			if _, err := m.Value.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *MetricPoint_UnknownValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricPoint_UnknownValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.UnknownValue != nil {
		{
			size, err := m.UnknownValue.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *MetricPoint_GaugeValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricPoint_GaugeValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.GaugeValue != nil {
		{
			size, err := m.GaugeValue.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *MetricPoint_CounterValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricPoint_CounterValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.CounterValue != nil {
		{
			size, err := m.CounterValue.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	return len(dAtA) - i, nil
}
func (m *MetricPoint_HistogramValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricPoint_HistogramValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.HistogramValue != nil {
		{
			size, err := m.HistogramValue.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	return len(dAtA) - i, nil
}
func (m *MetricPoint_StateSetValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricPoint_StateSetValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.StateSetValue != nil {
		{
			size, err := m.StateSetValue.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	return len(dAtA) - i, nil
}
func (m *MetricPoint_InfoValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricPoint_InfoValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.InfoValue != nil {
		{
			size, err := m.InfoValue.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	return len(dAtA) - i, nil
}
func (m *MetricPoint_SummaryValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MetricPoint_SummaryValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.SummaryValue != nil {
		{
			size, err := m.SummaryValue.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	return len(dAtA) - i, nil
}
func (m *UnknownValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnknownValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UnknownValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Value != nil {
		{
			size := m.Value.Size()
			i -= size
			if _, err := m.Value.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *UnknownValue_DoubleValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UnknownValue_DoubleValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	i -= 8
	encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.DoubleValue))))
	i--
	dAtA[i] = 0x9
	return len(dAtA) - i, nil
}
func (m *UnknownValue_IntValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UnknownValue_IntValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	i = encodeVarintOpenmetrics(dAtA, i, uint64(m.IntValue))
	i--
	dAtA[i] = 0x10
	return len(dAtA) - i, nil
}
func (m *GaugeValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GaugeValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GaugeValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Value != nil {
		{
			size := m.Value.Size()
			i -= size
			if _, err := m.Value.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *GaugeValue_DoubleValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GaugeValue_DoubleValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	i -= 8
	encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.DoubleValue))))
	i--
	dAtA[i] = 0x9
	return len(dAtA) - i, nil
}
func (m *GaugeValue_IntValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GaugeValue_IntValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	i = encodeVarintOpenmetrics(dAtA, i, uint64(m.IntValue))
	i--
	dAtA[i] = 0x10
	return len(dAtA) - i, nil
}
func (m *CounterValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CounterValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CounterValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Exemplar != nil {
		{
			size, err := m.Exemplar.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.Created != nil {
		{
			size, err := m.Created.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.Total != nil {
		{
			size := m.Total.Size()
			i -= size
			if _, err := m.Total.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *CounterValue_DoubleValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CounterValue_DoubleValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	i -= 8
	encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.DoubleValue))))
	i--
	dAtA[i] = 0x9
	return len(dAtA) - i, nil
}
func (m *CounterValue_IntValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CounterValue_IntValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	i = encodeVarintOpenmetrics(dAtA, i, uint64(m.IntValue))
	i--
	dAtA[i] = 0x10
	return len(dAtA) - i, nil
}
func (m *HistogramValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HistogramValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *HistogramValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Buckets) > 0 {
		for iNdEx := len(m.Buckets) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Buckets[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.Created != nil {
		{
			size, err := m.Created.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.Count != 0 {
		i = encodeVarintOpenmetrics(dAtA, i, uint64(m.Count))
		i--
		dAtA[i] = 0x18
	}
	if m.Sum != nil {
		{
			size := m.Sum.Size()
			i -= size
			if _, err := m.Sum.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *HistogramValue_DoubleValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *HistogramValue_DoubleValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	i -= 8
	encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.DoubleValue))))
	i--
	dAtA[i] = 0x9
	return len(dAtA) - i, nil
}
func (m *HistogramValue_IntValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *HistogramValue_IntValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	i = encodeVarintOpenmetrics(dAtA, i, uint64(m.IntValue))
	i--
	dAtA[i] = 0x10
	return len(dAtA) - i, nil
}
func (m *HistogramValue_Bucket) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HistogramValue_Bucket) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *HistogramValue_Bucket) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Exemplar != nil {
		{
			size, err := m.Exemplar.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.UpperBound != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.UpperBound))))
		i--
		dAtA[i] = 0x11
	}
	if m.Count != 0 {
		i = encodeVarintOpenmetrics(dAtA, i, uint64(m.Count))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Exemplar) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Exemplar) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Exemplar) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Label) > 0 {
		for iNdEx := len(m.Label) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Label[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.Timestamp != nil {
		{
			size, err := m.Timestamp.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if m.Value != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Value))))
		i--
		dAtA[i] = 0x9
	}
	return len(dAtA) - i, nil
}

func (m *StateSetValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StateSetValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StateSetValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.States) > 0 {
		for iNdEx := len(m.States) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.States[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *StateSetValue_State) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StateSetValue_State) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StateSetValue_State) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintOpenmetrics(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x12
	}
	if m.Enabled {
		i--
		if m.Enabled {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *InfoValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InfoValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *InfoValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Info) > 0 {
		for iNdEx := len(m.Info) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Info[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *SummaryValue) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SummaryValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SummaryValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Quantile) > 0 {
		for iNdEx := len(m.Quantile) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Quantile[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.Created != nil {
		{
			size, err := m.Created.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintOpenmetrics(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	if m.Count != 0 {
		i = encodeVarintOpenmetrics(dAtA, i, uint64(m.Count))
		i--
		dAtA[i] = 0x18
	}
	if m.Sum != nil {
		{
			size := m.Sum.Size()
			i -= size
			if _, err := m.Sum.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *SummaryValue_DoubleValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SummaryValue_DoubleValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	i -= 8
	encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.DoubleValue))))
	i--
	dAtA[i] = 0x9
	return len(dAtA) - i, nil
}
func (m *SummaryValue_IntValue) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SummaryValue_IntValue) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	i = encodeVarintOpenmetrics(dAtA, i, uint64(m.IntValue))
	i--
	dAtA[i] = 0x10
	return len(dAtA) - i, nil
}
func (m *SummaryValue_Quantile) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SummaryValue_Quantile) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SummaryValue_Quantile) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Value != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Value))))
		i--
		dAtA[i] = 0x11
	}
	if m.Quantile != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Quantile))))
		i--
		dAtA[i] = 0x9
	}
	return len(dAtA) - i, nil
}

func encodeVarintOpenmetrics(dAtA []byte, offset int, v uint64) int {
	offset -= sovOpenmetrics(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *MetricSet) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.MetricFamilies) > 0 {
		for _, e := range m.MetricFamilies {
			l = e.Size()
			n += 1 + l + sovOpenmetrics(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MetricFamily) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	if m.Type != 0 {
		n += 1 + sovOpenmetrics(uint64(m.Type))
	}
	l = len(m.Unit)
	if l > 0 {
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	l = len(m.Help)
	if l > 0 {
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	if len(m.Metrics) > 0 {
		for _, e := range m.Metrics {
			l = e.Size()
			n += 1 + l + sovOpenmetrics(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Metric) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Labels) > 0 {
		for _, e := range m.Labels {
			l = e.Size()
			n += 1 + l + sovOpenmetrics(uint64(l))
		}
	}
	if len(m.MetricPoints) > 0 {
		for _, e := range m.MetricPoints {
			l = e.Size()
			n += 1 + l + sovOpenmetrics(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Label) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MetricPoint) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Value != nil {
		n += m.Value.Size()
	}
	if m.Timestamp != nil {
		l = m.Timestamp.Size()
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MetricPoint_UnknownValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.UnknownValue != nil {
		l = m.UnknownValue.Size()
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	return n
}
func (m *MetricPoint_GaugeValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.GaugeValue != nil {
		l = m.GaugeValue.Size()
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	return n
}
func (m *MetricPoint_CounterValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CounterValue != nil {
		l = m.CounterValue.Size()
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	return n
}
func (m *MetricPoint_HistogramValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.HistogramValue != nil {
		l = m.HistogramValue.Size()
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	return n
}
func (m *MetricPoint_StateSetValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StateSetValue != nil {
		l = m.StateSetValue.Size()
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	return n
}
func (m *MetricPoint_InfoValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.InfoValue != nil {
		l = m.InfoValue.Size()
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	return n
}
func (m *MetricPoint_SummaryValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.SummaryValue != nil {
		l = m.SummaryValue.Size()
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	return n
}
func (m *UnknownValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Value != nil {
		n += m.Value.Size()
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *UnknownValue_DoubleValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 9
	return n
}
func (m *UnknownValue_IntValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 1 + sovOpenmetrics(uint64(m.IntValue))
	return n
}
func (m *GaugeValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Value != nil {
		n += m.Value.Size()
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GaugeValue_DoubleValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 9
	return n
}
func (m *GaugeValue_IntValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 1 + sovOpenmetrics(uint64(m.IntValue))
	return n
}
func (m *CounterValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Total != nil {
		n += m.Total.Size()
	}
	if m.Created != nil {
		l = m.Created.Size()
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	if m.Exemplar != nil {
		l = m.Exemplar.Size()
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CounterValue_DoubleValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 9
	return n
}
func (m *CounterValue_IntValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 1 + sovOpenmetrics(uint64(m.IntValue))
	return n
}
func (m *HistogramValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Sum != nil {
		n += m.Sum.Size()
	}
	if m.Count != 0 {
		n += 1 + sovOpenmetrics(uint64(m.Count))
	}
	if m.Created != nil {
		l = m.Created.Size()
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	if len(m.Buckets) > 0 {
		for _, e := range m.Buckets {
			l = e.Size()
			n += 1 + l + sovOpenmetrics(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *HistogramValue_DoubleValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 9
	return n
}
func (m *HistogramValue_IntValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 1 + sovOpenmetrics(uint64(m.IntValue))
	return n
}
func (m *HistogramValue_Bucket) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Count != 0 {
		n += 1 + sovOpenmetrics(uint64(m.Count))
	}
	if m.UpperBound != 0 {
		n += 9
	}
	if m.Exemplar != nil {
		l = m.Exemplar.Size()
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Exemplar) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Value != 0 {
		n += 9
	}
	if m.Timestamp != nil {
		l = m.Timestamp.Size()
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	if len(m.Label) > 0 {
		for _, e := range m.Label {
			l = e.Size()
			n += 1 + l + sovOpenmetrics(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StateSetValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.States) > 0 {
		for _, e := range m.States {
			l = e.Size()
			n += 1 + l + sovOpenmetrics(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StateSetValue_State) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Enabled {
		n += 2
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *InfoValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Info) > 0 {
		for _, e := range m.Info {
			l = e.Size()
			n += 1 + l + sovOpenmetrics(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SummaryValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Sum != nil {
		n += m.Sum.Size()
	}
	if m.Count != 0 {
		n += 1 + sovOpenmetrics(uint64(m.Count))
	}
	if m.Created != nil {
		l = m.Created.Size()
		n += 1 + l + sovOpenmetrics(uint64(l))
	}
	if len(m.Quantile) > 0 {
		for _, e := range m.Quantile {
			l = e.Size()
			n += 1 + l + sovOpenmetrics(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SummaryValue_DoubleValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 9
	return n
}
func (m *SummaryValue_IntValue) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 1 + sovOpenmetrics(uint64(m.IntValue))
	return n
}
func (m *SummaryValue_Quantile) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Quantile != 0 {
		n += 9
	}
	if m.Value != 0 {
		n += 9
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovOpenmetrics(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozOpenmetrics(x uint64) (n int) {
	return sovOpenmetrics(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *MetricSet) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MetricSet: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MetricSet: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MetricFamilies", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MetricFamilies = append(m.MetricFamilies, &MetricFamily{})
			if err := m.MetricFamilies[len(m.MetricFamilies)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MetricFamily) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MetricFamily: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MetricFamily: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= MetricType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Unit", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Unit = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Help", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Help = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Metrics", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Metrics = append(m.Metrics, &Metric{})
			if err := m.Metrics[len(m.Metrics)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Metric) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Metric: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Metric: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Labels = append(m.Labels, &Label{})
			if err := m.Labels[len(m.Labels)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field MetricPoints", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.MetricPoints = append(m.MetricPoints, &MetricPoint{})
			if err := m.MetricPoints[len(m.MetricPoints)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Label) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Label: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Label: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MetricPoint) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MetricPoint: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MetricPoint: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UnknownValue", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &UnknownValue{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Value = &MetricPoint_UnknownValue{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GaugeValue", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &GaugeValue{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Value = &MetricPoint_GaugeValue{v}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CounterValue", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &CounterValue{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Value = &MetricPoint_CounterValue{v}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field HistogramValue", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &HistogramValue{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Value = &MetricPoint_HistogramValue{v}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StateSetValue", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &StateSetValue{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Value = &MetricPoint_StateSetValue{v}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field InfoValue", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &InfoValue{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Value = &MetricPoint_InfoValue{v}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SummaryValue", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &SummaryValue{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Value = &MetricPoint_SummaryValue{v}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Timestamp == nil {
				m.Timestamp = &types.Timestamp{}
			}
			if err := m.Timestamp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnknownValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UnknownValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UnknownValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field DoubleValue", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Value = &UnknownValue_DoubleValue{float64(math.Float64frombits(v))}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IntValue", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Value = &UnknownValue_IntValue{v}
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GaugeValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GaugeValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GaugeValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field DoubleValue", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Value = &GaugeValue_DoubleValue{float64(math.Float64frombits(v))}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IntValue", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Value = &GaugeValue_IntValue{v}
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CounterValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CounterValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CounterValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field DoubleValue", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Total = &CounterValue_DoubleValue{float64(math.Float64frombits(v))}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IntValue", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Total = &CounterValue_IntValue{v}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Created", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Created == nil {
				m.Created = &types.Timestamp{}
			}
			if err := m.Created.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Exemplar", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Exemplar == nil {
				m.Exemplar = &Exemplar{}
			}
			if err := m.Exemplar.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *HistogramValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: HistogramValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: HistogramValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field DoubleValue", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Sum = &HistogramValue_DoubleValue{float64(math.Float64frombits(v))}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IntValue", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Sum = &HistogramValue_IntValue{v}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Created", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Created == nil {
				m.Created = &types.Timestamp{}
			}
			if err := m.Created.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Buckets", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Buckets = append(m.Buckets, &HistogramValue_Bucket{})
			if err := m.Buckets[len(m.Buckets)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *HistogramValue_Bucket) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Bucket: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Bucket: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpperBound", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.UpperBound = float64(math.Float64frombits(v))
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Exemplar", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Exemplar == nil {
				m.Exemplar = &Exemplar{}
			}
			if err := m.Exemplar.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Exemplar) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Exemplar: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Exemplar: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Value = float64(math.Float64frombits(v))
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Timestamp == nil {
				m.Timestamp = &types.Timestamp{}
			}
			if err := m.Timestamp.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Label", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Label = append(m.Label, &Label{})
			if err := m.Label[len(m.Label)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StateSetValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StateSetValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StateSetValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field States", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.States = append(m.States, &StateSetValue_State{})
			if err := m.States[len(m.States)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StateSetValue_State) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: State: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: State: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Enabled", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Enabled = bool(v != 0)
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *InfoValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: InfoValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: InfoValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Info = append(m.Info, &Label{})
			if err := m.Info[len(m.Info)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SummaryValue) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SummaryValue: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SummaryValue: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field DoubleValue", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Sum = &SummaryValue_DoubleValue{float64(math.Float64frombits(v))}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IntValue", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Sum = &SummaryValue_IntValue{v}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Created", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Created == nil {
				m.Created = &types.Timestamp{}
			}
			if err := m.Created.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Quantile", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Quantile = append(m.Quantile, &SummaryValue_Quantile{})
			if err := m.Quantile[len(m.Quantile)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SummaryValue_Quantile) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Quantile: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Quantile: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Quantile", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Quantile = float64(math.Float64frombits(v))
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Value = float64(math.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipOpenmetrics(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthOpenmetrics
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipOpenmetrics(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowOpenmetrics
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowOpenmetrics
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthOpenmetrics
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupOpenmetrics
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthOpenmetrics
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthOpenmetrics        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowOpenmetrics          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupOpenmetrics = fmt.Errorf("proto: unexpected end of group")
)
