// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: storage.proto

package storage_v1

import (
	context "context"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	_ "github.com/gogo/protobuf/types"
	github_com_gogo_protobuf_types "github.com/gogo/protobuf/types"
	github_com_jaegertracing_jaeger_idl_model_v1 "github.com/jaegertracing/jaeger-idl/model/v1"
	v1 "github.com/jaegertracing/jaeger-idl/model/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	io "io"
	math "math"
	math_bits "math/bits"
	time "time"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf
var _ = time.Kitchen

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type GetDependenciesRequest struct {
	StartTime            time.Time `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3,stdtime" json:"start_time"`
	EndTime              time.Time `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3,stdtime" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetDependenciesRequest) Reset()         { *m = GetDependenciesRequest{} }
func (m *GetDependenciesRequest) String() string { return proto.CompactTextString(m) }
func (*GetDependenciesRequest) ProtoMessage()    {}
func (*GetDependenciesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{0}
}
func (m *GetDependenciesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetDependenciesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetDependenciesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetDependenciesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDependenciesRequest.Merge(m, src)
}
func (m *GetDependenciesRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetDependenciesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDependenciesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDependenciesRequest proto.InternalMessageInfo

func (m *GetDependenciesRequest) GetStartTime() time.Time {
	if m != nil {
		return m.StartTime
	}
	return time.Time{}
}

func (m *GetDependenciesRequest) GetEndTime() time.Time {
	if m != nil {
		return m.EndTime
	}
	return time.Time{}
}

type GetDependenciesResponse struct {
	Dependencies         []v1.DependencyLink `protobuf:"bytes,1,rep,name=dependencies,proto3" json:"dependencies"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetDependenciesResponse) Reset()         { *m = GetDependenciesResponse{} }
func (m *GetDependenciesResponse) String() string { return proto.CompactTextString(m) }
func (*GetDependenciesResponse) ProtoMessage()    {}
func (*GetDependenciesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{1}
}
func (m *GetDependenciesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetDependenciesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetDependenciesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetDependenciesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDependenciesResponse.Merge(m, src)
}
func (m *GetDependenciesResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetDependenciesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDependenciesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetDependenciesResponse proto.InternalMessageInfo

func (m *GetDependenciesResponse) GetDependencies() []v1.DependencyLink {
	if m != nil {
		return m.Dependencies
	}
	return nil
}

type WriteSpanRequest struct {
	Span                 *v1.Span `protobuf:"bytes,1,opt,name=span,proto3" json:"span,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WriteSpanRequest) Reset()         { *m = WriteSpanRequest{} }
func (m *WriteSpanRequest) String() string { return proto.CompactTextString(m) }
func (*WriteSpanRequest) ProtoMessage()    {}
func (*WriteSpanRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{2}
}
func (m *WriteSpanRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WriteSpanRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WriteSpanRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WriteSpanRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteSpanRequest.Merge(m, src)
}
func (m *WriteSpanRequest) XXX_Size() int {
	return m.Size()
}
func (m *WriteSpanRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteSpanRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WriteSpanRequest proto.InternalMessageInfo

func (m *WriteSpanRequest) GetSpan() *v1.Span {
	if m != nil {
		return m.Span
	}
	return nil
}

// empty; extensible in the future
type WriteSpanResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WriteSpanResponse) Reset()         { *m = WriteSpanResponse{} }
func (m *WriteSpanResponse) String() string { return proto.CompactTextString(m) }
func (*WriteSpanResponse) ProtoMessage()    {}
func (*WriteSpanResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{3}
}
func (m *WriteSpanResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *WriteSpanResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_WriteSpanResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *WriteSpanResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteSpanResponse.Merge(m, src)
}
func (m *WriteSpanResponse) XXX_Size() int {
	return m.Size()
}
func (m *WriteSpanResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteSpanResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WriteSpanResponse proto.InternalMessageInfo

// empty; extensible in the future
type CloseWriterRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CloseWriterRequest) Reset()         { *m = CloseWriterRequest{} }
func (m *CloseWriterRequest) String() string { return proto.CompactTextString(m) }
func (*CloseWriterRequest) ProtoMessage()    {}
func (*CloseWriterRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{4}
}
func (m *CloseWriterRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CloseWriterRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CloseWriterRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CloseWriterRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseWriterRequest.Merge(m, src)
}
func (m *CloseWriterRequest) XXX_Size() int {
	return m.Size()
}
func (m *CloseWriterRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseWriterRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CloseWriterRequest proto.InternalMessageInfo

// empty; extensible in the future
type CloseWriterResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CloseWriterResponse) Reset()         { *m = CloseWriterResponse{} }
func (m *CloseWriterResponse) String() string { return proto.CompactTextString(m) }
func (*CloseWriterResponse) ProtoMessage()    {}
func (*CloseWriterResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{5}
}
func (m *CloseWriterResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CloseWriterResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CloseWriterResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CloseWriterResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseWriterResponse.Merge(m, src)
}
func (m *CloseWriterResponse) XXX_Size() int {
	return m.Size()
}
func (m *CloseWriterResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseWriterResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CloseWriterResponse proto.InternalMessageInfo

type GetTraceRequest struct {
	TraceID              github_com_jaegertracing_jaeger_idl_model_v1.TraceID `protobuf:"bytes,1,opt,name=trace_id,json=traceId,proto3,customtype=github.com/jaegertracing/jaeger-idl/model/v1.TraceID" json:"trace_id"`
	StartTime            time.Time                                            `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3,stdtime" json:"start_time"`
	EndTime              time.Time                                            `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3,stdtime" json:"end_time"`
	XXX_NoUnkeyedLiteral struct{}                                             `json:"-"`
	XXX_unrecognized     []byte                                               `json:"-"`
	XXX_sizecache        int32                                                `json:"-"`
}

func (m *GetTraceRequest) Reset()         { *m = GetTraceRequest{} }
func (m *GetTraceRequest) String() string { return proto.CompactTextString(m) }
func (*GetTraceRequest) ProtoMessage()    {}
func (*GetTraceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{6}
}
func (m *GetTraceRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetTraceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetTraceRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetTraceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTraceRequest.Merge(m, src)
}
func (m *GetTraceRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetTraceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTraceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTraceRequest proto.InternalMessageInfo

func (m *GetTraceRequest) GetStartTime() time.Time {
	if m != nil {
		return m.StartTime
	}
	return time.Time{}
}

func (m *GetTraceRequest) GetEndTime() time.Time {
	if m != nil {
		return m.EndTime
	}
	return time.Time{}
}

type GetServicesRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetServicesRequest) Reset()         { *m = GetServicesRequest{} }
func (m *GetServicesRequest) String() string { return proto.CompactTextString(m) }
func (*GetServicesRequest) ProtoMessage()    {}
func (*GetServicesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{7}
}
func (m *GetServicesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetServicesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetServicesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetServicesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetServicesRequest.Merge(m, src)
}
func (m *GetServicesRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetServicesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetServicesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetServicesRequest proto.InternalMessageInfo

type GetServicesResponse struct {
	Services             []string `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetServicesResponse) Reset()         { *m = GetServicesResponse{} }
func (m *GetServicesResponse) String() string { return proto.CompactTextString(m) }
func (*GetServicesResponse) ProtoMessage()    {}
func (*GetServicesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{8}
}
func (m *GetServicesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetServicesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetServicesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetServicesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetServicesResponse.Merge(m, src)
}
func (m *GetServicesResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetServicesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetServicesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetServicesResponse proto.InternalMessageInfo

func (m *GetServicesResponse) GetServices() []string {
	if m != nil {
		return m.Services
	}
	return nil
}

type GetOperationsRequest struct {
	Service              string   `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	SpanKind             string   `protobuf:"bytes,2,opt,name=span_kind,json=spanKind,proto3" json:"span_kind,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOperationsRequest) Reset()         { *m = GetOperationsRequest{} }
func (m *GetOperationsRequest) String() string { return proto.CompactTextString(m) }
func (*GetOperationsRequest) ProtoMessage()    {}
func (*GetOperationsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{9}
}
func (m *GetOperationsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetOperationsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetOperationsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetOperationsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperationsRequest.Merge(m, src)
}
func (m *GetOperationsRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetOperationsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperationsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperationsRequest proto.InternalMessageInfo

func (m *GetOperationsRequest) GetService() string {
	if m != nil {
		return m.Service
	}
	return ""
}

func (m *GetOperationsRequest) GetSpanKind() string {
	if m != nil {
		return m.SpanKind
	}
	return ""
}

type Operation struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	SpanKind             string   `protobuf:"bytes,2,opt,name=span_kind,json=spanKind,proto3" json:"span_kind,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Operation) Reset()         { *m = Operation{} }
func (m *Operation) String() string { return proto.CompactTextString(m) }
func (*Operation) ProtoMessage()    {}
func (*Operation) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{10}
}
func (m *Operation) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Operation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Operation.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Operation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Operation.Merge(m, src)
}
func (m *Operation) XXX_Size() int {
	return m.Size()
}
func (m *Operation) XXX_DiscardUnknown() {
	xxx_messageInfo_Operation.DiscardUnknown(m)
}

var xxx_messageInfo_Operation proto.InternalMessageInfo

func (m *Operation) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Operation) GetSpanKind() string {
	if m != nil {
		return m.SpanKind
	}
	return ""
}

type GetOperationsResponse struct {
	OperationNames       []string     `protobuf:"bytes,1,rep,name=operationNames,proto3" json:"operationNames,omitempty"`
	Operations           []*Operation `protobuf:"bytes,2,rep,name=operations,proto3" json:"operations,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetOperationsResponse) Reset()         { *m = GetOperationsResponse{} }
func (m *GetOperationsResponse) String() string { return proto.CompactTextString(m) }
func (*GetOperationsResponse) ProtoMessage()    {}
func (*GetOperationsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{11}
}
func (m *GetOperationsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetOperationsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetOperationsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetOperationsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOperationsResponse.Merge(m, src)
}
func (m *GetOperationsResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetOperationsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOperationsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOperationsResponse proto.InternalMessageInfo

func (m *GetOperationsResponse) GetOperationNames() []string {
	if m != nil {
		return m.OperationNames
	}
	return nil
}

func (m *GetOperationsResponse) GetOperations() []*Operation {
	if m != nil {
		return m.Operations
	}
	return nil
}

type TraceQueryParameters struct {
	ServiceName          string            `protobuf:"bytes,1,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	OperationName        string            `protobuf:"bytes,2,opt,name=operation_name,json=operationName,proto3" json:"operation_name,omitempty"`
	Tags                 map[string]string `protobuf:"bytes,3,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	StartTimeMin         time.Time         `protobuf:"bytes,4,opt,name=start_time_min,json=startTimeMin,proto3,stdtime" json:"start_time_min"`
	StartTimeMax         time.Time         `protobuf:"bytes,5,opt,name=start_time_max,json=startTimeMax,proto3,stdtime" json:"start_time_max"`
	DurationMin          time.Duration     `protobuf:"bytes,6,opt,name=duration_min,json=durationMin,proto3,stdduration" json:"duration_min"`
	DurationMax          time.Duration     `protobuf:"bytes,7,opt,name=duration_max,json=durationMax,proto3,stdduration" json:"duration_max"`
	NumTraces            int32             `protobuf:"varint,8,opt,name=num_traces,json=numTraces,proto3" json:"num_traces,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *TraceQueryParameters) Reset()         { *m = TraceQueryParameters{} }
func (m *TraceQueryParameters) String() string { return proto.CompactTextString(m) }
func (*TraceQueryParameters) ProtoMessage()    {}
func (*TraceQueryParameters) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{12}
}
func (m *TraceQueryParameters) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TraceQueryParameters) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TraceQueryParameters.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TraceQueryParameters) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TraceQueryParameters.Merge(m, src)
}
func (m *TraceQueryParameters) XXX_Size() int {
	return m.Size()
}
func (m *TraceQueryParameters) XXX_DiscardUnknown() {
	xxx_messageInfo_TraceQueryParameters.DiscardUnknown(m)
}

var xxx_messageInfo_TraceQueryParameters proto.InternalMessageInfo

func (m *TraceQueryParameters) GetServiceName() string {
	if m != nil {
		return m.ServiceName
	}
	return ""
}

func (m *TraceQueryParameters) GetOperationName() string {
	if m != nil {
		return m.OperationName
	}
	return ""
}

func (m *TraceQueryParameters) GetTags() map[string]string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *TraceQueryParameters) GetStartTimeMin() time.Time {
	if m != nil {
		return m.StartTimeMin
	}
	return time.Time{}
}

func (m *TraceQueryParameters) GetStartTimeMax() time.Time {
	if m != nil {
		return m.StartTimeMax
	}
	return time.Time{}
}

func (m *TraceQueryParameters) GetDurationMin() time.Duration {
	if m != nil {
		return m.DurationMin
	}
	return 0
}

func (m *TraceQueryParameters) GetDurationMax() time.Duration {
	if m != nil {
		return m.DurationMax
	}
	return 0
}

func (m *TraceQueryParameters) GetNumTraces() int32 {
	if m != nil {
		return m.NumTraces
	}
	return 0
}

type FindTracesRequest struct {
	Query                *TraceQueryParameters `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *FindTracesRequest) Reset()         { *m = FindTracesRequest{} }
func (m *FindTracesRequest) String() string { return proto.CompactTextString(m) }
func (*FindTracesRequest) ProtoMessage()    {}
func (*FindTracesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{13}
}
func (m *FindTracesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FindTracesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FindTracesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FindTracesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FindTracesRequest.Merge(m, src)
}
func (m *FindTracesRequest) XXX_Size() int {
	return m.Size()
}
func (m *FindTracesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FindTracesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FindTracesRequest proto.InternalMessageInfo

func (m *FindTracesRequest) GetQuery() *TraceQueryParameters {
	if m != nil {
		return m.Query
	}
	return nil
}

type SpansResponseChunk struct {
	Spans                []v1.Span `protobuf:"bytes,1,rep,name=spans,proto3" json:"spans"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *SpansResponseChunk) Reset()         { *m = SpansResponseChunk{} }
func (m *SpansResponseChunk) String() string { return proto.CompactTextString(m) }
func (*SpansResponseChunk) ProtoMessage()    {}
func (*SpansResponseChunk) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{14}
}
func (m *SpansResponseChunk) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SpansResponseChunk) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SpansResponseChunk.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SpansResponseChunk) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SpansResponseChunk.Merge(m, src)
}
func (m *SpansResponseChunk) XXX_Size() int {
	return m.Size()
}
func (m *SpansResponseChunk) XXX_DiscardUnknown() {
	xxx_messageInfo_SpansResponseChunk.DiscardUnknown(m)
}

var xxx_messageInfo_SpansResponseChunk proto.InternalMessageInfo

func (m *SpansResponseChunk) GetSpans() []v1.Span {
	if m != nil {
		return m.Spans
	}
	return nil
}

type FindTraceIDsRequest struct {
	Query                *TraceQueryParameters `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *FindTraceIDsRequest) Reset()         { *m = FindTraceIDsRequest{} }
func (m *FindTraceIDsRequest) String() string { return proto.CompactTextString(m) }
func (*FindTraceIDsRequest) ProtoMessage()    {}
func (*FindTraceIDsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{15}
}
func (m *FindTraceIDsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FindTraceIDsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FindTraceIDsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FindTraceIDsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FindTraceIDsRequest.Merge(m, src)
}
func (m *FindTraceIDsRequest) XXX_Size() int {
	return m.Size()
}
func (m *FindTraceIDsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FindTraceIDsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FindTraceIDsRequest proto.InternalMessageInfo

func (m *FindTraceIDsRequest) GetQuery() *TraceQueryParameters {
	if m != nil {
		return m.Query
	}
	return nil
}

type FindTraceIDsResponse struct {
	TraceIDs             []github_com_jaegertracing_jaeger_idl_model_v1.TraceID `protobuf:"bytes,1,rep,name=trace_ids,json=traceIds,proto3,customtype=github.com/jaegertracing/jaeger-idl/model/v1.TraceID" json:"trace_ids"`
	XXX_NoUnkeyedLiteral struct{}                                               `json:"-"`
	XXX_unrecognized     []byte                                                 `json:"-"`
	XXX_sizecache        int32                                                  `json:"-"`
}

func (m *FindTraceIDsResponse) Reset()         { *m = FindTraceIDsResponse{} }
func (m *FindTraceIDsResponse) String() string { return proto.CompactTextString(m) }
func (*FindTraceIDsResponse) ProtoMessage()    {}
func (*FindTraceIDsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{16}
}
func (m *FindTraceIDsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FindTraceIDsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FindTraceIDsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FindTraceIDsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FindTraceIDsResponse.Merge(m, src)
}
func (m *FindTraceIDsResponse) XXX_Size() int {
	return m.Size()
}
func (m *FindTraceIDsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FindTraceIDsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FindTraceIDsResponse proto.InternalMessageInfo

// empty; extensible in the future
type CapabilitiesRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CapabilitiesRequest) Reset()         { *m = CapabilitiesRequest{} }
func (m *CapabilitiesRequest) String() string { return proto.CompactTextString(m) }
func (*CapabilitiesRequest) ProtoMessage()    {}
func (*CapabilitiesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{17}
}
func (m *CapabilitiesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CapabilitiesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CapabilitiesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CapabilitiesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CapabilitiesRequest.Merge(m, src)
}
func (m *CapabilitiesRequest) XXX_Size() int {
	return m.Size()
}
func (m *CapabilitiesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CapabilitiesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CapabilitiesRequest proto.InternalMessageInfo

type CapabilitiesResponse struct {
	ArchiveSpanReader    bool     `protobuf:"varint,1,opt,name=archiveSpanReader,proto3" json:"archiveSpanReader,omitempty"` // Deprecated: Do not use.
	ArchiveSpanWriter    bool     `protobuf:"varint,2,opt,name=archiveSpanWriter,proto3" json:"archiveSpanWriter,omitempty"` // Deprecated: Do not use.
	StreamingSpanWriter  bool     `protobuf:"varint,3,opt,name=streamingSpanWriter,proto3" json:"streamingSpanWriter,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CapabilitiesResponse) Reset()         { *m = CapabilitiesResponse{} }
func (m *CapabilitiesResponse) String() string { return proto.CompactTextString(m) }
func (*CapabilitiesResponse) ProtoMessage()    {}
func (*CapabilitiesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_0d2c4ccf1453ffdb, []int{18}
}
func (m *CapabilitiesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CapabilitiesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CapabilitiesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CapabilitiesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CapabilitiesResponse.Merge(m, src)
}
func (m *CapabilitiesResponse) XXX_Size() int {
	return m.Size()
}
func (m *CapabilitiesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CapabilitiesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CapabilitiesResponse proto.InternalMessageInfo

// Deprecated: Do not use.
func (m *CapabilitiesResponse) GetArchiveSpanReader() bool {
	if m != nil {
		return m.ArchiveSpanReader
	}
	return false
}

// Deprecated: Do not use.
func (m *CapabilitiesResponse) GetArchiveSpanWriter() bool {
	if m != nil {
		return m.ArchiveSpanWriter
	}
	return false
}

func (m *CapabilitiesResponse) GetStreamingSpanWriter() bool {
	if m != nil {
		return m.StreamingSpanWriter
	}
	return false
}

func init() {
	proto.RegisterType((*GetDependenciesRequest)(nil), "jaeger.storage.v1.GetDependenciesRequest")
	proto.RegisterType((*GetDependenciesResponse)(nil), "jaeger.storage.v1.GetDependenciesResponse")
	proto.RegisterType((*WriteSpanRequest)(nil), "jaeger.storage.v1.WriteSpanRequest")
	proto.RegisterType((*WriteSpanResponse)(nil), "jaeger.storage.v1.WriteSpanResponse")
	proto.RegisterType((*CloseWriterRequest)(nil), "jaeger.storage.v1.CloseWriterRequest")
	proto.RegisterType((*CloseWriterResponse)(nil), "jaeger.storage.v1.CloseWriterResponse")
	proto.RegisterType((*GetTraceRequest)(nil), "jaeger.storage.v1.GetTraceRequest")
	proto.RegisterType((*GetServicesRequest)(nil), "jaeger.storage.v1.GetServicesRequest")
	proto.RegisterType((*GetServicesResponse)(nil), "jaeger.storage.v1.GetServicesResponse")
	proto.RegisterType((*GetOperationsRequest)(nil), "jaeger.storage.v1.GetOperationsRequest")
	proto.RegisterType((*Operation)(nil), "jaeger.storage.v1.Operation")
	proto.RegisterType((*GetOperationsResponse)(nil), "jaeger.storage.v1.GetOperationsResponse")
	proto.RegisterType((*TraceQueryParameters)(nil), "jaeger.storage.v1.TraceQueryParameters")
	proto.RegisterMapType((map[string]string)(nil), "jaeger.storage.v1.TraceQueryParameters.TagsEntry")
	proto.RegisterType((*FindTracesRequest)(nil), "jaeger.storage.v1.FindTracesRequest")
	proto.RegisterType((*SpansResponseChunk)(nil), "jaeger.storage.v1.SpansResponseChunk")
	proto.RegisterType((*FindTraceIDsRequest)(nil), "jaeger.storage.v1.FindTraceIDsRequest")
	proto.RegisterType((*FindTraceIDsResponse)(nil), "jaeger.storage.v1.FindTraceIDsResponse")
	proto.RegisterType((*CapabilitiesRequest)(nil), "jaeger.storage.v1.CapabilitiesRequest")
	proto.RegisterType((*CapabilitiesResponse)(nil), "jaeger.storage.v1.CapabilitiesResponse")
}

func init() { proto.RegisterFile("storage.proto", fileDescriptor_0d2c4ccf1453ffdb) }

var fileDescriptor_0d2c4ccf1453ffdb = []byte{
	// 1142 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x57, 0x4f, 0x73, 0xdb, 0x44,
	0x14, 0x47, 0x89, 0xdd, 0xd8, 0xcf, 0x4e, 0x9b, 0xac, 0x5d, 0xaa, 0x0a, 0x9a, 0x04, 0x41, 0x93,
	0xc0, 0x0c, 0x72, 0x62, 0x98, 0x81, 0x81, 0x32, 0x0c, 0x4e, 0x52, 0x13, 0xa0, 0x50, 0x94, 0x4c,
	0x3b, 0xc3, 0x9f, 0x7a, 0xd6, 0xd1, 0xa2, 0x2c, 0xb1, 0x56, 0xae, 0xfe, 0x78, 0x92, 0x61, 0x7a,
	0xe3, 0x03, 0x70, 0xe4, 0xc4, 0x95, 0x0b, 0x9f, 0x82, 0x53, 0x8f, 0x9c, 0x39, 0x04, 0x26, 0x57,
	0x3e, 0x02, 0x97, 0x8e, 0x76, 0x57, 0xb2, 0x64, 0x69, 0x92, 0x34, 0xcd, 0xcd, 0xfb, 0xf6, 0xf7,
	0x7e, 0xef, 0xef, 0xbe, 0x27, 0xc3, 0xac, 0x1f, 0xb8, 0x1e, 0xb6, 0x89, 0x31, 0xf4, 0xdc, 0xc0,
	0x45, 0xf3, 0x3f, 0x62, 0x62, 0x13, 0xcf, 0x88, 0xa5, 0xa3, 0x75, 0xad, 0x69, 0xbb, 0xb6, 0xcb,
	0x6f, 0x5b, 0xd1, 0x2f, 0x01, 0xd4, 0x16, 0x6d, 0xd7, 0xb5, 0x07, 0xa4, 0xc5, 0x4f, 0xfd, 0xf0,
	0x87, 0x56, 0x40, 0x1d, 0xe2, 0x07, 0xd8, 0x19, 0x4a, 0xc0, 0xc2, 0x24, 0xc0, 0x0a, 0x3d, 0x1c,
	0x50, 0x97, 0xc9, 0xfb, 0x9a, 0xe3, 0x5a, 0x64, 0x20, 0x0e, 0xfa, 0x6f, 0x0a, 0xbc, 0xdc, 0x25,
	0xc1, 0x26, 0x19, 0x12, 0x66, 0x11, 0xb6, 0x47, 0x89, 0x6f, 0x92, 0xc7, 0x21, 0xf1, 0x03, 0xb4,
	0x01, 0xe0, 0x07, 0xd8, 0x0b, 0x7a, 0x91, 0x01, 0x55, 0x59, 0x52, 0x56, 0x6b, 0x6d, 0xcd, 0x10,
	0xe4, 0x46, 0x4c, 0x6e, 0xec, 0xc6, 0xd6, 0x3b, 0x95, 0xa7, 0xc7, 0x8b, 0x2f, 0xfd, 0xf2, 0xcf,
	0xa2, 0x62, 0x56, 0xb9, 0x5e, 0x74, 0x83, 0x3e, 0x86, 0x0a, 0x61, 0x96, 0xa0, 0x98, 0x7a, 0x0e,
	0x8a, 0x19, 0xc2, 0xac, 0x48, 0xae, 0xf7, 0xe1, 0x46, 0xce, 0x3f, 0x7f, 0xe8, 0x32, 0x9f, 0xa0,
	0x2e, 0xd4, 0xad, 0x94, 0x5c, 0x55, 0x96, 0xa6, 0x57, 0x6b, 0xed, 0x5b, 0x86, 0xcc, 0x24, 0x1e,
	0xd2, 0xde, 0xa8, 0x6d, 0x24, 0xaa, 0x47, 0x5f, 0x50, 0x76, 0xd0, 0x29, 0x45, 0x26, 0xcc, 0x8c,
	0xa2, 0xfe, 0x21, 0xcc, 0x3d, 0xf4, 0x68, 0x40, 0x76, 0x86, 0x98, 0xc5, 0xd1, 0xaf, 0x40, 0xc9,
	0x1f, 0x62, 0x26, 0xe3, 0x6e, 0x4c, 0x90, 0x72, 0x24, 0x07, 0xe8, 0x0d, 0x98, 0x4f, 0x29, 0x0b,
	0xd7, 0xf4, 0x26, 0xa0, 0x8d, 0x81, 0xeb, 0x13, 0x7e, 0xe3, 0x49, 0x4e, 0xfd, 0x3a, 0x34, 0x32,
	0x52, 0x09, 0xfe, 0x5f, 0x81, 0x6b, 0x5d, 0x12, 0xec, 0x7a, 0x78, 0x8f, 0xc4, 0xe6, 0xfb, 0x50,
	0x09, 0xa2, 0x73, 0x8f, 0x5a, 0xdc, 0x85, 0x7a, 0xa7, 0x1b, 0x39, 0xfe, 0xf7, 0xf1, 0xe2, 0xbb,
	0x36, 0x0d, 0xf6, 0xc3, 0xbe, 0xb1, 0xe7, 0x3a, 0x2d, 0xe1, 0x54, 0x04, 0xa4, 0xcc, 0x96, 0xa7,
	0xb7, 0xa9, 0x35, 0x68, 0xf1, 0x12, 0xb7, 0x46, 0xeb, 0x06, 0x27, 0xdd, 0xde, 0x3c, 0x39, 0x5e,
	0x9c, 0x91, 0x3f, 0xcd, 0x19, 0x4e, 0xbc, 0x6d, 0x4d, 0x14, 0x78, 0xea, 0xc5, 0x0b, 0x3c, 0x7d,
	0x91, 0x02, 0x37, 0x01, 0x75, 0x49, 0xb0, 0x43, 0xbc, 0x11, 0xdd, 0x4b, 0x9a, 0x4f, 0x5f, 0x87,
	0x46, 0x46, 0x2a, 0x4b, 0xae, 0x41, 0xc5, 0x97, 0x32, 0x5e, 0xee, 0xaa, 0x99, 0x9c, 0xf5, 0x7b,
	0xd0, 0xec, 0x92, 0xe0, 0xab, 0x21, 0x11, 0xdd, 0x9e, 0xf4, 0xb1, 0x0a, 0x33, 0x12, 0xc3, 0x33,
	0x59, 0x35, 0xe3, 0x23, 0x7a, 0x05, 0xaa, 0x51, 0x09, 0x7b, 0x07, 0x94, 0x59, 0x3c, 0xfe, 0x88,
	0x6e, 0x88, 0xd9, 0xe7, 0x94, 0x59, 0xfa, 0x1d, 0xa8, 0x26, 0x5c, 0x08, 0x41, 0x89, 0x61, 0x27,
	0x26, 0xe0, 0xbf, 0x4f, 0xd7, 0x7e, 0x02, 0xd7, 0x27, 0x9c, 0x91, 0x11, 0x2c, 0xc3, 0x55, 0x37,
	0x96, 0x7e, 0x89, 0x9d, 0x24, 0x8e, 0x09, 0x29, 0xba, 0x03, 0x90, 0x48, 0x7c, 0x75, 0x8a, 0xb7,
	0xf6, 0xab, 0x46, 0x6e, 0x48, 0x18, 0x89, 0x09, 0x33, 0x85, 0xd7, 0x7f, 0x2f, 0x41, 0x93, 0xd7,
	0xfb, 0xeb, 0x90, 0x78, 0x47, 0xf7, 0xb1, 0x87, 0x1d, 0x12, 0x10, 0xcf, 0x47, 0xaf, 0x41, 0x5d,
	0x46, 0xdf, 0x4b, 0x05, 0x54, 0x93, 0xb2, 0xc8, 0x34, 0xba, 0x9d, 0xf2, 0x50, 0x80, 0x44, 0x70,
	0xb3, 0x19, 0x0f, 0xd1, 0x16, 0x94, 0x02, 0x6c, 0xfb, 0xea, 0x34, 0x77, 0x6d, 0xbd, 0xc0, 0xb5,
	0x22, 0x07, 0x8c, 0x5d, 0x6c, 0xfb, 0x5b, 0x2c, 0xf0, 0x8e, 0x4c, 0xae, 0x8e, 0x3e, 0x83, 0xab,
	0xe3, 0x26, 0xec, 0x39, 0x94, 0xa9, 0xa5, 0xe7, 0xe8, 0xa2, 0x7a, 0xd2, 0x88, 0xf7, 0x28, 0x9b,
	0xe4, 0xc2, 0x87, 0x6a, 0xf9, 0x62, 0x5c, 0xf8, 0x10, 0xdd, 0x85, 0x7a, 0x3c, 0x37, 0xb9, 0x57,
	0x57, 0x38, 0xd3, 0xcd, 0x1c, 0xd3, 0xa6, 0x04, 0x09, 0xa2, 0x5f, 0x23, 0xa2, 0x5a, 0xac, 0x18,
	0xf9, 0x94, 0xe1, 0xc1, 0x87, 0xea, 0xcc, 0x45, 0x78, 0xf0, 0x21, 0xba, 0x05, 0xc0, 0x42, 0xa7,
	0xc7, 0xdf, 0xae, 0xaf, 0x56, 0x96, 0x94, 0xd5, 0xb2, 0x59, 0x65, 0xa1, 0xc3, 0x93, 0xec, 0x6b,
	0xef, 0x41, 0x35, 0xc9, 0x2c, 0x9a, 0x83, 0xe9, 0x03, 0x72, 0x24, 0x6b, 0x1b, 0xfd, 0x44, 0x4d,
	0x28, 0x8f, 0xf0, 0x20, 0x8c, 0x4b, 0x29, 0x0e, 0x1f, 0x4c, 0xbd, 0xaf, 0xe8, 0x26, 0xcc, 0xdf,
	0xa5, 0xcc, 0x12, 0x34, 0xf1, 0x93, 0xf9, 0x08, 0xca, 0x8f, 0xa3, 0xba, 0xc9, 0xe9, 0xb7, 0x72,
	0xce, 0xe2, 0x9a, 0x42, 0x4b, 0xdf, 0x02, 0x14, 0x4d, 0xc3, 0xa4, 0xe9, 0x37, 0xf6, 0x43, 0x76,
	0x80, 0x5a, 0x50, 0x8e, 0x9e, 0x47, 0x3c, 0xa7, 0x8b, 0x46, 0xaa, 0x9c, 0xce, 0x02, 0xa7, 0xef,
	0x42, 0x23, 0x71, 0x6d, 0x7b, 0xf3, 0xb2, 0x9c, 0x7b, 0x02, 0xcd, 0x2c, 0xab, 0x7c, 0x98, 0x04,
	0xaa, 0xf1, 0xc4, 0x15, 0x2e, 0xd6, 0x3b, 0x9f, 0xbe, 0xe0, 0xc8, 0xad, 0x24, 0x46, 0x2a, 0x72,
	0xe6, 0xfa, 0x7c, 0x07, 0xe0, 0x21, 0xee, 0xd3, 0x01, 0x0d, 0xc6, 0xcb, 0x56, 0xff, 0x43, 0x81,
	0x66, 0x56, 0x2e, 0xdd, 0x5a, 0x83, 0x79, 0xec, 0xed, 0xed, 0xd3, 0x91, 0x5c, 0x30, 0xd8, 0x22,
	0x1e, 0x8f, 0xbc, 0xd2, 0x99, 0x52, 0x15, 0x33, 0x7f, 0x39, 0xa1, 0x21, 0x76, 0x0d, 0xaf, 0x7b,
	0x5e, 0x43, 0x5c, 0xa2, 0x35, 0x68, 0xf8, 0x81, 0x47, 0xb0, 0x43, 0x99, 0x9d, 0xd2, 0x89, 0xc6,
	0x79, 0xc5, 0x2c, 0xba, 0x6a, 0xff, 0xa9, 0xc0, 0xdc, 0xf8, 0x78, 0x7f, 0x10, 0xda, 0x94, 0xa1,
	0x07, 0x50, 0x4d, 0x36, 0x21, 0x7a, 0xbd, 0xa0, 0x2c, 0x93, 0x4b, 0x56, 0x7b, 0xe3, 0x74, 0x90,
	0x4c, 0xc1, 0x03, 0x28, 0xf3, 0xb5, 0x89, 0x6e, 0x17, 0xc0, 0xf3, 0x6b, 0x56, 0x5b, 0x3e, 0x0b,
	0x26, 0x78, 0xdb, 0x3f, 0xc1, 0xcd, 0x9d, 0x7c, 0x6c, 0x32, 0x98, 0x47, 0x70, 0x2d, 0xf1, 0x44,
	0xa0, 0x2e, 0x31, 0xa4, 0x55, 0xa5, 0xfd, 0xdf, 0xb4, 0xc8, 0xa0, 0x28, 0x9a, 0x34, 0xfa, 0x10,
	0x2a, 0xf1, 0x87, 0x00, 0xd2, 0x0b, 0x88, 0x26, 0xbe, 0x12, 0xb4, 0xa2, 0x84, 0xe4, 0x5f, 0xde,
	0x9a, 0x82, 0xbe, 0x83, 0x5a, 0x6a, 0x9d, 0x16, 0x26, 0x32, 0xbf, 0x84, 0x0b, 0x13, 0x59, 0xb4,
	0x95, 0xfb, 0x30, 0x9b, 0x59, 0x76, 0x68, 0xa5, 0x58, 0x31, 0xb7, 0x9b, 0xb5, 0xd5, 0xb3, 0x81,
	0xd2, 0xc6, 0xb7, 0x00, 0xe3, 0x39, 0x85, 0x8a, 0xb2, 0x9c, 0x1b, 0x63, 0xe7, 0x4f, 0x4f, 0x0f,
	0xea, 0xe9, 0x99, 0x80, 0x96, 0x4f, 0xa3, 0x1f, 0x8f, 0x22, 0x6d, 0xe5, 0x4c, 0x9c, 0x6c, 0xb5,
	0x43, 0xb8, 0xf1, 0xc9, 0xe4, 0xb3, 0x93, 0x35, 0xff, 0x5e, 0x7e, 0x7c, 0xa6, 0xee, 0x2f, 0xb1,
	0xd3, 0xda, 0x47, 0x19, 0xcb, 0x99, 0x6e, 0x7b, 0xc4, 0x3f, 0x3b, 0xe5, 0xed, 0xe5, 0x37, 0x5d,
	0xfb, 0x67, 0x05, 0xd4, 0xec, 0x87, 0x7b, 0xca, 0xf8, 0x3e, 0x37, 0x9e, 0xbe, 0x46, 0x6f, 0x16,
	0x1b, 0x2f, 0xf8, 0x6f, 0xa2, 0xbd, 0x75, 0x1e, 0xa8, 0xcc, 0x40, 0x08, 0x48, 0xd8, 0x4c, 0xcf,
	0xd7, 0xa8, 0xe4, 0x99, 0x73, 0xe1, 0xd0, 0xc8, 0x0f, 0xea, 0xc2, 0x92, 0x17, 0x0d, 0xee, 0x8e,
	0xfa, 0xf4, 0x64, 0x41, 0xf9, 0xeb, 0x64, 0x41, 0xf9, 0xf7, 0x64, 0x41, 0xf9, 0x06, 0x24, 0xbc,
	0x37, 0x5a, 0xef, 0x5f, 0xe1, 0x4b, 0xff, 0x9d, 0x67, 0x01, 0x00, 0x00, 0xff, 0xff, 0x6c, 0xd4,
	0x66, 0xf9, 0x02, 0x0e, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SpanWriterPluginClient is the client API for SpanWriterPlugin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SpanWriterPluginClient interface {
	// spanstore/Writer
	WriteSpan(ctx context.Context, in *WriteSpanRequest, opts ...grpc.CallOption) (*WriteSpanResponse, error)
	Close(ctx context.Context, in *CloseWriterRequest, opts ...grpc.CallOption) (*CloseWriterResponse, error)
}

type spanWriterPluginClient struct {
	cc *grpc.ClientConn
}

func NewSpanWriterPluginClient(cc *grpc.ClientConn) SpanWriterPluginClient {
	return &spanWriterPluginClient{cc}
}

func (c *spanWriterPluginClient) WriteSpan(ctx context.Context, in *WriteSpanRequest, opts ...grpc.CallOption) (*WriteSpanResponse, error) {
	out := new(WriteSpanResponse)
	err := c.cc.Invoke(ctx, "/jaeger.storage.v1.SpanWriterPlugin/WriteSpan", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *spanWriterPluginClient) Close(ctx context.Context, in *CloseWriterRequest, opts ...grpc.CallOption) (*CloseWriterResponse, error) {
	out := new(CloseWriterResponse)
	err := c.cc.Invoke(ctx, "/jaeger.storage.v1.SpanWriterPlugin/Close", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SpanWriterPluginServer is the server API for SpanWriterPlugin service.
type SpanWriterPluginServer interface {
	// spanstore/Writer
	WriteSpan(context.Context, *WriteSpanRequest) (*WriteSpanResponse, error)
	Close(context.Context, *CloseWriterRequest) (*CloseWriterResponse, error)
}

// UnimplementedSpanWriterPluginServer can be embedded to have forward compatible implementations.
type UnimplementedSpanWriterPluginServer struct {
}

func (*UnimplementedSpanWriterPluginServer) WriteSpan(ctx context.Context, req *WriteSpanRequest) (*WriteSpanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WriteSpan not implemented")
}
func (*UnimplementedSpanWriterPluginServer) Close(ctx context.Context, req *CloseWriterRequest) (*CloseWriterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Close not implemented")
}

func RegisterSpanWriterPluginServer(s *grpc.Server, srv SpanWriterPluginServer) {
	s.RegisterService(&_SpanWriterPlugin_serviceDesc, srv)
}

func _SpanWriterPlugin_WriteSpan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteSpanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpanWriterPluginServer).WriteSpan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jaeger.storage.v1.SpanWriterPlugin/WriteSpan",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpanWriterPluginServer).WriteSpan(ctx, req.(*WriteSpanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SpanWriterPlugin_Close_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseWriterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpanWriterPluginServer).Close(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jaeger.storage.v1.SpanWriterPlugin/Close",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpanWriterPluginServer).Close(ctx, req.(*CloseWriterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SpanWriterPlugin_serviceDesc = grpc.ServiceDesc{
	ServiceName: "jaeger.storage.v1.SpanWriterPlugin",
	HandlerType: (*SpanWriterPluginServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "WriteSpan",
			Handler:    _SpanWriterPlugin_WriteSpan_Handler,
		},
		{
			MethodName: "Close",
			Handler:    _SpanWriterPlugin_Close_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "storage.proto",
}

// StreamingSpanWriterPluginClient is the client API for StreamingSpanWriterPlugin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type StreamingSpanWriterPluginClient interface {
	WriteSpanStream(ctx context.Context, opts ...grpc.CallOption) (StreamingSpanWriterPlugin_WriteSpanStreamClient, error)
}

type streamingSpanWriterPluginClient struct {
	cc *grpc.ClientConn
}

func NewStreamingSpanWriterPluginClient(cc *grpc.ClientConn) StreamingSpanWriterPluginClient {
	return &streamingSpanWriterPluginClient{cc}
}

func (c *streamingSpanWriterPluginClient) WriteSpanStream(ctx context.Context, opts ...grpc.CallOption) (StreamingSpanWriterPlugin_WriteSpanStreamClient, error) {
	stream, err := c.cc.NewStream(ctx, &_StreamingSpanWriterPlugin_serviceDesc.Streams[0], "/jaeger.storage.v1.StreamingSpanWriterPlugin/WriteSpanStream", opts...)
	if err != nil {
		return nil, err
	}
	x := &streamingSpanWriterPluginWriteSpanStreamClient{stream}
	return x, nil
}

type StreamingSpanWriterPlugin_WriteSpanStreamClient interface {
	Send(*WriteSpanRequest) error
	CloseAndRecv() (*WriteSpanResponse, error)
	grpc.ClientStream
}

type streamingSpanWriterPluginWriteSpanStreamClient struct {
	grpc.ClientStream
}

func (x *streamingSpanWriterPluginWriteSpanStreamClient) Send(m *WriteSpanRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *streamingSpanWriterPluginWriteSpanStreamClient) CloseAndRecv() (*WriteSpanResponse, error) {
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	m := new(WriteSpanResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// StreamingSpanWriterPluginServer is the server API for StreamingSpanWriterPlugin service.
type StreamingSpanWriterPluginServer interface {
	WriteSpanStream(StreamingSpanWriterPlugin_WriteSpanStreamServer) error
}

// UnimplementedStreamingSpanWriterPluginServer can be embedded to have forward compatible implementations.
type UnimplementedStreamingSpanWriterPluginServer struct {
}

func (*UnimplementedStreamingSpanWriterPluginServer) WriteSpanStream(srv StreamingSpanWriterPlugin_WriteSpanStreamServer) error {
	return status.Errorf(codes.Unimplemented, "method WriteSpanStream not implemented")
}

func RegisterStreamingSpanWriterPluginServer(s *grpc.Server, srv StreamingSpanWriterPluginServer) {
	s.RegisterService(&_StreamingSpanWriterPlugin_serviceDesc, srv)
}

func _StreamingSpanWriterPlugin_WriteSpanStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(StreamingSpanWriterPluginServer).WriteSpanStream(&streamingSpanWriterPluginWriteSpanStreamServer{stream})
}

type StreamingSpanWriterPlugin_WriteSpanStreamServer interface {
	SendAndClose(*WriteSpanResponse) error
	Recv() (*WriteSpanRequest, error)
	grpc.ServerStream
}

type streamingSpanWriterPluginWriteSpanStreamServer struct {
	grpc.ServerStream
}

func (x *streamingSpanWriterPluginWriteSpanStreamServer) SendAndClose(m *WriteSpanResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *streamingSpanWriterPluginWriteSpanStreamServer) Recv() (*WriteSpanRequest, error) {
	m := new(WriteSpanRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _StreamingSpanWriterPlugin_serviceDesc = grpc.ServiceDesc{
	ServiceName: "jaeger.storage.v1.StreamingSpanWriterPlugin",
	HandlerType: (*StreamingSpanWriterPluginServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "WriteSpanStream",
			Handler:       _StreamingSpanWriterPlugin_WriteSpanStream_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "storage.proto",
}

// SpanReaderPluginClient is the client API for SpanReaderPlugin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SpanReaderPluginClient interface {
	// spanstore/Reader
	GetTrace(ctx context.Context, in *GetTraceRequest, opts ...grpc.CallOption) (SpanReaderPlugin_GetTraceClient, error)
	GetServices(ctx context.Context, in *GetServicesRequest, opts ...grpc.CallOption) (*GetServicesResponse, error)
	GetOperations(ctx context.Context, in *GetOperationsRequest, opts ...grpc.CallOption) (*GetOperationsResponse, error)
	FindTraces(ctx context.Context, in *FindTracesRequest, opts ...grpc.CallOption) (SpanReaderPlugin_FindTracesClient, error)
	FindTraceIDs(ctx context.Context, in *FindTraceIDsRequest, opts ...grpc.CallOption) (*FindTraceIDsResponse, error)
}

type spanReaderPluginClient struct {
	cc *grpc.ClientConn
}

func NewSpanReaderPluginClient(cc *grpc.ClientConn) SpanReaderPluginClient {
	return &spanReaderPluginClient{cc}
}

func (c *spanReaderPluginClient) GetTrace(ctx context.Context, in *GetTraceRequest, opts ...grpc.CallOption) (SpanReaderPlugin_GetTraceClient, error) {
	stream, err := c.cc.NewStream(ctx, &_SpanReaderPlugin_serviceDesc.Streams[0], "/jaeger.storage.v1.SpanReaderPlugin/GetTrace", opts...)
	if err != nil {
		return nil, err
	}
	x := &spanReaderPluginGetTraceClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type SpanReaderPlugin_GetTraceClient interface {
	Recv() (*SpansResponseChunk, error)
	grpc.ClientStream
}

type spanReaderPluginGetTraceClient struct {
	grpc.ClientStream
}

func (x *spanReaderPluginGetTraceClient) Recv() (*SpansResponseChunk, error) {
	m := new(SpansResponseChunk)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *spanReaderPluginClient) GetServices(ctx context.Context, in *GetServicesRequest, opts ...grpc.CallOption) (*GetServicesResponse, error) {
	out := new(GetServicesResponse)
	err := c.cc.Invoke(ctx, "/jaeger.storage.v1.SpanReaderPlugin/GetServices", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *spanReaderPluginClient) GetOperations(ctx context.Context, in *GetOperationsRequest, opts ...grpc.CallOption) (*GetOperationsResponse, error) {
	out := new(GetOperationsResponse)
	err := c.cc.Invoke(ctx, "/jaeger.storage.v1.SpanReaderPlugin/GetOperations", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *spanReaderPluginClient) FindTraces(ctx context.Context, in *FindTracesRequest, opts ...grpc.CallOption) (SpanReaderPlugin_FindTracesClient, error) {
	stream, err := c.cc.NewStream(ctx, &_SpanReaderPlugin_serviceDesc.Streams[1], "/jaeger.storage.v1.SpanReaderPlugin/FindTraces", opts...)
	if err != nil {
		return nil, err
	}
	x := &spanReaderPluginFindTracesClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type SpanReaderPlugin_FindTracesClient interface {
	Recv() (*SpansResponseChunk, error)
	grpc.ClientStream
}

type spanReaderPluginFindTracesClient struct {
	grpc.ClientStream
}

func (x *spanReaderPluginFindTracesClient) Recv() (*SpansResponseChunk, error) {
	m := new(SpansResponseChunk)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *spanReaderPluginClient) FindTraceIDs(ctx context.Context, in *FindTraceIDsRequest, opts ...grpc.CallOption) (*FindTraceIDsResponse, error) {
	out := new(FindTraceIDsResponse)
	err := c.cc.Invoke(ctx, "/jaeger.storage.v1.SpanReaderPlugin/FindTraceIDs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SpanReaderPluginServer is the server API for SpanReaderPlugin service.
type SpanReaderPluginServer interface {
	// spanstore/Reader
	GetTrace(*GetTraceRequest, SpanReaderPlugin_GetTraceServer) error
	GetServices(context.Context, *GetServicesRequest) (*GetServicesResponse, error)
	GetOperations(context.Context, *GetOperationsRequest) (*GetOperationsResponse, error)
	FindTraces(*FindTracesRequest, SpanReaderPlugin_FindTracesServer) error
	FindTraceIDs(context.Context, *FindTraceIDsRequest) (*FindTraceIDsResponse, error)
}

// UnimplementedSpanReaderPluginServer can be embedded to have forward compatible implementations.
type UnimplementedSpanReaderPluginServer struct {
}

func (*UnimplementedSpanReaderPluginServer) GetTrace(req *GetTraceRequest, srv SpanReaderPlugin_GetTraceServer) error {
	return status.Errorf(codes.Unimplemented, "method GetTrace not implemented")
}
func (*UnimplementedSpanReaderPluginServer) GetServices(ctx context.Context, req *GetServicesRequest) (*GetServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServices not implemented")
}
func (*UnimplementedSpanReaderPluginServer) GetOperations(ctx context.Context, req *GetOperationsRequest) (*GetOperationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOperations not implemented")
}
func (*UnimplementedSpanReaderPluginServer) FindTraces(req *FindTracesRequest, srv SpanReaderPlugin_FindTracesServer) error {
	return status.Errorf(codes.Unimplemented, "method FindTraces not implemented")
}
func (*UnimplementedSpanReaderPluginServer) FindTraceIDs(ctx context.Context, req *FindTraceIDsRequest) (*FindTraceIDsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindTraceIDs not implemented")
}

func RegisterSpanReaderPluginServer(s *grpc.Server, srv SpanReaderPluginServer) {
	s.RegisterService(&_SpanReaderPlugin_serviceDesc, srv)
}

func _SpanReaderPlugin_GetTrace_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetTraceRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(SpanReaderPluginServer).GetTrace(m, &spanReaderPluginGetTraceServer{stream})
}

type SpanReaderPlugin_GetTraceServer interface {
	Send(*SpansResponseChunk) error
	grpc.ServerStream
}

type spanReaderPluginGetTraceServer struct {
	grpc.ServerStream
}

func (x *spanReaderPluginGetTraceServer) Send(m *SpansResponseChunk) error {
	return x.ServerStream.SendMsg(m)
}

func _SpanReaderPlugin_GetServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpanReaderPluginServer).GetServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jaeger.storage.v1.SpanReaderPlugin/GetServices",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpanReaderPluginServer).GetServices(ctx, req.(*GetServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SpanReaderPlugin_GetOperations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOperationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpanReaderPluginServer).GetOperations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jaeger.storage.v1.SpanReaderPlugin/GetOperations",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpanReaderPluginServer).GetOperations(ctx, req.(*GetOperationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SpanReaderPlugin_FindTraces_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(FindTracesRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(SpanReaderPluginServer).FindTraces(m, &spanReaderPluginFindTracesServer{stream})
}

type SpanReaderPlugin_FindTracesServer interface {
	Send(*SpansResponseChunk) error
	grpc.ServerStream
}

type spanReaderPluginFindTracesServer struct {
	grpc.ServerStream
}

func (x *spanReaderPluginFindTracesServer) Send(m *SpansResponseChunk) error {
	return x.ServerStream.SendMsg(m)
}

func _SpanReaderPlugin_FindTraceIDs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindTraceIDsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpanReaderPluginServer).FindTraceIDs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jaeger.storage.v1.SpanReaderPlugin/FindTraceIDs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpanReaderPluginServer).FindTraceIDs(ctx, req.(*FindTraceIDsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SpanReaderPlugin_serviceDesc = grpc.ServiceDesc{
	ServiceName: "jaeger.storage.v1.SpanReaderPlugin",
	HandlerType: (*SpanReaderPluginServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetServices",
			Handler:    _SpanReaderPlugin_GetServices_Handler,
		},
		{
			MethodName: "GetOperations",
			Handler:    _SpanReaderPlugin_GetOperations_Handler,
		},
		{
			MethodName: "FindTraceIDs",
			Handler:    _SpanReaderPlugin_FindTraceIDs_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetTrace",
			Handler:       _SpanReaderPlugin_GetTrace_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "FindTraces",
			Handler:       _SpanReaderPlugin_FindTraces_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "storage.proto",
}

// ArchiveSpanWriterPluginClient is the client API for ArchiveSpanWriterPlugin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ArchiveSpanWriterPluginClient interface {
	// spanstore/Writer
	WriteArchiveSpan(ctx context.Context, in *WriteSpanRequest, opts ...grpc.CallOption) (*WriteSpanResponse, error)
}

type archiveSpanWriterPluginClient struct {
	cc *grpc.ClientConn
}

func NewArchiveSpanWriterPluginClient(cc *grpc.ClientConn) ArchiveSpanWriterPluginClient {
	return &archiveSpanWriterPluginClient{cc}
}

func (c *archiveSpanWriterPluginClient) WriteArchiveSpan(ctx context.Context, in *WriteSpanRequest, opts ...grpc.CallOption) (*WriteSpanResponse, error) {
	out := new(WriteSpanResponse)
	err := c.cc.Invoke(ctx, "/jaeger.storage.v1.ArchiveSpanWriterPlugin/WriteArchiveSpan", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ArchiveSpanWriterPluginServer is the server API for ArchiveSpanWriterPlugin service.
type ArchiveSpanWriterPluginServer interface {
	// spanstore/Writer
	WriteArchiveSpan(context.Context, *WriteSpanRequest) (*WriteSpanResponse, error)
}

// UnimplementedArchiveSpanWriterPluginServer can be embedded to have forward compatible implementations.
type UnimplementedArchiveSpanWriterPluginServer struct {
}

func (*UnimplementedArchiveSpanWriterPluginServer) WriteArchiveSpan(ctx context.Context, req *WriteSpanRequest) (*WriteSpanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WriteArchiveSpan not implemented")
}

func RegisterArchiveSpanWriterPluginServer(s *grpc.Server, srv ArchiveSpanWriterPluginServer) {
	s.RegisterService(&_ArchiveSpanWriterPlugin_serviceDesc, srv)
}

func _ArchiveSpanWriterPlugin_WriteArchiveSpan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteSpanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ArchiveSpanWriterPluginServer).WriteArchiveSpan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jaeger.storage.v1.ArchiveSpanWriterPlugin/WriteArchiveSpan",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ArchiveSpanWriterPluginServer).WriteArchiveSpan(ctx, req.(*WriteSpanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ArchiveSpanWriterPlugin_serviceDesc = grpc.ServiceDesc{
	ServiceName: "jaeger.storage.v1.ArchiveSpanWriterPlugin",
	HandlerType: (*ArchiveSpanWriterPluginServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "WriteArchiveSpan",
			Handler:    _ArchiveSpanWriterPlugin_WriteArchiveSpan_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "storage.proto",
}

// ArchiveSpanReaderPluginClient is the client API for ArchiveSpanReaderPlugin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ArchiveSpanReaderPluginClient interface {
	// spanstore/Reader
	GetArchiveTrace(ctx context.Context, in *GetTraceRequest, opts ...grpc.CallOption) (ArchiveSpanReaderPlugin_GetArchiveTraceClient, error)
}

type archiveSpanReaderPluginClient struct {
	cc *grpc.ClientConn
}

func NewArchiveSpanReaderPluginClient(cc *grpc.ClientConn) ArchiveSpanReaderPluginClient {
	return &archiveSpanReaderPluginClient{cc}
}

func (c *archiveSpanReaderPluginClient) GetArchiveTrace(ctx context.Context, in *GetTraceRequest, opts ...grpc.CallOption) (ArchiveSpanReaderPlugin_GetArchiveTraceClient, error) {
	stream, err := c.cc.NewStream(ctx, &_ArchiveSpanReaderPlugin_serviceDesc.Streams[0], "/jaeger.storage.v1.ArchiveSpanReaderPlugin/GetArchiveTrace", opts...)
	if err != nil {
		return nil, err
	}
	x := &archiveSpanReaderPluginGetArchiveTraceClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type ArchiveSpanReaderPlugin_GetArchiveTraceClient interface {
	Recv() (*SpansResponseChunk, error)
	grpc.ClientStream
}

type archiveSpanReaderPluginGetArchiveTraceClient struct {
	grpc.ClientStream
}

func (x *archiveSpanReaderPluginGetArchiveTraceClient) Recv() (*SpansResponseChunk, error) {
	m := new(SpansResponseChunk)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// ArchiveSpanReaderPluginServer is the server API for ArchiveSpanReaderPlugin service.
type ArchiveSpanReaderPluginServer interface {
	// spanstore/Reader
	GetArchiveTrace(*GetTraceRequest, ArchiveSpanReaderPlugin_GetArchiveTraceServer) error
}

// UnimplementedArchiveSpanReaderPluginServer can be embedded to have forward compatible implementations.
type UnimplementedArchiveSpanReaderPluginServer struct {
}

func (*UnimplementedArchiveSpanReaderPluginServer) GetArchiveTrace(req *GetTraceRequest, srv ArchiveSpanReaderPlugin_GetArchiveTraceServer) error {
	return status.Errorf(codes.Unimplemented, "method GetArchiveTrace not implemented")
}

func RegisterArchiveSpanReaderPluginServer(s *grpc.Server, srv ArchiveSpanReaderPluginServer) {
	s.RegisterService(&_ArchiveSpanReaderPlugin_serviceDesc, srv)
}

func _ArchiveSpanReaderPlugin_GetArchiveTrace_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GetTraceRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ArchiveSpanReaderPluginServer).GetArchiveTrace(m, &archiveSpanReaderPluginGetArchiveTraceServer{stream})
}

type ArchiveSpanReaderPlugin_GetArchiveTraceServer interface {
	Send(*SpansResponseChunk) error
	grpc.ServerStream
}

type archiveSpanReaderPluginGetArchiveTraceServer struct {
	grpc.ServerStream
}

func (x *archiveSpanReaderPluginGetArchiveTraceServer) Send(m *SpansResponseChunk) error {
	return x.ServerStream.SendMsg(m)
}

var _ArchiveSpanReaderPlugin_serviceDesc = grpc.ServiceDesc{
	ServiceName: "jaeger.storage.v1.ArchiveSpanReaderPlugin",
	HandlerType: (*ArchiveSpanReaderPluginServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetArchiveTrace",
			Handler:       _ArchiveSpanReaderPlugin_GetArchiveTrace_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "storage.proto",
}

// DependenciesReaderPluginClient is the client API for DependenciesReaderPlugin service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type DependenciesReaderPluginClient interface {
	// dependencystore/Reader
	GetDependencies(ctx context.Context, in *GetDependenciesRequest, opts ...grpc.CallOption) (*GetDependenciesResponse, error)
}

type dependenciesReaderPluginClient struct {
	cc *grpc.ClientConn
}

func NewDependenciesReaderPluginClient(cc *grpc.ClientConn) DependenciesReaderPluginClient {
	return &dependenciesReaderPluginClient{cc}
}

func (c *dependenciesReaderPluginClient) GetDependencies(ctx context.Context, in *GetDependenciesRequest, opts ...grpc.CallOption) (*GetDependenciesResponse, error) {
	out := new(GetDependenciesResponse)
	err := c.cc.Invoke(ctx, "/jaeger.storage.v1.DependenciesReaderPlugin/GetDependencies", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DependenciesReaderPluginServer is the server API for DependenciesReaderPlugin service.
type DependenciesReaderPluginServer interface {
	// dependencystore/Reader
	GetDependencies(context.Context, *GetDependenciesRequest) (*GetDependenciesResponse, error)
}

// UnimplementedDependenciesReaderPluginServer can be embedded to have forward compatible implementations.
type UnimplementedDependenciesReaderPluginServer struct {
}

func (*UnimplementedDependenciesReaderPluginServer) GetDependencies(ctx context.Context, req *GetDependenciesRequest) (*GetDependenciesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDependencies not implemented")
}

func RegisterDependenciesReaderPluginServer(s *grpc.Server, srv DependenciesReaderPluginServer) {
	s.RegisterService(&_DependenciesReaderPlugin_serviceDesc, srv)
}

func _DependenciesReaderPlugin_GetDependencies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDependenciesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DependenciesReaderPluginServer).GetDependencies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jaeger.storage.v1.DependenciesReaderPlugin/GetDependencies",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DependenciesReaderPluginServer).GetDependencies(ctx, req.(*GetDependenciesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DependenciesReaderPlugin_serviceDesc = grpc.ServiceDesc{
	ServiceName: "jaeger.storage.v1.DependenciesReaderPlugin",
	HandlerType: (*DependenciesReaderPluginServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetDependencies",
			Handler:    _DependenciesReaderPlugin_GetDependencies_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "storage.proto",
}

// PluginCapabilitiesClient is the client API for PluginCapabilities service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PluginCapabilitiesClient interface {
	Capabilities(ctx context.Context, in *CapabilitiesRequest, opts ...grpc.CallOption) (*CapabilitiesResponse, error)
}

type pluginCapabilitiesClient struct {
	cc *grpc.ClientConn
}

func NewPluginCapabilitiesClient(cc *grpc.ClientConn) PluginCapabilitiesClient {
	return &pluginCapabilitiesClient{cc}
}

func (c *pluginCapabilitiesClient) Capabilities(ctx context.Context, in *CapabilitiesRequest, opts ...grpc.CallOption) (*CapabilitiesResponse, error) {
	out := new(CapabilitiesResponse)
	err := c.cc.Invoke(ctx, "/jaeger.storage.v1.PluginCapabilities/Capabilities", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PluginCapabilitiesServer is the server API for PluginCapabilities service.
type PluginCapabilitiesServer interface {
	Capabilities(context.Context, *CapabilitiesRequest) (*CapabilitiesResponse, error)
}

// UnimplementedPluginCapabilitiesServer can be embedded to have forward compatible implementations.
type UnimplementedPluginCapabilitiesServer struct {
}

func (*UnimplementedPluginCapabilitiesServer) Capabilities(ctx context.Context, req *CapabilitiesRequest) (*CapabilitiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Capabilities not implemented")
}

func RegisterPluginCapabilitiesServer(s *grpc.Server, srv PluginCapabilitiesServer) {
	s.RegisterService(&_PluginCapabilities_serviceDesc, srv)
}

func _PluginCapabilities_Capabilities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CapabilitiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PluginCapabilitiesServer).Capabilities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/jaeger.storage.v1.PluginCapabilities/Capabilities",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PluginCapabilitiesServer).Capabilities(ctx, req.(*CapabilitiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PluginCapabilities_serviceDesc = grpc.ServiceDesc{
	ServiceName: "jaeger.storage.v1.PluginCapabilities",
	HandlerType: (*PluginCapabilitiesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Capabilities",
			Handler:    _PluginCapabilities_Capabilities_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "storage.proto",
}

func (m *GetDependenciesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDependenciesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetDependenciesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	n1, err1 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.EndTime, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.EndTime):])
	if err1 != nil {
		return 0, err1
	}
	i -= n1
	i = encodeVarintStorage(dAtA, i, uint64(n1))
	i--
	dAtA[i] = 0x12
	n2, err2 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.StartTime, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTime):])
	if err2 != nil {
		return 0, err2
	}
	i -= n2
	i = encodeVarintStorage(dAtA, i, uint64(n2))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *GetDependenciesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDependenciesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetDependenciesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Dependencies) > 0 {
		for iNdEx := len(m.Dependencies) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Dependencies[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintStorage(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *WriteSpanRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteSpanRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteSpanRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Span != nil {
		{
			size, err := m.Span.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorage(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *WriteSpanResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteSpanResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *WriteSpanResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	return len(dAtA) - i, nil
}

func (m *CloseWriterRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CloseWriterRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CloseWriterRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	return len(dAtA) - i, nil
}

func (m *CloseWriterResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CloseWriterResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CloseWriterResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	return len(dAtA) - i, nil
}

func (m *GetTraceRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTraceRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetTraceRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	n4, err4 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.EndTime, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.EndTime):])
	if err4 != nil {
		return 0, err4
	}
	i -= n4
	i = encodeVarintStorage(dAtA, i, uint64(n4))
	i--
	dAtA[i] = 0x1a
	n5, err5 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.StartTime, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTime):])
	if err5 != nil {
		return 0, err5
	}
	i -= n5
	i = encodeVarintStorage(dAtA, i, uint64(n5))
	i--
	dAtA[i] = 0x12
	{
		size := m.TraceID.Size()
		i -= size
		if _, err := m.TraceID.MarshalTo(dAtA[i:]); err != nil {
			return 0, err
		}
		i = encodeVarintStorage(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *GetServicesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetServicesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetServicesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	return len(dAtA) - i, nil
}

func (m *GetServicesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetServicesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetServicesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Services) > 0 {
		for iNdEx := len(m.Services) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Services[iNdEx])
			copy(dAtA[i:], m.Services[iNdEx])
			i = encodeVarintStorage(dAtA, i, uint64(len(m.Services[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetOperationsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOperationsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetOperationsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.SpanKind) > 0 {
		i -= len(m.SpanKind)
		copy(dAtA[i:], m.SpanKind)
		i = encodeVarintStorage(dAtA, i, uint64(len(m.SpanKind)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Service) > 0 {
		i -= len(m.Service)
		copy(dAtA[i:], m.Service)
		i = encodeVarintStorage(dAtA, i, uint64(len(m.Service)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Operation) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Operation) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Operation) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.SpanKind) > 0 {
		i -= len(m.SpanKind)
		copy(dAtA[i:], m.SpanKind)
		i = encodeVarintStorage(dAtA, i, uint64(len(m.SpanKind)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintStorage(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetOperationsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOperationsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetOperationsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Operations) > 0 {
		for iNdEx := len(m.Operations) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Operations[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintStorage(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.OperationNames) > 0 {
		for iNdEx := len(m.OperationNames) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.OperationNames[iNdEx])
			copy(dAtA[i:], m.OperationNames[iNdEx])
			i = encodeVarintStorage(dAtA, i, uint64(len(m.OperationNames[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *TraceQueryParameters) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TraceQueryParameters) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TraceQueryParameters) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.NumTraces != 0 {
		i = encodeVarintStorage(dAtA, i, uint64(m.NumTraces))
		i--
		dAtA[i] = 0x40
	}
	n6, err6 := github_com_gogo_protobuf_types.StdDurationMarshalTo(m.DurationMax, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdDuration(m.DurationMax):])
	if err6 != nil {
		return 0, err6
	}
	i -= n6
	i = encodeVarintStorage(dAtA, i, uint64(n6))
	i--
	dAtA[i] = 0x3a
	n7, err7 := github_com_gogo_protobuf_types.StdDurationMarshalTo(m.DurationMin, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdDuration(m.DurationMin):])
	if err7 != nil {
		return 0, err7
	}
	i -= n7
	i = encodeVarintStorage(dAtA, i, uint64(n7))
	i--
	dAtA[i] = 0x32
	n8, err8 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.StartTimeMax, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTimeMax):])
	if err8 != nil {
		return 0, err8
	}
	i -= n8
	i = encodeVarintStorage(dAtA, i, uint64(n8))
	i--
	dAtA[i] = 0x2a
	n9, err9 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.StartTimeMin, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTimeMin):])
	if err9 != nil {
		return 0, err9
	}
	i -= n9
	i = encodeVarintStorage(dAtA, i, uint64(n9))
	i--
	dAtA[i] = 0x22
	if len(m.Tags) > 0 {
		for k := range m.Tags {
			v := m.Tags[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintStorage(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintStorage(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintStorage(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.OperationName) > 0 {
		i -= len(m.OperationName)
		copy(dAtA[i:], m.OperationName)
		i = encodeVarintStorage(dAtA, i, uint64(len(m.OperationName)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.ServiceName) > 0 {
		i -= len(m.ServiceName)
		copy(dAtA[i:], m.ServiceName)
		i = encodeVarintStorage(dAtA, i, uint64(len(m.ServiceName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *FindTracesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindTracesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FindTracesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Query != nil {
		{
			size, err := m.Query.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorage(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SpansResponseChunk) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SpansResponseChunk) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SpansResponseChunk) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Spans) > 0 {
		for iNdEx := len(m.Spans) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Spans[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintStorage(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *FindTraceIDsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindTraceIDsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FindTraceIDsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Query != nil {
		{
			size, err := m.Query.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorage(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *FindTraceIDsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FindTraceIDsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FindTraceIDsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.TraceIDs) > 0 {
		for iNdEx := len(m.TraceIDs) - 1; iNdEx >= 0; iNdEx-- {
			{
				size := m.TraceIDs[iNdEx].Size()
				i -= size
				if _, err := m.TraceIDs[iNdEx].MarshalTo(dAtA[i:]); err != nil {
					return 0, err
				}
				i = encodeVarintStorage(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *CapabilitiesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CapabilitiesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CapabilitiesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	return len(dAtA) - i, nil
}

func (m *CapabilitiesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CapabilitiesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CapabilitiesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.StreamingSpanWriter {
		i--
		if m.StreamingSpanWriter {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x18
	}
	if m.ArchiveSpanWriter {
		i--
		if m.ArchiveSpanWriter {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if m.ArchiveSpanReader {
		i--
		if m.ArchiveSpanReader {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintStorage(dAtA []byte, offset int, v uint64) int {
	offset -= sovStorage(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *GetDependenciesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTime)
	n += 1 + l + sovStorage(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.EndTime)
	n += 1 + l + sovStorage(uint64(l))
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetDependenciesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Dependencies) > 0 {
		for _, e := range m.Dependencies {
			l = e.Size()
			n += 1 + l + sovStorage(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *WriteSpanRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Span != nil {
		l = m.Span.Size()
		n += 1 + l + sovStorage(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *WriteSpanResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CloseWriterRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CloseWriterResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetTraceRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.TraceID.Size()
	n += 1 + l + sovStorage(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTime)
	n += 1 + l + sovStorage(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.EndTime)
	n += 1 + l + sovStorage(uint64(l))
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetServicesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetServicesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Services) > 0 {
		for _, s := range m.Services {
			l = len(s)
			n += 1 + l + sovStorage(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetOperationsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Service)
	if l > 0 {
		n += 1 + l + sovStorage(uint64(l))
	}
	l = len(m.SpanKind)
	if l > 0 {
		n += 1 + l + sovStorage(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Operation) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovStorage(uint64(l))
	}
	l = len(m.SpanKind)
	if l > 0 {
		n += 1 + l + sovStorage(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetOperationsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.OperationNames) > 0 {
		for _, s := range m.OperationNames {
			l = len(s)
			n += 1 + l + sovStorage(uint64(l))
		}
	}
	if len(m.Operations) > 0 {
		for _, e := range m.Operations {
			l = e.Size()
			n += 1 + l + sovStorage(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *TraceQueryParameters) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ServiceName)
	if l > 0 {
		n += 1 + l + sovStorage(uint64(l))
	}
	l = len(m.OperationName)
	if l > 0 {
		n += 1 + l + sovStorage(uint64(l))
	}
	if len(m.Tags) > 0 {
		for k, v := range m.Tags {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovStorage(uint64(len(k))) + 1 + len(v) + sovStorage(uint64(len(v)))
			n += mapEntrySize + 1 + sovStorage(uint64(mapEntrySize))
		}
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTimeMin)
	n += 1 + l + sovStorage(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.StartTimeMax)
	n += 1 + l + sovStorage(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdDuration(m.DurationMin)
	n += 1 + l + sovStorage(uint64(l))
	l = github_com_gogo_protobuf_types.SizeOfStdDuration(m.DurationMax)
	n += 1 + l + sovStorage(uint64(l))
	if m.NumTraces != 0 {
		n += 1 + sovStorage(uint64(m.NumTraces))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *FindTracesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Query != nil {
		l = m.Query.Size()
		n += 1 + l + sovStorage(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SpansResponseChunk) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Spans) > 0 {
		for _, e := range m.Spans {
			l = e.Size()
			n += 1 + l + sovStorage(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *FindTraceIDsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Query != nil {
		l = m.Query.Size()
		n += 1 + l + sovStorage(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *FindTraceIDsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.TraceIDs) > 0 {
		for _, e := range m.TraceIDs {
			l = e.Size()
			n += 1 + l + sovStorage(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CapabilitiesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CapabilitiesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ArchiveSpanReader {
		n += 2
	}
	if m.ArchiveSpanWriter {
		n += 2
	}
	if m.StreamingSpanWriter {
		n += 2
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovStorage(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozStorage(x uint64) (n int) {
	return sovStorage(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *GetDependenciesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetDependenciesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetDependenciesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.StartTime, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.EndTime, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDependenciesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetDependenciesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetDependenciesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Dependencies", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Dependencies = append(m.Dependencies, v1.DependencyLink{})
			if err := m.Dependencies[len(m.Dependencies)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteSpanRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WriteSpanRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WriteSpanRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Span", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Span == nil {
				m.Span = &v1.Span{}
			}
			if err := m.Span.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteSpanResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: WriteSpanResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: WriteSpanResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CloseWriterRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CloseWriterRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CloseWriterRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CloseWriterResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CloseWriterResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CloseWriterResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTraceRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetTraceRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetTraceRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TraceID", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.TraceID.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.StartTime, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.EndTime, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetServicesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetServicesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetServicesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetServicesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetServicesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetServicesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Services", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Services = append(m.Services, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOperationsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetOperationsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetOperationsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Service", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Service = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpanKind", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SpanKind = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Operation) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Operation: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Operation: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpanKind", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SpanKind = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOperationsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetOperationsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetOperationsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OperationNames", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OperationNames = append(m.OperationNames, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Operations", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Operations = append(m.Operations, &Operation{})
			if err := m.Operations[len(m.Operations)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TraceQueryParameters) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TraceQueryParameters: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TraceQueryParameters: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ServiceName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ServiceName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OperationName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OperationName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tags", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Tags == nil {
				m.Tags = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStorage
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowStorage
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthStorage
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthStorage
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowStorage
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthStorage
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthStorage
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipStorage(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthStorage
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Tags[mapkey] = mapvalue
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTimeMin", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.StartTimeMin, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTimeMax", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.StartTimeMax, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DurationMin", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdDurationUnmarshal(&m.DurationMin, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DurationMax", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdDurationUnmarshal(&m.DurationMax, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NumTraces", wireType)
			}
			m.NumTraces = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NumTraces |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindTracesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FindTracesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FindTracesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Query", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Query == nil {
				m.Query = &TraceQueryParameters{}
			}
			if err := m.Query.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SpansResponseChunk) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SpansResponseChunk: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SpansResponseChunk: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Spans", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Spans = append(m.Spans, v1.Span{})
			if err := m.Spans[len(m.Spans)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindTraceIDsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FindTraceIDsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FindTraceIDsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Query", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Query == nil {
				m.Query = &TraceQueryParameters{}
			}
			if err := m.Query.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FindTraceIDsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FindTraceIDsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FindTraceIDsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TraceIDs", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthStorage
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthStorage
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			var v github_com_jaegertracing_jaeger_idl_model_v1.TraceID
			m.TraceIDs = append(m.TraceIDs, v)
			if err := m.TraceIDs[len(m.TraceIDs)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CapabilitiesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CapabilitiesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CapabilitiesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CapabilitiesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CapabilitiesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CapabilitiesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ArchiveSpanReader", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ArchiveSpanReader = bool(v != 0)
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ArchiveSpanWriter", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ArchiveSpanWriter = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StreamingSpanWriter", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.StreamingSpanWriter = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipStorage(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorage
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipStorage(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowStorage
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowStorage
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthStorage
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupStorage
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthStorage
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthStorage        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowStorage          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupStorage = fmt.Errorf("proto: unexpected end of group")
)
