// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify
// Copyright (c) The Jaeger Authors.
// SPDX-License-Identifier: Apache-2.0
//
// Run 'make generate-mocks' to regenerate.

package mocks

import (
	"context"

	"github.com/jaegertracing/jaeger/internal/proto-gen/storage_v1"
	mock "github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

// NewSpanWriterPluginClient creates a new instance of SpanWriterPluginClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSpanWriterPluginClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *SpanWriterPluginClient {
	mock := &SpanWriterPluginClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SpanWriterPluginClient is an autogenerated mock type for the SpanWriterPluginClient type
type SpanWriterPluginClient struct {
	mock.Mock
}

type SpanWriterPluginClient_Expecter struct {
	mock *mock.Mock
}

func (_m *SpanWriterPluginClient) EXPECT() *SpanWriterPluginClient_Expecter {
	return &SpanWriterPluginClient_Expecter{mock: &_m.Mock}
}

// Close provides a mock function for the type SpanWriterPluginClient
func (_mock *SpanWriterPluginClient) Close(ctx context.Context, in *storage_v1.CloseWriterRequest, opts ...grpc.CallOption) (*storage_v1.CloseWriterResponse, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, in, opts)
	} else {
		tmpRet = _mock.Called(ctx, in)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 *storage_v1.CloseWriterResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.CloseWriterRequest, ...grpc.CallOption) (*storage_v1.CloseWriterResponse, error)); ok {
		return returnFunc(ctx, in, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.CloseWriterRequest, ...grpc.CallOption) *storage_v1.CloseWriterResponse); ok {
		r0 = returnFunc(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.CloseWriterResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.CloseWriterRequest, ...grpc.CallOption) error); ok {
		r1 = returnFunc(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanWriterPluginClient_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type SpanWriterPluginClient_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
//   - ctx context.Context
//   - in *storage_v1.CloseWriterRequest
//   - opts ...grpc.CallOption
func (_e *SpanWriterPluginClient_Expecter) Close(ctx interface{}, in interface{}, opts ...interface{}) *SpanWriterPluginClient_Close_Call {
	return &SpanWriterPluginClient_Close_Call{Call: _e.mock.On("Close",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *SpanWriterPluginClient_Close_Call) Run(run func(ctx context.Context, in *storage_v1.CloseWriterRequest, opts ...grpc.CallOption)) *SpanWriterPluginClient_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.CloseWriterRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.CloseWriterRequest)
		}
		var arg2 []grpc.CallOption
		var variadicArgs []grpc.CallOption
		if len(args) > 2 {
			variadicArgs = args[2].([]grpc.CallOption)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *SpanWriterPluginClient_Close_Call) Return(closeWriterResponse *storage_v1.CloseWriterResponse, err error) *SpanWriterPluginClient_Close_Call {
	_c.Call.Return(closeWriterResponse, err)
	return _c
}

func (_c *SpanWriterPluginClient_Close_Call) RunAndReturn(run func(ctx context.Context, in *storage_v1.CloseWriterRequest, opts ...grpc.CallOption) (*storage_v1.CloseWriterResponse, error)) *SpanWriterPluginClient_Close_Call {
	_c.Call.Return(run)
	return _c
}

// WriteSpan provides a mock function for the type SpanWriterPluginClient
func (_mock *SpanWriterPluginClient) WriteSpan(ctx context.Context, in *storage_v1.WriteSpanRequest, opts ...grpc.CallOption) (*storage_v1.WriteSpanResponse, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, in, opts)
	} else {
		tmpRet = _mock.Called(ctx, in)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for WriteSpan")
	}

	var r0 *storage_v1.WriteSpanResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.WriteSpanRequest, ...grpc.CallOption) (*storage_v1.WriteSpanResponse, error)); ok {
		return returnFunc(ctx, in, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.WriteSpanRequest, ...grpc.CallOption) *storage_v1.WriteSpanResponse); ok {
		r0 = returnFunc(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.WriteSpanResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.WriteSpanRequest, ...grpc.CallOption) error); ok {
		r1 = returnFunc(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanWriterPluginClient_WriteSpan_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WriteSpan'
type SpanWriterPluginClient_WriteSpan_Call struct {
	*mock.Call
}

// WriteSpan is a helper method to define mock.On call
//   - ctx context.Context
//   - in *storage_v1.WriteSpanRequest
//   - opts ...grpc.CallOption
func (_e *SpanWriterPluginClient_Expecter) WriteSpan(ctx interface{}, in interface{}, opts ...interface{}) *SpanWriterPluginClient_WriteSpan_Call {
	return &SpanWriterPluginClient_WriteSpan_Call{Call: _e.mock.On("WriteSpan",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *SpanWriterPluginClient_WriteSpan_Call) Run(run func(ctx context.Context, in *storage_v1.WriteSpanRequest, opts ...grpc.CallOption)) *SpanWriterPluginClient_WriteSpan_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.WriteSpanRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.WriteSpanRequest)
		}
		var arg2 []grpc.CallOption
		var variadicArgs []grpc.CallOption
		if len(args) > 2 {
			variadicArgs = args[2].([]grpc.CallOption)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *SpanWriterPluginClient_WriteSpan_Call) Return(writeSpanResponse *storage_v1.WriteSpanResponse, err error) *SpanWriterPluginClient_WriteSpan_Call {
	_c.Call.Return(writeSpanResponse, err)
	return _c
}

func (_c *SpanWriterPluginClient_WriteSpan_Call) RunAndReturn(run func(ctx context.Context, in *storage_v1.WriteSpanRequest, opts ...grpc.CallOption) (*storage_v1.WriteSpanResponse, error)) *SpanWriterPluginClient_WriteSpan_Call {
	_c.Call.Return(run)
	return _c
}

// NewSpanWriterPluginServer creates a new instance of SpanWriterPluginServer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSpanWriterPluginServer(t interface {
	mock.TestingT
	Cleanup(func())
}) *SpanWriterPluginServer {
	mock := &SpanWriterPluginServer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SpanWriterPluginServer is an autogenerated mock type for the SpanWriterPluginServer type
type SpanWriterPluginServer struct {
	mock.Mock
}

type SpanWriterPluginServer_Expecter struct {
	mock *mock.Mock
}

func (_m *SpanWriterPluginServer) EXPECT() *SpanWriterPluginServer_Expecter {
	return &SpanWriterPluginServer_Expecter{mock: &_m.Mock}
}

// Close provides a mock function for the type SpanWriterPluginServer
func (_mock *SpanWriterPluginServer) Close(context1 context.Context, closeWriterRequest *storage_v1.CloseWriterRequest) (*storage_v1.CloseWriterResponse, error) {
	ret := _mock.Called(context1, closeWriterRequest)

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 *storage_v1.CloseWriterResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.CloseWriterRequest) (*storage_v1.CloseWriterResponse, error)); ok {
		return returnFunc(context1, closeWriterRequest)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.CloseWriterRequest) *storage_v1.CloseWriterResponse); ok {
		r0 = returnFunc(context1, closeWriterRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.CloseWriterResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.CloseWriterRequest) error); ok {
		r1 = returnFunc(context1, closeWriterRequest)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanWriterPluginServer_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type SpanWriterPluginServer_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
//   - context1 context.Context
//   - closeWriterRequest *storage_v1.CloseWriterRequest
func (_e *SpanWriterPluginServer_Expecter) Close(context1 interface{}, closeWriterRequest interface{}) *SpanWriterPluginServer_Close_Call {
	return &SpanWriterPluginServer_Close_Call{Call: _e.mock.On("Close", context1, closeWriterRequest)}
}

func (_c *SpanWriterPluginServer_Close_Call) Run(run func(context1 context.Context, closeWriterRequest *storage_v1.CloseWriterRequest)) *SpanWriterPluginServer_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.CloseWriterRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.CloseWriterRequest)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *SpanWriterPluginServer_Close_Call) Return(closeWriterResponse *storage_v1.CloseWriterResponse, err error) *SpanWriterPluginServer_Close_Call {
	_c.Call.Return(closeWriterResponse, err)
	return _c
}

func (_c *SpanWriterPluginServer_Close_Call) RunAndReturn(run func(context1 context.Context, closeWriterRequest *storage_v1.CloseWriterRequest) (*storage_v1.CloseWriterResponse, error)) *SpanWriterPluginServer_Close_Call {
	_c.Call.Return(run)
	return _c
}

// WriteSpan provides a mock function for the type SpanWriterPluginServer
func (_mock *SpanWriterPluginServer) WriteSpan(context1 context.Context, writeSpanRequest *storage_v1.WriteSpanRequest) (*storage_v1.WriteSpanResponse, error) {
	ret := _mock.Called(context1, writeSpanRequest)

	if len(ret) == 0 {
		panic("no return value specified for WriteSpan")
	}

	var r0 *storage_v1.WriteSpanResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.WriteSpanRequest) (*storage_v1.WriteSpanResponse, error)); ok {
		return returnFunc(context1, writeSpanRequest)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.WriteSpanRequest) *storage_v1.WriteSpanResponse); ok {
		r0 = returnFunc(context1, writeSpanRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.WriteSpanResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.WriteSpanRequest) error); ok {
		r1 = returnFunc(context1, writeSpanRequest)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanWriterPluginServer_WriteSpan_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WriteSpan'
type SpanWriterPluginServer_WriteSpan_Call struct {
	*mock.Call
}

// WriteSpan is a helper method to define mock.On call
//   - context1 context.Context
//   - writeSpanRequest *storage_v1.WriteSpanRequest
func (_e *SpanWriterPluginServer_Expecter) WriteSpan(context1 interface{}, writeSpanRequest interface{}) *SpanWriterPluginServer_WriteSpan_Call {
	return &SpanWriterPluginServer_WriteSpan_Call{Call: _e.mock.On("WriteSpan", context1, writeSpanRequest)}
}

func (_c *SpanWriterPluginServer_WriteSpan_Call) Run(run func(context1 context.Context, writeSpanRequest *storage_v1.WriteSpanRequest)) *SpanWriterPluginServer_WriteSpan_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.WriteSpanRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.WriteSpanRequest)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *SpanWriterPluginServer_WriteSpan_Call) Return(writeSpanResponse *storage_v1.WriteSpanResponse, err error) *SpanWriterPluginServer_WriteSpan_Call {
	_c.Call.Return(writeSpanResponse, err)
	return _c
}

func (_c *SpanWriterPluginServer_WriteSpan_Call) RunAndReturn(run func(context1 context.Context, writeSpanRequest *storage_v1.WriteSpanRequest) (*storage_v1.WriteSpanResponse, error)) *SpanWriterPluginServer_WriteSpan_Call {
	_c.Call.Return(run)
	return _c
}

// NewStreamingSpanWriterPluginClient creates a new instance of StreamingSpanWriterPluginClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewStreamingSpanWriterPluginClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *StreamingSpanWriterPluginClient {
	mock := &StreamingSpanWriterPluginClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// StreamingSpanWriterPluginClient is an autogenerated mock type for the StreamingSpanWriterPluginClient type
type StreamingSpanWriterPluginClient struct {
	mock.Mock
}

type StreamingSpanWriterPluginClient_Expecter struct {
	mock *mock.Mock
}

func (_m *StreamingSpanWriterPluginClient) EXPECT() *StreamingSpanWriterPluginClient_Expecter {
	return &StreamingSpanWriterPluginClient_Expecter{mock: &_m.Mock}
}

// WriteSpanStream provides a mock function for the type StreamingSpanWriterPluginClient
func (_mock *StreamingSpanWriterPluginClient) WriteSpanStream(ctx context.Context, opts ...grpc.CallOption) (storage_v1.StreamingSpanWriterPlugin_WriteSpanStreamClient, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, opts)
	} else {
		tmpRet = _mock.Called(ctx)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for WriteSpanStream")
	}

	var r0 storage_v1.StreamingSpanWriterPlugin_WriteSpanStreamClient
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, ...grpc.CallOption) (storage_v1.StreamingSpanWriterPlugin_WriteSpanStreamClient, error)); ok {
		return returnFunc(ctx, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, ...grpc.CallOption) storage_v1.StreamingSpanWriterPlugin_WriteSpanStreamClient); ok {
		r0 = returnFunc(ctx, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(storage_v1.StreamingSpanWriterPlugin_WriteSpanStreamClient)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, ...grpc.CallOption) error); ok {
		r1 = returnFunc(ctx, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// StreamingSpanWriterPluginClient_WriteSpanStream_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WriteSpanStream'
type StreamingSpanWriterPluginClient_WriteSpanStream_Call struct {
	*mock.Call
}

// WriteSpanStream is a helper method to define mock.On call
//   - ctx context.Context
//   - opts ...grpc.CallOption
func (_e *StreamingSpanWriterPluginClient_Expecter) WriteSpanStream(ctx interface{}, opts ...interface{}) *StreamingSpanWriterPluginClient_WriteSpanStream_Call {
	return &StreamingSpanWriterPluginClient_WriteSpanStream_Call{Call: _e.mock.On("WriteSpanStream",
		append([]interface{}{ctx}, opts...)...)}
}

func (_c *StreamingSpanWriterPluginClient_WriteSpanStream_Call) Run(run func(ctx context.Context, opts ...grpc.CallOption)) *StreamingSpanWriterPluginClient_WriteSpanStream_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []grpc.CallOption
		var variadicArgs []grpc.CallOption
		if len(args) > 1 {
			variadicArgs = args[1].([]grpc.CallOption)
		}
		arg1 = variadicArgs
		run(
			arg0,
			arg1...,
		)
	})
	return _c
}

func (_c *StreamingSpanWriterPluginClient_WriteSpanStream_Call) Return(streamingSpanWriterPlugin_WriteSpanStreamClient storage_v1.StreamingSpanWriterPlugin_WriteSpanStreamClient, err error) *StreamingSpanWriterPluginClient_WriteSpanStream_Call {
	_c.Call.Return(streamingSpanWriterPlugin_WriteSpanStreamClient, err)
	return _c
}

func (_c *StreamingSpanWriterPluginClient_WriteSpanStream_Call) RunAndReturn(run func(ctx context.Context, opts ...grpc.CallOption) (storage_v1.StreamingSpanWriterPlugin_WriteSpanStreamClient, error)) *StreamingSpanWriterPluginClient_WriteSpanStream_Call {
	_c.Call.Return(run)
	return _c
}

// NewStreamingSpanWriterPlugin_WriteSpanStreamClient creates a new instance of StreamingSpanWriterPlugin_WriteSpanStreamClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewStreamingSpanWriterPlugin_WriteSpanStreamClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *StreamingSpanWriterPlugin_WriteSpanStreamClient {
	mock := &StreamingSpanWriterPlugin_WriteSpanStreamClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// StreamingSpanWriterPlugin_WriteSpanStreamClient is an autogenerated mock type for the StreamingSpanWriterPlugin_WriteSpanStreamClient type
type StreamingSpanWriterPlugin_WriteSpanStreamClient struct {
	mock.Mock
}

type StreamingSpanWriterPlugin_WriteSpanStreamClient_Expecter struct {
	mock *mock.Mock
}

func (_m *StreamingSpanWriterPlugin_WriteSpanStreamClient) EXPECT() *StreamingSpanWriterPlugin_WriteSpanStreamClient_Expecter {
	return &StreamingSpanWriterPlugin_WriteSpanStreamClient_Expecter{mock: &_m.Mock}
}

// CloseAndRecv provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamClient
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamClient) CloseAndRecv() (*storage_v1.WriteSpanResponse, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for CloseAndRecv")
	}

	var r0 *storage_v1.WriteSpanResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (*storage_v1.WriteSpanResponse, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() *storage_v1.WriteSpanResponse); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.WriteSpanResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseAndRecv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CloseAndRecv'
type StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseAndRecv_Call struct {
	*mock.Call
}

// CloseAndRecv is a helper method to define mock.On call
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamClient_Expecter) CloseAndRecv() *StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseAndRecv_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseAndRecv_Call{Call: _e.mock.On("CloseAndRecv")}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseAndRecv_Call) Run(run func()) *StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseAndRecv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseAndRecv_Call) Return(writeSpanResponse *storage_v1.WriteSpanResponse, err error) *StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseAndRecv_Call {
	_c.Call.Return(writeSpanResponse, err)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseAndRecv_Call) RunAndReturn(run func() (*storage_v1.WriteSpanResponse, error)) *StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseAndRecv_Call {
	_c.Call.Return(run)
	return _c
}

// CloseSend provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamClient
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamClient) CloseSend() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for CloseSend")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseSend_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CloseSend'
type StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseSend_Call struct {
	*mock.Call
}

// CloseSend is a helper method to define mock.On call
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamClient_Expecter) CloseSend() *StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseSend_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseSend_Call{Call: _e.mock.On("CloseSend")}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseSend_Call) Run(run func()) *StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseSend_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseSend_Call) Return(err error) *StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseSend_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseSend_Call) RunAndReturn(run func() error) *StreamingSpanWriterPlugin_WriteSpanStreamClient_CloseSend_Call {
	_c.Call.Return(run)
	return _c
}

// Context provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamClient
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamClient) Context() context.Context {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Context")
	}

	var r0 context.Context
	if returnFunc, ok := ret.Get(0).(func() context.Context); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(context.Context)
		}
	}
	return r0
}

// StreamingSpanWriterPlugin_WriteSpanStreamClient_Context_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Context'
type StreamingSpanWriterPlugin_WriteSpanStreamClient_Context_Call struct {
	*mock.Call
}

// Context is a helper method to define mock.On call
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamClient_Expecter) Context() *StreamingSpanWriterPlugin_WriteSpanStreamClient_Context_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamClient_Context_Call{Call: _e.mock.On("Context")}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_Context_Call) Run(run func()) *StreamingSpanWriterPlugin_WriteSpanStreamClient_Context_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_Context_Call) Return(context1 context.Context) *StreamingSpanWriterPlugin_WriteSpanStreamClient_Context_Call {
	_c.Call.Return(context1)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_Context_Call) RunAndReturn(run func() context.Context) *StreamingSpanWriterPlugin_WriteSpanStreamClient_Context_Call {
	_c.Call.Return(run)
	return _c
}

// Header provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamClient
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamClient) Header() (metadata.MD, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Header")
	}

	var r0 metadata.MD
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (metadata.MD, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() metadata.MD); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(metadata.MD)
		}
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// StreamingSpanWriterPlugin_WriteSpanStreamClient_Header_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Header'
type StreamingSpanWriterPlugin_WriteSpanStreamClient_Header_Call struct {
	*mock.Call
}

// Header is a helper method to define mock.On call
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamClient_Expecter) Header() *StreamingSpanWriterPlugin_WriteSpanStreamClient_Header_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamClient_Header_Call{Call: _e.mock.On("Header")}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_Header_Call) Run(run func()) *StreamingSpanWriterPlugin_WriteSpanStreamClient_Header_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_Header_Call) Return(mD metadata.MD, err error) *StreamingSpanWriterPlugin_WriteSpanStreamClient_Header_Call {
	_c.Call.Return(mD, err)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_Header_Call) RunAndReturn(run func() (metadata.MD, error)) *StreamingSpanWriterPlugin_WriteSpanStreamClient_Header_Call {
	_c.Call.Return(run)
	return _c
}

// RecvMsg provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamClient
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamClient) RecvMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for RecvMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// StreamingSpanWriterPlugin_WriteSpanStreamClient_RecvMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecvMsg'
type StreamingSpanWriterPlugin_WriteSpanStreamClient_RecvMsg_Call struct {
	*mock.Call
}

// RecvMsg is a helper method to define mock.On call
//   - m any
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamClient_Expecter) RecvMsg(m interface{}) *StreamingSpanWriterPlugin_WriteSpanStreamClient_RecvMsg_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamClient_RecvMsg_Call{Call: _e.mock.On("RecvMsg", m)}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_RecvMsg_Call) Run(run func(m any)) *StreamingSpanWriterPlugin_WriteSpanStreamClient_RecvMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_RecvMsg_Call) Return(err error) *StreamingSpanWriterPlugin_WriteSpanStreamClient_RecvMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_RecvMsg_Call) RunAndReturn(run func(m any) error) *StreamingSpanWriterPlugin_WriteSpanStreamClient_RecvMsg_Call {
	_c.Call.Return(run)
	return _c
}

// Send provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamClient
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamClient) Send(writeSpanRequest *storage_v1.WriteSpanRequest) error {
	ret := _mock.Called(writeSpanRequest)

	if len(ret) == 0 {
		panic("no return value specified for Send")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(*storage_v1.WriteSpanRequest) error); ok {
		r0 = returnFunc(writeSpanRequest)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// StreamingSpanWriterPlugin_WriteSpanStreamClient_Send_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Send'
type StreamingSpanWriterPlugin_WriteSpanStreamClient_Send_Call struct {
	*mock.Call
}

// Send is a helper method to define mock.On call
//   - writeSpanRequest *storage_v1.WriteSpanRequest
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamClient_Expecter) Send(writeSpanRequest interface{}) *StreamingSpanWriterPlugin_WriteSpanStreamClient_Send_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamClient_Send_Call{Call: _e.mock.On("Send", writeSpanRequest)}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_Send_Call) Run(run func(writeSpanRequest *storage_v1.WriteSpanRequest)) *StreamingSpanWriterPlugin_WriteSpanStreamClient_Send_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *storage_v1.WriteSpanRequest
		if args[0] != nil {
			arg0 = args[0].(*storage_v1.WriteSpanRequest)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_Send_Call) Return(err error) *StreamingSpanWriterPlugin_WriteSpanStreamClient_Send_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_Send_Call) RunAndReturn(run func(writeSpanRequest *storage_v1.WriteSpanRequest) error) *StreamingSpanWriterPlugin_WriteSpanStreamClient_Send_Call {
	_c.Call.Return(run)
	return _c
}

// SendMsg provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamClient
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamClient) SendMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for SendMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// StreamingSpanWriterPlugin_WriteSpanStreamClient_SendMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendMsg'
type StreamingSpanWriterPlugin_WriteSpanStreamClient_SendMsg_Call struct {
	*mock.Call
}

// SendMsg is a helper method to define mock.On call
//   - m any
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamClient_Expecter) SendMsg(m interface{}) *StreamingSpanWriterPlugin_WriteSpanStreamClient_SendMsg_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamClient_SendMsg_Call{Call: _e.mock.On("SendMsg", m)}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_SendMsg_Call) Run(run func(m any)) *StreamingSpanWriterPlugin_WriteSpanStreamClient_SendMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_SendMsg_Call) Return(err error) *StreamingSpanWriterPlugin_WriteSpanStreamClient_SendMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_SendMsg_Call) RunAndReturn(run func(m any) error) *StreamingSpanWriterPlugin_WriteSpanStreamClient_SendMsg_Call {
	_c.Call.Return(run)
	return _c
}

// Trailer provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamClient
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamClient) Trailer() metadata.MD {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Trailer")
	}

	var r0 metadata.MD
	if returnFunc, ok := ret.Get(0).(func() metadata.MD); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(metadata.MD)
		}
	}
	return r0
}

// StreamingSpanWriterPlugin_WriteSpanStreamClient_Trailer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Trailer'
type StreamingSpanWriterPlugin_WriteSpanStreamClient_Trailer_Call struct {
	*mock.Call
}

// Trailer is a helper method to define mock.On call
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamClient_Expecter) Trailer() *StreamingSpanWriterPlugin_WriteSpanStreamClient_Trailer_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamClient_Trailer_Call{Call: _e.mock.On("Trailer")}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_Trailer_Call) Run(run func()) *StreamingSpanWriterPlugin_WriteSpanStreamClient_Trailer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_Trailer_Call) Return(mD metadata.MD) *StreamingSpanWriterPlugin_WriteSpanStreamClient_Trailer_Call {
	_c.Call.Return(mD)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamClient_Trailer_Call) RunAndReturn(run func() metadata.MD) *StreamingSpanWriterPlugin_WriteSpanStreamClient_Trailer_Call {
	_c.Call.Return(run)
	return _c
}

// NewStreamingSpanWriterPluginServer creates a new instance of StreamingSpanWriterPluginServer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewStreamingSpanWriterPluginServer(t interface {
	mock.TestingT
	Cleanup(func())
}) *StreamingSpanWriterPluginServer {
	mock := &StreamingSpanWriterPluginServer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// StreamingSpanWriterPluginServer is an autogenerated mock type for the StreamingSpanWriterPluginServer type
type StreamingSpanWriterPluginServer struct {
	mock.Mock
}

type StreamingSpanWriterPluginServer_Expecter struct {
	mock *mock.Mock
}

func (_m *StreamingSpanWriterPluginServer) EXPECT() *StreamingSpanWriterPluginServer_Expecter {
	return &StreamingSpanWriterPluginServer_Expecter{mock: &_m.Mock}
}

// WriteSpanStream provides a mock function for the type StreamingSpanWriterPluginServer
func (_mock *StreamingSpanWriterPluginServer) WriteSpanStream(streamingSpanWriterPlugin_WriteSpanStreamServer storage_v1.StreamingSpanWriterPlugin_WriteSpanStreamServer) error {
	ret := _mock.Called(streamingSpanWriterPlugin_WriteSpanStreamServer)

	if len(ret) == 0 {
		panic("no return value specified for WriteSpanStream")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(storage_v1.StreamingSpanWriterPlugin_WriteSpanStreamServer) error); ok {
		r0 = returnFunc(streamingSpanWriterPlugin_WriteSpanStreamServer)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// StreamingSpanWriterPluginServer_WriteSpanStream_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WriteSpanStream'
type StreamingSpanWriterPluginServer_WriteSpanStream_Call struct {
	*mock.Call
}

// WriteSpanStream is a helper method to define mock.On call
//   - streamingSpanWriterPlugin_WriteSpanStreamServer storage_v1.StreamingSpanWriterPlugin_WriteSpanStreamServer
func (_e *StreamingSpanWriterPluginServer_Expecter) WriteSpanStream(streamingSpanWriterPlugin_WriteSpanStreamServer interface{}) *StreamingSpanWriterPluginServer_WriteSpanStream_Call {
	return &StreamingSpanWriterPluginServer_WriteSpanStream_Call{Call: _e.mock.On("WriteSpanStream", streamingSpanWriterPlugin_WriteSpanStreamServer)}
}

func (_c *StreamingSpanWriterPluginServer_WriteSpanStream_Call) Run(run func(streamingSpanWriterPlugin_WriteSpanStreamServer storage_v1.StreamingSpanWriterPlugin_WriteSpanStreamServer)) *StreamingSpanWriterPluginServer_WriteSpanStream_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 storage_v1.StreamingSpanWriterPlugin_WriteSpanStreamServer
		if args[0] != nil {
			arg0 = args[0].(storage_v1.StreamingSpanWriterPlugin_WriteSpanStreamServer)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *StreamingSpanWriterPluginServer_WriteSpanStream_Call) Return(err error) *StreamingSpanWriterPluginServer_WriteSpanStream_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *StreamingSpanWriterPluginServer_WriteSpanStream_Call) RunAndReturn(run func(streamingSpanWriterPlugin_WriteSpanStreamServer storage_v1.StreamingSpanWriterPlugin_WriteSpanStreamServer) error) *StreamingSpanWriterPluginServer_WriteSpanStream_Call {
	_c.Call.Return(run)
	return _c
}

// NewStreamingSpanWriterPlugin_WriteSpanStreamServer creates a new instance of StreamingSpanWriterPlugin_WriteSpanStreamServer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewStreamingSpanWriterPlugin_WriteSpanStreamServer(t interface {
	mock.TestingT
	Cleanup(func())
}) *StreamingSpanWriterPlugin_WriteSpanStreamServer {
	mock := &StreamingSpanWriterPlugin_WriteSpanStreamServer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// StreamingSpanWriterPlugin_WriteSpanStreamServer is an autogenerated mock type for the StreamingSpanWriterPlugin_WriteSpanStreamServer type
type StreamingSpanWriterPlugin_WriteSpanStreamServer struct {
	mock.Mock
}

type StreamingSpanWriterPlugin_WriteSpanStreamServer_Expecter struct {
	mock *mock.Mock
}

func (_m *StreamingSpanWriterPlugin_WriteSpanStreamServer) EXPECT() *StreamingSpanWriterPlugin_WriteSpanStreamServer_Expecter {
	return &StreamingSpanWriterPlugin_WriteSpanStreamServer_Expecter{mock: &_m.Mock}
}

// Context provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamServer
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamServer) Context() context.Context {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Context")
	}

	var r0 context.Context
	if returnFunc, ok := ret.Get(0).(func() context.Context); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(context.Context)
		}
	}
	return r0
}

// StreamingSpanWriterPlugin_WriteSpanStreamServer_Context_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Context'
type StreamingSpanWriterPlugin_WriteSpanStreamServer_Context_Call struct {
	*mock.Call
}

// Context is a helper method to define mock.On call
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamServer_Expecter) Context() *StreamingSpanWriterPlugin_WriteSpanStreamServer_Context_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamServer_Context_Call{Call: _e.mock.On("Context")}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_Context_Call) Run(run func()) *StreamingSpanWriterPlugin_WriteSpanStreamServer_Context_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_Context_Call) Return(context1 context.Context) *StreamingSpanWriterPlugin_WriteSpanStreamServer_Context_Call {
	_c.Call.Return(context1)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_Context_Call) RunAndReturn(run func() context.Context) *StreamingSpanWriterPlugin_WriteSpanStreamServer_Context_Call {
	_c.Call.Return(run)
	return _c
}

// Recv provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamServer
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamServer) Recv() (*storage_v1.WriteSpanRequest, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Recv")
	}

	var r0 *storage_v1.WriteSpanRequest
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (*storage_v1.WriteSpanRequest, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() *storage_v1.WriteSpanRequest); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.WriteSpanRequest)
		}
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// StreamingSpanWriterPlugin_WriteSpanStreamServer_Recv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Recv'
type StreamingSpanWriterPlugin_WriteSpanStreamServer_Recv_Call struct {
	*mock.Call
}

// Recv is a helper method to define mock.On call
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamServer_Expecter) Recv() *StreamingSpanWriterPlugin_WriteSpanStreamServer_Recv_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamServer_Recv_Call{Call: _e.mock.On("Recv")}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_Recv_Call) Run(run func()) *StreamingSpanWriterPlugin_WriteSpanStreamServer_Recv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_Recv_Call) Return(writeSpanRequest *storage_v1.WriteSpanRequest, err error) *StreamingSpanWriterPlugin_WriteSpanStreamServer_Recv_Call {
	_c.Call.Return(writeSpanRequest, err)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_Recv_Call) RunAndReturn(run func() (*storage_v1.WriteSpanRequest, error)) *StreamingSpanWriterPlugin_WriteSpanStreamServer_Recv_Call {
	_c.Call.Return(run)
	return _c
}

// RecvMsg provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamServer
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamServer) RecvMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for RecvMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// StreamingSpanWriterPlugin_WriteSpanStreamServer_RecvMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecvMsg'
type StreamingSpanWriterPlugin_WriteSpanStreamServer_RecvMsg_Call struct {
	*mock.Call
}

// RecvMsg is a helper method to define mock.On call
//   - m any
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamServer_Expecter) RecvMsg(m interface{}) *StreamingSpanWriterPlugin_WriteSpanStreamServer_RecvMsg_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamServer_RecvMsg_Call{Call: _e.mock.On("RecvMsg", m)}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_RecvMsg_Call) Run(run func(m any)) *StreamingSpanWriterPlugin_WriteSpanStreamServer_RecvMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_RecvMsg_Call) Return(err error) *StreamingSpanWriterPlugin_WriteSpanStreamServer_RecvMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_RecvMsg_Call) RunAndReturn(run func(m any) error) *StreamingSpanWriterPlugin_WriteSpanStreamServer_RecvMsg_Call {
	_c.Call.Return(run)
	return _c
}

// SendAndClose provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamServer
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamServer) SendAndClose(writeSpanResponse *storage_v1.WriteSpanResponse) error {
	ret := _mock.Called(writeSpanResponse)

	if len(ret) == 0 {
		panic("no return value specified for SendAndClose")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(*storage_v1.WriteSpanResponse) error); ok {
		r0 = returnFunc(writeSpanResponse)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// StreamingSpanWriterPlugin_WriteSpanStreamServer_SendAndClose_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendAndClose'
type StreamingSpanWriterPlugin_WriteSpanStreamServer_SendAndClose_Call struct {
	*mock.Call
}

// SendAndClose is a helper method to define mock.On call
//   - writeSpanResponse *storage_v1.WriteSpanResponse
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamServer_Expecter) SendAndClose(writeSpanResponse interface{}) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendAndClose_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamServer_SendAndClose_Call{Call: _e.mock.On("SendAndClose", writeSpanResponse)}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendAndClose_Call) Run(run func(writeSpanResponse *storage_v1.WriteSpanResponse)) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendAndClose_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *storage_v1.WriteSpanResponse
		if args[0] != nil {
			arg0 = args[0].(*storage_v1.WriteSpanResponse)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendAndClose_Call) Return(err error) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendAndClose_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendAndClose_Call) RunAndReturn(run func(writeSpanResponse *storage_v1.WriteSpanResponse) error) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendAndClose_Call {
	_c.Call.Return(run)
	return _c
}

// SendHeader provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamServer
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamServer) SendHeader(mD metadata.MD) error {
	ret := _mock.Called(mD)

	if len(ret) == 0 {
		panic("no return value specified for SendHeader")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(metadata.MD) error); ok {
		r0 = returnFunc(mD)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// StreamingSpanWriterPlugin_WriteSpanStreamServer_SendHeader_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendHeader'
type StreamingSpanWriterPlugin_WriteSpanStreamServer_SendHeader_Call struct {
	*mock.Call
}

// SendHeader is a helper method to define mock.On call
//   - mD metadata.MD
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamServer_Expecter) SendHeader(mD interface{}) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendHeader_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamServer_SendHeader_Call{Call: _e.mock.On("SendHeader", mD)}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendHeader_Call) Run(run func(mD metadata.MD)) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendHeader_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 metadata.MD
		if args[0] != nil {
			arg0 = args[0].(metadata.MD)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendHeader_Call) Return(err error) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendHeader_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendHeader_Call) RunAndReturn(run func(mD metadata.MD) error) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendHeader_Call {
	_c.Call.Return(run)
	return _c
}

// SendMsg provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamServer
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamServer) SendMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for SendMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// StreamingSpanWriterPlugin_WriteSpanStreamServer_SendMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendMsg'
type StreamingSpanWriterPlugin_WriteSpanStreamServer_SendMsg_Call struct {
	*mock.Call
}

// SendMsg is a helper method to define mock.On call
//   - m any
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamServer_Expecter) SendMsg(m interface{}) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendMsg_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamServer_SendMsg_Call{Call: _e.mock.On("SendMsg", m)}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendMsg_Call) Run(run func(m any)) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendMsg_Call) Return(err error) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendMsg_Call) RunAndReturn(run func(m any) error) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SendMsg_Call {
	_c.Call.Return(run)
	return _c
}

// SetHeader provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamServer
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamServer) SetHeader(mD metadata.MD) error {
	ret := _mock.Called(mD)

	if len(ret) == 0 {
		panic("no return value specified for SetHeader")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(metadata.MD) error); ok {
		r0 = returnFunc(mD)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// StreamingSpanWriterPlugin_WriteSpanStreamServer_SetHeader_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetHeader'
type StreamingSpanWriterPlugin_WriteSpanStreamServer_SetHeader_Call struct {
	*mock.Call
}

// SetHeader is a helper method to define mock.On call
//   - mD metadata.MD
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamServer_Expecter) SetHeader(mD interface{}) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SetHeader_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamServer_SetHeader_Call{Call: _e.mock.On("SetHeader", mD)}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SetHeader_Call) Run(run func(mD metadata.MD)) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SetHeader_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 metadata.MD
		if args[0] != nil {
			arg0 = args[0].(metadata.MD)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SetHeader_Call) Return(err error) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SetHeader_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SetHeader_Call) RunAndReturn(run func(mD metadata.MD) error) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SetHeader_Call {
	_c.Call.Return(run)
	return _c
}

// SetTrailer provides a mock function for the type StreamingSpanWriterPlugin_WriteSpanStreamServer
func (_mock *StreamingSpanWriterPlugin_WriteSpanStreamServer) SetTrailer(mD metadata.MD) {
	_mock.Called(mD)
	return
}

// StreamingSpanWriterPlugin_WriteSpanStreamServer_SetTrailer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetTrailer'
type StreamingSpanWriterPlugin_WriteSpanStreamServer_SetTrailer_Call struct {
	*mock.Call
}

// SetTrailer is a helper method to define mock.On call
//   - mD metadata.MD
func (_e *StreamingSpanWriterPlugin_WriteSpanStreamServer_Expecter) SetTrailer(mD interface{}) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SetTrailer_Call {
	return &StreamingSpanWriterPlugin_WriteSpanStreamServer_SetTrailer_Call{Call: _e.mock.On("SetTrailer", mD)}
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SetTrailer_Call) Run(run func(mD metadata.MD)) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SetTrailer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 metadata.MD
		if args[0] != nil {
			arg0 = args[0].(metadata.MD)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SetTrailer_Call) Return() *StreamingSpanWriterPlugin_WriteSpanStreamServer_SetTrailer_Call {
	_c.Call.Return()
	return _c
}

func (_c *StreamingSpanWriterPlugin_WriteSpanStreamServer_SetTrailer_Call) RunAndReturn(run func(mD metadata.MD)) *StreamingSpanWriterPlugin_WriteSpanStreamServer_SetTrailer_Call {
	_c.Run(run)
	return _c
}

// NewSpanReaderPluginClient creates a new instance of SpanReaderPluginClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSpanReaderPluginClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *SpanReaderPluginClient {
	mock := &SpanReaderPluginClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SpanReaderPluginClient is an autogenerated mock type for the SpanReaderPluginClient type
type SpanReaderPluginClient struct {
	mock.Mock
}

type SpanReaderPluginClient_Expecter struct {
	mock *mock.Mock
}

func (_m *SpanReaderPluginClient) EXPECT() *SpanReaderPluginClient_Expecter {
	return &SpanReaderPluginClient_Expecter{mock: &_m.Mock}
}

// FindTraceIDs provides a mock function for the type SpanReaderPluginClient
func (_mock *SpanReaderPluginClient) FindTraceIDs(ctx context.Context, in *storage_v1.FindTraceIDsRequest, opts ...grpc.CallOption) (*storage_v1.FindTraceIDsResponse, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, in, opts)
	} else {
		tmpRet = _mock.Called(ctx, in)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for FindTraceIDs")
	}

	var r0 *storage_v1.FindTraceIDsResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.FindTraceIDsRequest, ...grpc.CallOption) (*storage_v1.FindTraceIDsResponse, error)); ok {
		return returnFunc(ctx, in, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.FindTraceIDsRequest, ...grpc.CallOption) *storage_v1.FindTraceIDsResponse); ok {
		r0 = returnFunc(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.FindTraceIDsResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.FindTraceIDsRequest, ...grpc.CallOption) error); ok {
		r1 = returnFunc(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanReaderPluginClient_FindTraceIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTraceIDs'
type SpanReaderPluginClient_FindTraceIDs_Call struct {
	*mock.Call
}

// FindTraceIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - in *storage_v1.FindTraceIDsRequest
//   - opts ...grpc.CallOption
func (_e *SpanReaderPluginClient_Expecter) FindTraceIDs(ctx interface{}, in interface{}, opts ...interface{}) *SpanReaderPluginClient_FindTraceIDs_Call {
	return &SpanReaderPluginClient_FindTraceIDs_Call{Call: _e.mock.On("FindTraceIDs",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *SpanReaderPluginClient_FindTraceIDs_Call) Run(run func(ctx context.Context, in *storage_v1.FindTraceIDsRequest, opts ...grpc.CallOption)) *SpanReaderPluginClient_FindTraceIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.FindTraceIDsRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.FindTraceIDsRequest)
		}
		var arg2 []grpc.CallOption
		var variadicArgs []grpc.CallOption
		if len(args) > 2 {
			variadicArgs = args[2].([]grpc.CallOption)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *SpanReaderPluginClient_FindTraceIDs_Call) Return(findTraceIDsResponse *storage_v1.FindTraceIDsResponse, err error) *SpanReaderPluginClient_FindTraceIDs_Call {
	_c.Call.Return(findTraceIDsResponse, err)
	return _c
}

func (_c *SpanReaderPluginClient_FindTraceIDs_Call) RunAndReturn(run func(ctx context.Context, in *storage_v1.FindTraceIDsRequest, opts ...grpc.CallOption) (*storage_v1.FindTraceIDsResponse, error)) *SpanReaderPluginClient_FindTraceIDs_Call {
	_c.Call.Return(run)
	return _c
}

// FindTraces provides a mock function for the type SpanReaderPluginClient
func (_mock *SpanReaderPluginClient) FindTraces(ctx context.Context, in *storage_v1.FindTracesRequest, opts ...grpc.CallOption) (storage_v1.SpanReaderPlugin_FindTracesClient, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, in, opts)
	} else {
		tmpRet = _mock.Called(ctx, in)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for FindTraces")
	}

	var r0 storage_v1.SpanReaderPlugin_FindTracesClient
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.FindTracesRequest, ...grpc.CallOption) (storage_v1.SpanReaderPlugin_FindTracesClient, error)); ok {
		return returnFunc(ctx, in, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.FindTracesRequest, ...grpc.CallOption) storage_v1.SpanReaderPlugin_FindTracesClient); ok {
		r0 = returnFunc(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(storage_v1.SpanReaderPlugin_FindTracesClient)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.FindTracesRequest, ...grpc.CallOption) error); ok {
		r1 = returnFunc(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanReaderPluginClient_FindTraces_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTraces'
type SpanReaderPluginClient_FindTraces_Call struct {
	*mock.Call
}

// FindTraces is a helper method to define mock.On call
//   - ctx context.Context
//   - in *storage_v1.FindTracesRequest
//   - opts ...grpc.CallOption
func (_e *SpanReaderPluginClient_Expecter) FindTraces(ctx interface{}, in interface{}, opts ...interface{}) *SpanReaderPluginClient_FindTraces_Call {
	return &SpanReaderPluginClient_FindTraces_Call{Call: _e.mock.On("FindTraces",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *SpanReaderPluginClient_FindTraces_Call) Run(run func(ctx context.Context, in *storage_v1.FindTracesRequest, opts ...grpc.CallOption)) *SpanReaderPluginClient_FindTraces_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.FindTracesRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.FindTracesRequest)
		}
		var arg2 []grpc.CallOption
		var variadicArgs []grpc.CallOption
		if len(args) > 2 {
			variadicArgs = args[2].([]grpc.CallOption)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *SpanReaderPluginClient_FindTraces_Call) Return(spanReaderPlugin_FindTracesClient storage_v1.SpanReaderPlugin_FindTracesClient, err error) *SpanReaderPluginClient_FindTraces_Call {
	_c.Call.Return(spanReaderPlugin_FindTracesClient, err)
	return _c
}

func (_c *SpanReaderPluginClient_FindTraces_Call) RunAndReturn(run func(ctx context.Context, in *storage_v1.FindTracesRequest, opts ...grpc.CallOption) (storage_v1.SpanReaderPlugin_FindTracesClient, error)) *SpanReaderPluginClient_FindTraces_Call {
	_c.Call.Return(run)
	return _c
}

// GetOperations provides a mock function for the type SpanReaderPluginClient
func (_mock *SpanReaderPluginClient) GetOperations(ctx context.Context, in *storage_v1.GetOperationsRequest, opts ...grpc.CallOption) (*storage_v1.GetOperationsResponse, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, in, opts)
	} else {
		tmpRet = _mock.Called(ctx, in)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for GetOperations")
	}

	var r0 *storage_v1.GetOperationsResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetOperationsRequest, ...grpc.CallOption) (*storage_v1.GetOperationsResponse, error)); ok {
		return returnFunc(ctx, in, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetOperationsRequest, ...grpc.CallOption) *storage_v1.GetOperationsResponse); ok {
		r0 = returnFunc(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.GetOperationsResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.GetOperationsRequest, ...grpc.CallOption) error); ok {
		r1 = returnFunc(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanReaderPluginClient_GetOperations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOperations'
type SpanReaderPluginClient_GetOperations_Call struct {
	*mock.Call
}

// GetOperations is a helper method to define mock.On call
//   - ctx context.Context
//   - in *storage_v1.GetOperationsRequest
//   - opts ...grpc.CallOption
func (_e *SpanReaderPluginClient_Expecter) GetOperations(ctx interface{}, in interface{}, opts ...interface{}) *SpanReaderPluginClient_GetOperations_Call {
	return &SpanReaderPluginClient_GetOperations_Call{Call: _e.mock.On("GetOperations",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *SpanReaderPluginClient_GetOperations_Call) Run(run func(ctx context.Context, in *storage_v1.GetOperationsRequest, opts ...grpc.CallOption)) *SpanReaderPluginClient_GetOperations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.GetOperationsRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.GetOperationsRequest)
		}
		var arg2 []grpc.CallOption
		var variadicArgs []grpc.CallOption
		if len(args) > 2 {
			variadicArgs = args[2].([]grpc.CallOption)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *SpanReaderPluginClient_GetOperations_Call) Return(getOperationsResponse *storage_v1.GetOperationsResponse, err error) *SpanReaderPluginClient_GetOperations_Call {
	_c.Call.Return(getOperationsResponse, err)
	return _c
}

func (_c *SpanReaderPluginClient_GetOperations_Call) RunAndReturn(run func(ctx context.Context, in *storage_v1.GetOperationsRequest, opts ...grpc.CallOption) (*storage_v1.GetOperationsResponse, error)) *SpanReaderPluginClient_GetOperations_Call {
	_c.Call.Return(run)
	return _c
}

// GetServices provides a mock function for the type SpanReaderPluginClient
func (_mock *SpanReaderPluginClient) GetServices(ctx context.Context, in *storage_v1.GetServicesRequest, opts ...grpc.CallOption) (*storage_v1.GetServicesResponse, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, in, opts)
	} else {
		tmpRet = _mock.Called(ctx, in)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for GetServices")
	}

	var r0 *storage_v1.GetServicesResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetServicesRequest, ...grpc.CallOption) (*storage_v1.GetServicesResponse, error)); ok {
		return returnFunc(ctx, in, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetServicesRequest, ...grpc.CallOption) *storage_v1.GetServicesResponse); ok {
		r0 = returnFunc(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.GetServicesResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.GetServicesRequest, ...grpc.CallOption) error); ok {
		r1 = returnFunc(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanReaderPluginClient_GetServices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetServices'
type SpanReaderPluginClient_GetServices_Call struct {
	*mock.Call
}

// GetServices is a helper method to define mock.On call
//   - ctx context.Context
//   - in *storage_v1.GetServicesRequest
//   - opts ...grpc.CallOption
func (_e *SpanReaderPluginClient_Expecter) GetServices(ctx interface{}, in interface{}, opts ...interface{}) *SpanReaderPluginClient_GetServices_Call {
	return &SpanReaderPluginClient_GetServices_Call{Call: _e.mock.On("GetServices",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *SpanReaderPluginClient_GetServices_Call) Run(run func(ctx context.Context, in *storage_v1.GetServicesRequest, opts ...grpc.CallOption)) *SpanReaderPluginClient_GetServices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.GetServicesRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.GetServicesRequest)
		}
		var arg2 []grpc.CallOption
		var variadicArgs []grpc.CallOption
		if len(args) > 2 {
			variadicArgs = args[2].([]grpc.CallOption)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *SpanReaderPluginClient_GetServices_Call) Return(getServicesResponse *storage_v1.GetServicesResponse, err error) *SpanReaderPluginClient_GetServices_Call {
	_c.Call.Return(getServicesResponse, err)
	return _c
}

func (_c *SpanReaderPluginClient_GetServices_Call) RunAndReturn(run func(ctx context.Context, in *storage_v1.GetServicesRequest, opts ...grpc.CallOption) (*storage_v1.GetServicesResponse, error)) *SpanReaderPluginClient_GetServices_Call {
	_c.Call.Return(run)
	return _c
}

// GetTrace provides a mock function for the type SpanReaderPluginClient
func (_mock *SpanReaderPluginClient) GetTrace(ctx context.Context, in *storage_v1.GetTraceRequest, opts ...grpc.CallOption) (storage_v1.SpanReaderPlugin_GetTraceClient, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, in, opts)
	} else {
		tmpRet = _mock.Called(ctx, in)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for GetTrace")
	}

	var r0 storage_v1.SpanReaderPlugin_GetTraceClient
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetTraceRequest, ...grpc.CallOption) (storage_v1.SpanReaderPlugin_GetTraceClient, error)); ok {
		return returnFunc(ctx, in, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetTraceRequest, ...grpc.CallOption) storage_v1.SpanReaderPlugin_GetTraceClient); ok {
		r0 = returnFunc(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(storage_v1.SpanReaderPlugin_GetTraceClient)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.GetTraceRequest, ...grpc.CallOption) error); ok {
		r1 = returnFunc(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanReaderPluginClient_GetTrace_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTrace'
type SpanReaderPluginClient_GetTrace_Call struct {
	*mock.Call
}

// GetTrace is a helper method to define mock.On call
//   - ctx context.Context
//   - in *storage_v1.GetTraceRequest
//   - opts ...grpc.CallOption
func (_e *SpanReaderPluginClient_Expecter) GetTrace(ctx interface{}, in interface{}, opts ...interface{}) *SpanReaderPluginClient_GetTrace_Call {
	return &SpanReaderPluginClient_GetTrace_Call{Call: _e.mock.On("GetTrace",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *SpanReaderPluginClient_GetTrace_Call) Run(run func(ctx context.Context, in *storage_v1.GetTraceRequest, opts ...grpc.CallOption)) *SpanReaderPluginClient_GetTrace_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.GetTraceRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.GetTraceRequest)
		}
		var arg2 []grpc.CallOption
		var variadicArgs []grpc.CallOption
		if len(args) > 2 {
			variadicArgs = args[2].([]grpc.CallOption)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *SpanReaderPluginClient_GetTrace_Call) Return(spanReaderPlugin_GetTraceClient storage_v1.SpanReaderPlugin_GetTraceClient, err error) *SpanReaderPluginClient_GetTrace_Call {
	_c.Call.Return(spanReaderPlugin_GetTraceClient, err)
	return _c
}

func (_c *SpanReaderPluginClient_GetTrace_Call) RunAndReturn(run func(ctx context.Context, in *storage_v1.GetTraceRequest, opts ...grpc.CallOption) (storage_v1.SpanReaderPlugin_GetTraceClient, error)) *SpanReaderPluginClient_GetTrace_Call {
	_c.Call.Return(run)
	return _c
}

// NewSpanReaderPlugin_GetTraceClient creates a new instance of SpanReaderPlugin_GetTraceClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSpanReaderPlugin_GetTraceClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *SpanReaderPlugin_GetTraceClient {
	mock := &SpanReaderPlugin_GetTraceClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SpanReaderPlugin_GetTraceClient is an autogenerated mock type for the SpanReaderPlugin_GetTraceClient type
type SpanReaderPlugin_GetTraceClient struct {
	mock.Mock
}

type SpanReaderPlugin_GetTraceClient_Expecter struct {
	mock *mock.Mock
}

func (_m *SpanReaderPlugin_GetTraceClient) EXPECT() *SpanReaderPlugin_GetTraceClient_Expecter {
	return &SpanReaderPlugin_GetTraceClient_Expecter{mock: &_m.Mock}
}

// CloseSend provides a mock function for the type SpanReaderPlugin_GetTraceClient
func (_mock *SpanReaderPlugin_GetTraceClient) CloseSend() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for CloseSend")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_GetTraceClient_CloseSend_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CloseSend'
type SpanReaderPlugin_GetTraceClient_CloseSend_Call struct {
	*mock.Call
}

// CloseSend is a helper method to define mock.On call
func (_e *SpanReaderPlugin_GetTraceClient_Expecter) CloseSend() *SpanReaderPlugin_GetTraceClient_CloseSend_Call {
	return &SpanReaderPlugin_GetTraceClient_CloseSend_Call{Call: _e.mock.On("CloseSend")}
}

func (_c *SpanReaderPlugin_GetTraceClient_CloseSend_Call) Run(run func()) *SpanReaderPlugin_GetTraceClient_CloseSend_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *SpanReaderPlugin_GetTraceClient_CloseSend_Call) Return(err error) *SpanReaderPlugin_GetTraceClient_CloseSend_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_GetTraceClient_CloseSend_Call) RunAndReturn(run func() error) *SpanReaderPlugin_GetTraceClient_CloseSend_Call {
	_c.Call.Return(run)
	return _c
}

// Context provides a mock function for the type SpanReaderPlugin_GetTraceClient
func (_mock *SpanReaderPlugin_GetTraceClient) Context() context.Context {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Context")
	}

	var r0 context.Context
	if returnFunc, ok := ret.Get(0).(func() context.Context); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(context.Context)
		}
	}
	return r0
}

// SpanReaderPlugin_GetTraceClient_Context_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Context'
type SpanReaderPlugin_GetTraceClient_Context_Call struct {
	*mock.Call
}

// Context is a helper method to define mock.On call
func (_e *SpanReaderPlugin_GetTraceClient_Expecter) Context() *SpanReaderPlugin_GetTraceClient_Context_Call {
	return &SpanReaderPlugin_GetTraceClient_Context_Call{Call: _e.mock.On("Context")}
}

func (_c *SpanReaderPlugin_GetTraceClient_Context_Call) Run(run func()) *SpanReaderPlugin_GetTraceClient_Context_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *SpanReaderPlugin_GetTraceClient_Context_Call) Return(context1 context.Context) *SpanReaderPlugin_GetTraceClient_Context_Call {
	_c.Call.Return(context1)
	return _c
}

func (_c *SpanReaderPlugin_GetTraceClient_Context_Call) RunAndReturn(run func() context.Context) *SpanReaderPlugin_GetTraceClient_Context_Call {
	_c.Call.Return(run)
	return _c
}

// Header provides a mock function for the type SpanReaderPlugin_GetTraceClient
func (_mock *SpanReaderPlugin_GetTraceClient) Header() (metadata.MD, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Header")
	}

	var r0 metadata.MD
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (metadata.MD, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() metadata.MD); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(metadata.MD)
		}
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanReaderPlugin_GetTraceClient_Header_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Header'
type SpanReaderPlugin_GetTraceClient_Header_Call struct {
	*mock.Call
}

// Header is a helper method to define mock.On call
func (_e *SpanReaderPlugin_GetTraceClient_Expecter) Header() *SpanReaderPlugin_GetTraceClient_Header_Call {
	return &SpanReaderPlugin_GetTraceClient_Header_Call{Call: _e.mock.On("Header")}
}

func (_c *SpanReaderPlugin_GetTraceClient_Header_Call) Run(run func()) *SpanReaderPlugin_GetTraceClient_Header_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *SpanReaderPlugin_GetTraceClient_Header_Call) Return(mD metadata.MD, err error) *SpanReaderPlugin_GetTraceClient_Header_Call {
	_c.Call.Return(mD, err)
	return _c
}

func (_c *SpanReaderPlugin_GetTraceClient_Header_Call) RunAndReturn(run func() (metadata.MD, error)) *SpanReaderPlugin_GetTraceClient_Header_Call {
	_c.Call.Return(run)
	return _c
}

// Recv provides a mock function for the type SpanReaderPlugin_GetTraceClient
func (_mock *SpanReaderPlugin_GetTraceClient) Recv() (*storage_v1.SpansResponseChunk, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Recv")
	}

	var r0 *storage_v1.SpansResponseChunk
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (*storage_v1.SpansResponseChunk, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() *storage_v1.SpansResponseChunk); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.SpansResponseChunk)
		}
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanReaderPlugin_GetTraceClient_Recv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Recv'
type SpanReaderPlugin_GetTraceClient_Recv_Call struct {
	*mock.Call
}

// Recv is a helper method to define mock.On call
func (_e *SpanReaderPlugin_GetTraceClient_Expecter) Recv() *SpanReaderPlugin_GetTraceClient_Recv_Call {
	return &SpanReaderPlugin_GetTraceClient_Recv_Call{Call: _e.mock.On("Recv")}
}

func (_c *SpanReaderPlugin_GetTraceClient_Recv_Call) Run(run func()) *SpanReaderPlugin_GetTraceClient_Recv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *SpanReaderPlugin_GetTraceClient_Recv_Call) Return(spansResponseChunk *storage_v1.SpansResponseChunk, err error) *SpanReaderPlugin_GetTraceClient_Recv_Call {
	_c.Call.Return(spansResponseChunk, err)
	return _c
}

func (_c *SpanReaderPlugin_GetTraceClient_Recv_Call) RunAndReturn(run func() (*storage_v1.SpansResponseChunk, error)) *SpanReaderPlugin_GetTraceClient_Recv_Call {
	_c.Call.Return(run)
	return _c
}

// RecvMsg provides a mock function for the type SpanReaderPlugin_GetTraceClient
func (_mock *SpanReaderPlugin_GetTraceClient) RecvMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for RecvMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_GetTraceClient_RecvMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecvMsg'
type SpanReaderPlugin_GetTraceClient_RecvMsg_Call struct {
	*mock.Call
}

// RecvMsg is a helper method to define mock.On call
//   - m any
func (_e *SpanReaderPlugin_GetTraceClient_Expecter) RecvMsg(m interface{}) *SpanReaderPlugin_GetTraceClient_RecvMsg_Call {
	return &SpanReaderPlugin_GetTraceClient_RecvMsg_Call{Call: _e.mock.On("RecvMsg", m)}
}

func (_c *SpanReaderPlugin_GetTraceClient_RecvMsg_Call) Run(run func(m any)) *SpanReaderPlugin_GetTraceClient_RecvMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_GetTraceClient_RecvMsg_Call) Return(err error) *SpanReaderPlugin_GetTraceClient_RecvMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_GetTraceClient_RecvMsg_Call) RunAndReturn(run func(m any) error) *SpanReaderPlugin_GetTraceClient_RecvMsg_Call {
	_c.Call.Return(run)
	return _c
}

// SendMsg provides a mock function for the type SpanReaderPlugin_GetTraceClient
func (_mock *SpanReaderPlugin_GetTraceClient) SendMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for SendMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_GetTraceClient_SendMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendMsg'
type SpanReaderPlugin_GetTraceClient_SendMsg_Call struct {
	*mock.Call
}

// SendMsg is a helper method to define mock.On call
//   - m any
func (_e *SpanReaderPlugin_GetTraceClient_Expecter) SendMsg(m interface{}) *SpanReaderPlugin_GetTraceClient_SendMsg_Call {
	return &SpanReaderPlugin_GetTraceClient_SendMsg_Call{Call: _e.mock.On("SendMsg", m)}
}

func (_c *SpanReaderPlugin_GetTraceClient_SendMsg_Call) Run(run func(m any)) *SpanReaderPlugin_GetTraceClient_SendMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_GetTraceClient_SendMsg_Call) Return(err error) *SpanReaderPlugin_GetTraceClient_SendMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_GetTraceClient_SendMsg_Call) RunAndReturn(run func(m any) error) *SpanReaderPlugin_GetTraceClient_SendMsg_Call {
	_c.Call.Return(run)
	return _c
}

// Trailer provides a mock function for the type SpanReaderPlugin_GetTraceClient
func (_mock *SpanReaderPlugin_GetTraceClient) Trailer() metadata.MD {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Trailer")
	}

	var r0 metadata.MD
	if returnFunc, ok := ret.Get(0).(func() metadata.MD); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(metadata.MD)
		}
	}
	return r0
}

// SpanReaderPlugin_GetTraceClient_Trailer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Trailer'
type SpanReaderPlugin_GetTraceClient_Trailer_Call struct {
	*mock.Call
}

// Trailer is a helper method to define mock.On call
func (_e *SpanReaderPlugin_GetTraceClient_Expecter) Trailer() *SpanReaderPlugin_GetTraceClient_Trailer_Call {
	return &SpanReaderPlugin_GetTraceClient_Trailer_Call{Call: _e.mock.On("Trailer")}
}

func (_c *SpanReaderPlugin_GetTraceClient_Trailer_Call) Run(run func()) *SpanReaderPlugin_GetTraceClient_Trailer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *SpanReaderPlugin_GetTraceClient_Trailer_Call) Return(mD metadata.MD) *SpanReaderPlugin_GetTraceClient_Trailer_Call {
	_c.Call.Return(mD)
	return _c
}

func (_c *SpanReaderPlugin_GetTraceClient_Trailer_Call) RunAndReturn(run func() metadata.MD) *SpanReaderPlugin_GetTraceClient_Trailer_Call {
	_c.Call.Return(run)
	return _c
}

// NewSpanReaderPlugin_FindTracesClient creates a new instance of SpanReaderPlugin_FindTracesClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSpanReaderPlugin_FindTracesClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *SpanReaderPlugin_FindTracesClient {
	mock := &SpanReaderPlugin_FindTracesClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SpanReaderPlugin_FindTracesClient is an autogenerated mock type for the SpanReaderPlugin_FindTracesClient type
type SpanReaderPlugin_FindTracesClient struct {
	mock.Mock
}

type SpanReaderPlugin_FindTracesClient_Expecter struct {
	mock *mock.Mock
}

func (_m *SpanReaderPlugin_FindTracesClient) EXPECT() *SpanReaderPlugin_FindTracesClient_Expecter {
	return &SpanReaderPlugin_FindTracesClient_Expecter{mock: &_m.Mock}
}

// CloseSend provides a mock function for the type SpanReaderPlugin_FindTracesClient
func (_mock *SpanReaderPlugin_FindTracesClient) CloseSend() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for CloseSend")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_FindTracesClient_CloseSend_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CloseSend'
type SpanReaderPlugin_FindTracesClient_CloseSend_Call struct {
	*mock.Call
}

// CloseSend is a helper method to define mock.On call
func (_e *SpanReaderPlugin_FindTracesClient_Expecter) CloseSend() *SpanReaderPlugin_FindTracesClient_CloseSend_Call {
	return &SpanReaderPlugin_FindTracesClient_CloseSend_Call{Call: _e.mock.On("CloseSend")}
}

func (_c *SpanReaderPlugin_FindTracesClient_CloseSend_Call) Run(run func()) *SpanReaderPlugin_FindTracesClient_CloseSend_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *SpanReaderPlugin_FindTracesClient_CloseSend_Call) Return(err error) *SpanReaderPlugin_FindTracesClient_CloseSend_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_FindTracesClient_CloseSend_Call) RunAndReturn(run func() error) *SpanReaderPlugin_FindTracesClient_CloseSend_Call {
	_c.Call.Return(run)
	return _c
}

// Context provides a mock function for the type SpanReaderPlugin_FindTracesClient
func (_mock *SpanReaderPlugin_FindTracesClient) Context() context.Context {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Context")
	}

	var r0 context.Context
	if returnFunc, ok := ret.Get(0).(func() context.Context); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(context.Context)
		}
	}
	return r0
}

// SpanReaderPlugin_FindTracesClient_Context_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Context'
type SpanReaderPlugin_FindTracesClient_Context_Call struct {
	*mock.Call
}

// Context is a helper method to define mock.On call
func (_e *SpanReaderPlugin_FindTracesClient_Expecter) Context() *SpanReaderPlugin_FindTracesClient_Context_Call {
	return &SpanReaderPlugin_FindTracesClient_Context_Call{Call: _e.mock.On("Context")}
}

func (_c *SpanReaderPlugin_FindTracesClient_Context_Call) Run(run func()) *SpanReaderPlugin_FindTracesClient_Context_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *SpanReaderPlugin_FindTracesClient_Context_Call) Return(context1 context.Context) *SpanReaderPlugin_FindTracesClient_Context_Call {
	_c.Call.Return(context1)
	return _c
}

func (_c *SpanReaderPlugin_FindTracesClient_Context_Call) RunAndReturn(run func() context.Context) *SpanReaderPlugin_FindTracesClient_Context_Call {
	_c.Call.Return(run)
	return _c
}

// Header provides a mock function for the type SpanReaderPlugin_FindTracesClient
func (_mock *SpanReaderPlugin_FindTracesClient) Header() (metadata.MD, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Header")
	}

	var r0 metadata.MD
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (metadata.MD, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() metadata.MD); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(metadata.MD)
		}
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanReaderPlugin_FindTracesClient_Header_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Header'
type SpanReaderPlugin_FindTracesClient_Header_Call struct {
	*mock.Call
}

// Header is a helper method to define mock.On call
func (_e *SpanReaderPlugin_FindTracesClient_Expecter) Header() *SpanReaderPlugin_FindTracesClient_Header_Call {
	return &SpanReaderPlugin_FindTracesClient_Header_Call{Call: _e.mock.On("Header")}
}

func (_c *SpanReaderPlugin_FindTracesClient_Header_Call) Run(run func()) *SpanReaderPlugin_FindTracesClient_Header_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *SpanReaderPlugin_FindTracesClient_Header_Call) Return(mD metadata.MD, err error) *SpanReaderPlugin_FindTracesClient_Header_Call {
	_c.Call.Return(mD, err)
	return _c
}

func (_c *SpanReaderPlugin_FindTracesClient_Header_Call) RunAndReturn(run func() (metadata.MD, error)) *SpanReaderPlugin_FindTracesClient_Header_Call {
	_c.Call.Return(run)
	return _c
}

// Recv provides a mock function for the type SpanReaderPlugin_FindTracesClient
func (_mock *SpanReaderPlugin_FindTracesClient) Recv() (*storage_v1.SpansResponseChunk, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Recv")
	}

	var r0 *storage_v1.SpansResponseChunk
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (*storage_v1.SpansResponseChunk, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() *storage_v1.SpansResponseChunk); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.SpansResponseChunk)
		}
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanReaderPlugin_FindTracesClient_Recv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Recv'
type SpanReaderPlugin_FindTracesClient_Recv_Call struct {
	*mock.Call
}

// Recv is a helper method to define mock.On call
func (_e *SpanReaderPlugin_FindTracesClient_Expecter) Recv() *SpanReaderPlugin_FindTracesClient_Recv_Call {
	return &SpanReaderPlugin_FindTracesClient_Recv_Call{Call: _e.mock.On("Recv")}
}

func (_c *SpanReaderPlugin_FindTracesClient_Recv_Call) Run(run func()) *SpanReaderPlugin_FindTracesClient_Recv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *SpanReaderPlugin_FindTracesClient_Recv_Call) Return(spansResponseChunk *storage_v1.SpansResponseChunk, err error) *SpanReaderPlugin_FindTracesClient_Recv_Call {
	_c.Call.Return(spansResponseChunk, err)
	return _c
}

func (_c *SpanReaderPlugin_FindTracesClient_Recv_Call) RunAndReturn(run func() (*storage_v1.SpansResponseChunk, error)) *SpanReaderPlugin_FindTracesClient_Recv_Call {
	_c.Call.Return(run)
	return _c
}

// RecvMsg provides a mock function for the type SpanReaderPlugin_FindTracesClient
func (_mock *SpanReaderPlugin_FindTracesClient) RecvMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for RecvMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_FindTracesClient_RecvMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecvMsg'
type SpanReaderPlugin_FindTracesClient_RecvMsg_Call struct {
	*mock.Call
}

// RecvMsg is a helper method to define mock.On call
//   - m any
func (_e *SpanReaderPlugin_FindTracesClient_Expecter) RecvMsg(m interface{}) *SpanReaderPlugin_FindTracesClient_RecvMsg_Call {
	return &SpanReaderPlugin_FindTracesClient_RecvMsg_Call{Call: _e.mock.On("RecvMsg", m)}
}

func (_c *SpanReaderPlugin_FindTracesClient_RecvMsg_Call) Run(run func(m any)) *SpanReaderPlugin_FindTracesClient_RecvMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_FindTracesClient_RecvMsg_Call) Return(err error) *SpanReaderPlugin_FindTracesClient_RecvMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_FindTracesClient_RecvMsg_Call) RunAndReturn(run func(m any) error) *SpanReaderPlugin_FindTracesClient_RecvMsg_Call {
	_c.Call.Return(run)
	return _c
}

// SendMsg provides a mock function for the type SpanReaderPlugin_FindTracesClient
func (_mock *SpanReaderPlugin_FindTracesClient) SendMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for SendMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_FindTracesClient_SendMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendMsg'
type SpanReaderPlugin_FindTracesClient_SendMsg_Call struct {
	*mock.Call
}

// SendMsg is a helper method to define mock.On call
//   - m any
func (_e *SpanReaderPlugin_FindTracesClient_Expecter) SendMsg(m interface{}) *SpanReaderPlugin_FindTracesClient_SendMsg_Call {
	return &SpanReaderPlugin_FindTracesClient_SendMsg_Call{Call: _e.mock.On("SendMsg", m)}
}

func (_c *SpanReaderPlugin_FindTracesClient_SendMsg_Call) Run(run func(m any)) *SpanReaderPlugin_FindTracesClient_SendMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_FindTracesClient_SendMsg_Call) Return(err error) *SpanReaderPlugin_FindTracesClient_SendMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_FindTracesClient_SendMsg_Call) RunAndReturn(run func(m any) error) *SpanReaderPlugin_FindTracesClient_SendMsg_Call {
	_c.Call.Return(run)
	return _c
}

// Trailer provides a mock function for the type SpanReaderPlugin_FindTracesClient
func (_mock *SpanReaderPlugin_FindTracesClient) Trailer() metadata.MD {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Trailer")
	}

	var r0 metadata.MD
	if returnFunc, ok := ret.Get(0).(func() metadata.MD); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(metadata.MD)
		}
	}
	return r0
}

// SpanReaderPlugin_FindTracesClient_Trailer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Trailer'
type SpanReaderPlugin_FindTracesClient_Trailer_Call struct {
	*mock.Call
}

// Trailer is a helper method to define mock.On call
func (_e *SpanReaderPlugin_FindTracesClient_Expecter) Trailer() *SpanReaderPlugin_FindTracesClient_Trailer_Call {
	return &SpanReaderPlugin_FindTracesClient_Trailer_Call{Call: _e.mock.On("Trailer")}
}

func (_c *SpanReaderPlugin_FindTracesClient_Trailer_Call) Run(run func()) *SpanReaderPlugin_FindTracesClient_Trailer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *SpanReaderPlugin_FindTracesClient_Trailer_Call) Return(mD metadata.MD) *SpanReaderPlugin_FindTracesClient_Trailer_Call {
	_c.Call.Return(mD)
	return _c
}

func (_c *SpanReaderPlugin_FindTracesClient_Trailer_Call) RunAndReturn(run func() metadata.MD) *SpanReaderPlugin_FindTracesClient_Trailer_Call {
	_c.Call.Return(run)
	return _c
}

// NewSpanReaderPluginServer creates a new instance of SpanReaderPluginServer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSpanReaderPluginServer(t interface {
	mock.TestingT
	Cleanup(func())
}) *SpanReaderPluginServer {
	mock := &SpanReaderPluginServer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SpanReaderPluginServer is an autogenerated mock type for the SpanReaderPluginServer type
type SpanReaderPluginServer struct {
	mock.Mock
}

type SpanReaderPluginServer_Expecter struct {
	mock *mock.Mock
}

func (_m *SpanReaderPluginServer) EXPECT() *SpanReaderPluginServer_Expecter {
	return &SpanReaderPluginServer_Expecter{mock: &_m.Mock}
}

// FindTraceIDs provides a mock function for the type SpanReaderPluginServer
func (_mock *SpanReaderPluginServer) FindTraceIDs(context1 context.Context, findTraceIDsRequest *storage_v1.FindTraceIDsRequest) (*storage_v1.FindTraceIDsResponse, error) {
	ret := _mock.Called(context1, findTraceIDsRequest)

	if len(ret) == 0 {
		panic("no return value specified for FindTraceIDs")
	}

	var r0 *storage_v1.FindTraceIDsResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.FindTraceIDsRequest) (*storage_v1.FindTraceIDsResponse, error)); ok {
		return returnFunc(context1, findTraceIDsRequest)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.FindTraceIDsRequest) *storage_v1.FindTraceIDsResponse); ok {
		r0 = returnFunc(context1, findTraceIDsRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.FindTraceIDsResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.FindTraceIDsRequest) error); ok {
		r1 = returnFunc(context1, findTraceIDsRequest)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanReaderPluginServer_FindTraceIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTraceIDs'
type SpanReaderPluginServer_FindTraceIDs_Call struct {
	*mock.Call
}

// FindTraceIDs is a helper method to define mock.On call
//   - context1 context.Context
//   - findTraceIDsRequest *storage_v1.FindTraceIDsRequest
func (_e *SpanReaderPluginServer_Expecter) FindTraceIDs(context1 interface{}, findTraceIDsRequest interface{}) *SpanReaderPluginServer_FindTraceIDs_Call {
	return &SpanReaderPluginServer_FindTraceIDs_Call{Call: _e.mock.On("FindTraceIDs", context1, findTraceIDsRequest)}
}

func (_c *SpanReaderPluginServer_FindTraceIDs_Call) Run(run func(context1 context.Context, findTraceIDsRequest *storage_v1.FindTraceIDsRequest)) *SpanReaderPluginServer_FindTraceIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.FindTraceIDsRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.FindTraceIDsRequest)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *SpanReaderPluginServer_FindTraceIDs_Call) Return(findTraceIDsResponse *storage_v1.FindTraceIDsResponse, err error) *SpanReaderPluginServer_FindTraceIDs_Call {
	_c.Call.Return(findTraceIDsResponse, err)
	return _c
}

func (_c *SpanReaderPluginServer_FindTraceIDs_Call) RunAndReturn(run func(context1 context.Context, findTraceIDsRequest *storage_v1.FindTraceIDsRequest) (*storage_v1.FindTraceIDsResponse, error)) *SpanReaderPluginServer_FindTraceIDs_Call {
	_c.Call.Return(run)
	return _c
}

// FindTraces provides a mock function for the type SpanReaderPluginServer
func (_mock *SpanReaderPluginServer) FindTraces(findTracesRequest *storage_v1.FindTracesRequest, spanReaderPlugin_FindTracesServer storage_v1.SpanReaderPlugin_FindTracesServer) error {
	ret := _mock.Called(findTracesRequest, spanReaderPlugin_FindTracesServer)

	if len(ret) == 0 {
		panic("no return value specified for FindTraces")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(*storage_v1.FindTracesRequest, storage_v1.SpanReaderPlugin_FindTracesServer) error); ok {
		r0 = returnFunc(findTracesRequest, spanReaderPlugin_FindTracesServer)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPluginServer_FindTraces_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTraces'
type SpanReaderPluginServer_FindTraces_Call struct {
	*mock.Call
}

// FindTraces is a helper method to define mock.On call
//   - findTracesRequest *storage_v1.FindTracesRequest
//   - spanReaderPlugin_FindTracesServer storage_v1.SpanReaderPlugin_FindTracesServer
func (_e *SpanReaderPluginServer_Expecter) FindTraces(findTracesRequest interface{}, spanReaderPlugin_FindTracesServer interface{}) *SpanReaderPluginServer_FindTraces_Call {
	return &SpanReaderPluginServer_FindTraces_Call{Call: _e.mock.On("FindTraces", findTracesRequest, spanReaderPlugin_FindTracesServer)}
}

func (_c *SpanReaderPluginServer_FindTraces_Call) Run(run func(findTracesRequest *storage_v1.FindTracesRequest, spanReaderPlugin_FindTracesServer storage_v1.SpanReaderPlugin_FindTracesServer)) *SpanReaderPluginServer_FindTraces_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *storage_v1.FindTracesRequest
		if args[0] != nil {
			arg0 = args[0].(*storage_v1.FindTracesRequest)
		}
		var arg1 storage_v1.SpanReaderPlugin_FindTracesServer
		if args[1] != nil {
			arg1 = args[1].(storage_v1.SpanReaderPlugin_FindTracesServer)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *SpanReaderPluginServer_FindTraces_Call) Return(err error) *SpanReaderPluginServer_FindTraces_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPluginServer_FindTraces_Call) RunAndReturn(run func(findTracesRequest *storage_v1.FindTracesRequest, spanReaderPlugin_FindTracesServer storage_v1.SpanReaderPlugin_FindTracesServer) error) *SpanReaderPluginServer_FindTraces_Call {
	_c.Call.Return(run)
	return _c
}

// GetOperations provides a mock function for the type SpanReaderPluginServer
func (_mock *SpanReaderPluginServer) GetOperations(context1 context.Context, getOperationsRequest *storage_v1.GetOperationsRequest) (*storage_v1.GetOperationsResponse, error) {
	ret := _mock.Called(context1, getOperationsRequest)

	if len(ret) == 0 {
		panic("no return value specified for GetOperations")
	}

	var r0 *storage_v1.GetOperationsResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetOperationsRequest) (*storage_v1.GetOperationsResponse, error)); ok {
		return returnFunc(context1, getOperationsRequest)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetOperationsRequest) *storage_v1.GetOperationsResponse); ok {
		r0 = returnFunc(context1, getOperationsRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.GetOperationsResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.GetOperationsRequest) error); ok {
		r1 = returnFunc(context1, getOperationsRequest)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanReaderPluginServer_GetOperations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOperations'
type SpanReaderPluginServer_GetOperations_Call struct {
	*mock.Call
}

// GetOperations is a helper method to define mock.On call
//   - context1 context.Context
//   - getOperationsRequest *storage_v1.GetOperationsRequest
func (_e *SpanReaderPluginServer_Expecter) GetOperations(context1 interface{}, getOperationsRequest interface{}) *SpanReaderPluginServer_GetOperations_Call {
	return &SpanReaderPluginServer_GetOperations_Call{Call: _e.mock.On("GetOperations", context1, getOperationsRequest)}
}

func (_c *SpanReaderPluginServer_GetOperations_Call) Run(run func(context1 context.Context, getOperationsRequest *storage_v1.GetOperationsRequest)) *SpanReaderPluginServer_GetOperations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.GetOperationsRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.GetOperationsRequest)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *SpanReaderPluginServer_GetOperations_Call) Return(getOperationsResponse *storage_v1.GetOperationsResponse, err error) *SpanReaderPluginServer_GetOperations_Call {
	_c.Call.Return(getOperationsResponse, err)
	return _c
}

func (_c *SpanReaderPluginServer_GetOperations_Call) RunAndReturn(run func(context1 context.Context, getOperationsRequest *storage_v1.GetOperationsRequest) (*storage_v1.GetOperationsResponse, error)) *SpanReaderPluginServer_GetOperations_Call {
	_c.Call.Return(run)
	return _c
}

// GetServices provides a mock function for the type SpanReaderPluginServer
func (_mock *SpanReaderPluginServer) GetServices(context1 context.Context, getServicesRequest *storage_v1.GetServicesRequest) (*storage_v1.GetServicesResponse, error) {
	ret := _mock.Called(context1, getServicesRequest)

	if len(ret) == 0 {
		panic("no return value specified for GetServices")
	}

	var r0 *storage_v1.GetServicesResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetServicesRequest) (*storage_v1.GetServicesResponse, error)); ok {
		return returnFunc(context1, getServicesRequest)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetServicesRequest) *storage_v1.GetServicesResponse); ok {
		r0 = returnFunc(context1, getServicesRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.GetServicesResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.GetServicesRequest) error); ok {
		r1 = returnFunc(context1, getServicesRequest)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SpanReaderPluginServer_GetServices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetServices'
type SpanReaderPluginServer_GetServices_Call struct {
	*mock.Call
}

// GetServices is a helper method to define mock.On call
//   - context1 context.Context
//   - getServicesRequest *storage_v1.GetServicesRequest
func (_e *SpanReaderPluginServer_Expecter) GetServices(context1 interface{}, getServicesRequest interface{}) *SpanReaderPluginServer_GetServices_Call {
	return &SpanReaderPluginServer_GetServices_Call{Call: _e.mock.On("GetServices", context1, getServicesRequest)}
}

func (_c *SpanReaderPluginServer_GetServices_Call) Run(run func(context1 context.Context, getServicesRequest *storage_v1.GetServicesRequest)) *SpanReaderPluginServer_GetServices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.GetServicesRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.GetServicesRequest)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *SpanReaderPluginServer_GetServices_Call) Return(getServicesResponse *storage_v1.GetServicesResponse, err error) *SpanReaderPluginServer_GetServices_Call {
	_c.Call.Return(getServicesResponse, err)
	return _c
}

func (_c *SpanReaderPluginServer_GetServices_Call) RunAndReturn(run func(context1 context.Context, getServicesRequest *storage_v1.GetServicesRequest) (*storage_v1.GetServicesResponse, error)) *SpanReaderPluginServer_GetServices_Call {
	_c.Call.Return(run)
	return _c
}

// GetTrace provides a mock function for the type SpanReaderPluginServer
func (_mock *SpanReaderPluginServer) GetTrace(getTraceRequest *storage_v1.GetTraceRequest, spanReaderPlugin_GetTraceServer storage_v1.SpanReaderPlugin_GetTraceServer) error {
	ret := _mock.Called(getTraceRequest, spanReaderPlugin_GetTraceServer)

	if len(ret) == 0 {
		panic("no return value specified for GetTrace")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(*storage_v1.GetTraceRequest, storage_v1.SpanReaderPlugin_GetTraceServer) error); ok {
		r0 = returnFunc(getTraceRequest, spanReaderPlugin_GetTraceServer)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPluginServer_GetTrace_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTrace'
type SpanReaderPluginServer_GetTrace_Call struct {
	*mock.Call
}

// GetTrace is a helper method to define mock.On call
//   - getTraceRequest *storage_v1.GetTraceRequest
//   - spanReaderPlugin_GetTraceServer storage_v1.SpanReaderPlugin_GetTraceServer
func (_e *SpanReaderPluginServer_Expecter) GetTrace(getTraceRequest interface{}, spanReaderPlugin_GetTraceServer interface{}) *SpanReaderPluginServer_GetTrace_Call {
	return &SpanReaderPluginServer_GetTrace_Call{Call: _e.mock.On("GetTrace", getTraceRequest, spanReaderPlugin_GetTraceServer)}
}

func (_c *SpanReaderPluginServer_GetTrace_Call) Run(run func(getTraceRequest *storage_v1.GetTraceRequest, spanReaderPlugin_GetTraceServer storage_v1.SpanReaderPlugin_GetTraceServer)) *SpanReaderPluginServer_GetTrace_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *storage_v1.GetTraceRequest
		if args[0] != nil {
			arg0 = args[0].(*storage_v1.GetTraceRequest)
		}
		var arg1 storage_v1.SpanReaderPlugin_GetTraceServer
		if args[1] != nil {
			arg1 = args[1].(storage_v1.SpanReaderPlugin_GetTraceServer)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *SpanReaderPluginServer_GetTrace_Call) Return(err error) *SpanReaderPluginServer_GetTrace_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPluginServer_GetTrace_Call) RunAndReturn(run func(getTraceRequest *storage_v1.GetTraceRequest, spanReaderPlugin_GetTraceServer storage_v1.SpanReaderPlugin_GetTraceServer) error) *SpanReaderPluginServer_GetTrace_Call {
	_c.Call.Return(run)
	return _c
}

// NewSpanReaderPlugin_GetTraceServer creates a new instance of SpanReaderPlugin_GetTraceServer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSpanReaderPlugin_GetTraceServer(t interface {
	mock.TestingT
	Cleanup(func())
}) *SpanReaderPlugin_GetTraceServer {
	mock := &SpanReaderPlugin_GetTraceServer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SpanReaderPlugin_GetTraceServer is an autogenerated mock type for the SpanReaderPlugin_GetTraceServer type
type SpanReaderPlugin_GetTraceServer struct {
	mock.Mock
}

type SpanReaderPlugin_GetTraceServer_Expecter struct {
	mock *mock.Mock
}

func (_m *SpanReaderPlugin_GetTraceServer) EXPECT() *SpanReaderPlugin_GetTraceServer_Expecter {
	return &SpanReaderPlugin_GetTraceServer_Expecter{mock: &_m.Mock}
}

// Context provides a mock function for the type SpanReaderPlugin_GetTraceServer
func (_mock *SpanReaderPlugin_GetTraceServer) Context() context.Context {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Context")
	}

	var r0 context.Context
	if returnFunc, ok := ret.Get(0).(func() context.Context); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(context.Context)
		}
	}
	return r0
}

// SpanReaderPlugin_GetTraceServer_Context_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Context'
type SpanReaderPlugin_GetTraceServer_Context_Call struct {
	*mock.Call
}

// Context is a helper method to define mock.On call
func (_e *SpanReaderPlugin_GetTraceServer_Expecter) Context() *SpanReaderPlugin_GetTraceServer_Context_Call {
	return &SpanReaderPlugin_GetTraceServer_Context_Call{Call: _e.mock.On("Context")}
}

func (_c *SpanReaderPlugin_GetTraceServer_Context_Call) Run(run func()) *SpanReaderPlugin_GetTraceServer_Context_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *SpanReaderPlugin_GetTraceServer_Context_Call) Return(context1 context.Context) *SpanReaderPlugin_GetTraceServer_Context_Call {
	_c.Call.Return(context1)
	return _c
}

func (_c *SpanReaderPlugin_GetTraceServer_Context_Call) RunAndReturn(run func() context.Context) *SpanReaderPlugin_GetTraceServer_Context_Call {
	_c.Call.Return(run)
	return _c
}

// RecvMsg provides a mock function for the type SpanReaderPlugin_GetTraceServer
func (_mock *SpanReaderPlugin_GetTraceServer) RecvMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for RecvMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_GetTraceServer_RecvMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecvMsg'
type SpanReaderPlugin_GetTraceServer_RecvMsg_Call struct {
	*mock.Call
}

// RecvMsg is a helper method to define mock.On call
//   - m any
func (_e *SpanReaderPlugin_GetTraceServer_Expecter) RecvMsg(m interface{}) *SpanReaderPlugin_GetTraceServer_RecvMsg_Call {
	return &SpanReaderPlugin_GetTraceServer_RecvMsg_Call{Call: _e.mock.On("RecvMsg", m)}
}

func (_c *SpanReaderPlugin_GetTraceServer_RecvMsg_Call) Run(run func(m any)) *SpanReaderPlugin_GetTraceServer_RecvMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_GetTraceServer_RecvMsg_Call) Return(err error) *SpanReaderPlugin_GetTraceServer_RecvMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_GetTraceServer_RecvMsg_Call) RunAndReturn(run func(m any) error) *SpanReaderPlugin_GetTraceServer_RecvMsg_Call {
	_c.Call.Return(run)
	return _c
}

// Send provides a mock function for the type SpanReaderPlugin_GetTraceServer
func (_mock *SpanReaderPlugin_GetTraceServer) Send(spansResponseChunk *storage_v1.SpansResponseChunk) error {
	ret := _mock.Called(spansResponseChunk)

	if len(ret) == 0 {
		panic("no return value specified for Send")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(*storage_v1.SpansResponseChunk) error); ok {
		r0 = returnFunc(spansResponseChunk)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_GetTraceServer_Send_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Send'
type SpanReaderPlugin_GetTraceServer_Send_Call struct {
	*mock.Call
}

// Send is a helper method to define mock.On call
//   - spansResponseChunk *storage_v1.SpansResponseChunk
func (_e *SpanReaderPlugin_GetTraceServer_Expecter) Send(spansResponseChunk interface{}) *SpanReaderPlugin_GetTraceServer_Send_Call {
	return &SpanReaderPlugin_GetTraceServer_Send_Call{Call: _e.mock.On("Send", spansResponseChunk)}
}

func (_c *SpanReaderPlugin_GetTraceServer_Send_Call) Run(run func(spansResponseChunk *storage_v1.SpansResponseChunk)) *SpanReaderPlugin_GetTraceServer_Send_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *storage_v1.SpansResponseChunk
		if args[0] != nil {
			arg0 = args[0].(*storage_v1.SpansResponseChunk)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_GetTraceServer_Send_Call) Return(err error) *SpanReaderPlugin_GetTraceServer_Send_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_GetTraceServer_Send_Call) RunAndReturn(run func(spansResponseChunk *storage_v1.SpansResponseChunk) error) *SpanReaderPlugin_GetTraceServer_Send_Call {
	_c.Call.Return(run)
	return _c
}

// SendHeader provides a mock function for the type SpanReaderPlugin_GetTraceServer
func (_mock *SpanReaderPlugin_GetTraceServer) SendHeader(mD metadata.MD) error {
	ret := _mock.Called(mD)

	if len(ret) == 0 {
		panic("no return value specified for SendHeader")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(metadata.MD) error); ok {
		r0 = returnFunc(mD)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_GetTraceServer_SendHeader_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendHeader'
type SpanReaderPlugin_GetTraceServer_SendHeader_Call struct {
	*mock.Call
}

// SendHeader is a helper method to define mock.On call
//   - mD metadata.MD
func (_e *SpanReaderPlugin_GetTraceServer_Expecter) SendHeader(mD interface{}) *SpanReaderPlugin_GetTraceServer_SendHeader_Call {
	return &SpanReaderPlugin_GetTraceServer_SendHeader_Call{Call: _e.mock.On("SendHeader", mD)}
}

func (_c *SpanReaderPlugin_GetTraceServer_SendHeader_Call) Run(run func(mD metadata.MD)) *SpanReaderPlugin_GetTraceServer_SendHeader_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 metadata.MD
		if args[0] != nil {
			arg0 = args[0].(metadata.MD)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_GetTraceServer_SendHeader_Call) Return(err error) *SpanReaderPlugin_GetTraceServer_SendHeader_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_GetTraceServer_SendHeader_Call) RunAndReturn(run func(mD metadata.MD) error) *SpanReaderPlugin_GetTraceServer_SendHeader_Call {
	_c.Call.Return(run)
	return _c
}

// SendMsg provides a mock function for the type SpanReaderPlugin_GetTraceServer
func (_mock *SpanReaderPlugin_GetTraceServer) SendMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for SendMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_GetTraceServer_SendMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendMsg'
type SpanReaderPlugin_GetTraceServer_SendMsg_Call struct {
	*mock.Call
}

// SendMsg is a helper method to define mock.On call
//   - m any
func (_e *SpanReaderPlugin_GetTraceServer_Expecter) SendMsg(m interface{}) *SpanReaderPlugin_GetTraceServer_SendMsg_Call {
	return &SpanReaderPlugin_GetTraceServer_SendMsg_Call{Call: _e.mock.On("SendMsg", m)}
}

func (_c *SpanReaderPlugin_GetTraceServer_SendMsg_Call) Run(run func(m any)) *SpanReaderPlugin_GetTraceServer_SendMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_GetTraceServer_SendMsg_Call) Return(err error) *SpanReaderPlugin_GetTraceServer_SendMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_GetTraceServer_SendMsg_Call) RunAndReturn(run func(m any) error) *SpanReaderPlugin_GetTraceServer_SendMsg_Call {
	_c.Call.Return(run)
	return _c
}

// SetHeader provides a mock function for the type SpanReaderPlugin_GetTraceServer
func (_mock *SpanReaderPlugin_GetTraceServer) SetHeader(mD metadata.MD) error {
	ret := _mock.Called(mD)

	if len(ret) == 0 {
		panic("no return value specified for SetHeader")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(metadata.MD) error); ok {
		r0 = returnFunc(mD)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_GetTraceServer_SetHeader_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetHeader'
type SpanReaderPlugin_GetTraceServer_SetHeader_Call struct {
	*mock.Call
}

// SetHeader is a helper method to define mock.On call
//   - mD metadata.MD
func (_e *SpanReaderPlugin_GetTraceServer_Expecter) SetHeader(mD interface{}) *SpanReaderPlugin_GetTraceServer_SetHeader_Call {
	return &SpanReaderPlugin_GetTraceServer_SetHeader_Call{Call: _e.mock.On("SetHeader", mD)}
}

func (_c *SpanReaderPlugin_GetTraceServer_SetHeader_Call) Run(run func(mD metadata.MD)) *SpanReaderPlugin_GetTraceServer_SetHeader_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 metadata.MD
		if args[0] != nil {
			arg0 = args[0].(metadata.MD)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_GetTraceServer_SetHeader_Call) Return(err error) *SpanReaderPlugin_GetTraceServer_SetHeader_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_GetTraceServer_SetHeader_Call) RunAndReturn(run func(mD metadata.MD) error) *SpanReaderPlugin_GetTraceServer_SetHeader_Call {
	_c.Call.Return(run)
	return _c
}

// SetTrailer provides a mock function for the type SpanReaderPlugin_GetTraceServer
func (_mock *SpanReaderPlugin_GetTraceServer) SetTrailer(mD metadata.MD) {
	_mock.Called(mD)
	return
}

// SpanReaderPlugin_GetTraceServer_SetTrailer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetTrailer'
type SpanReaderPlugin_GetTraceServer_SetTrailer_Call struct {
	*mock.Call
}

// SetTrailer is a helper method to define mock.On call
//   - mD metadata.MD
func (_e *SpanReaderPlugin_GetTraceServer_Expecter) SetTrailer(mD interface{}) *SpanReaderPlugin_GetTraceServer_SetTrailer_Call {
	return &SpanReaderPlugin_GetTraceServer_SetTrailer_Call{Call: _e.mock.On("SetTrailer", mD)}
}

func (_c *SpanReaderPlugin_GetTraceServer_SetTrailer_Call) Run(run func(mD metadata.MD)) *SpanReaderPlugin_GetTraceServer_SetTrailer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 metadata.MD
		if args[0] != nil {
			arg0 = args[0].(metadata.MD)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_GetTraceServer_SetTrailer_Call) Return() *SpanReaderPlugin_GetTraceServer_SetTrailer_Call {
	_c.Call.Return()
	return _c
}

func (_c *SpanReaderPlugin_GetTraceServer_SetTrailer_Call) RunAndReturn(run func(mD metadata.MD)) *SpanReaderPlugin_GetTraceServer_SetTrailer_Call {
	_c.Run(run)
	return _c
}

// NewSpanReaderPlugin_FindTracesServer creates a new instance of SpanReaderPlugin_FindTracesServer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSpanReaderPlugin_FindTracesServer(t interface {
	mock.TestingT
	Cleanup(func())
}) *SpanReaderPlugin_FindTracesServer {
	mock := &SpanReaderPlugin_FindTracesServer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SpanReaderPlugin_FindTracesServer is an autogenerated mock type for the SpanReaderPlugin_FindTracesServer type
type SpanReaderPlugin_FindTracesServer struct {
	mock.Mock
}

type SpanReaderPlugin_FindTracesServer_Expecter struct {
	mock *mock.Mock
}

func (_m *SpanReaderPlugin_FindTracesServer) EXPECT() *SpanReaderPlugin_FindTracesServer_Expecter {
	return &SpanReaderPlugin_FindTracesServer_Expecter{mock: &_m.Mock}
}

// Context provides a mock function for the type SpanReaderPlugin_FindTracesServer
func (_mock *SpanReaderPlugin_FindTracesServer) Context() context.Context {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Context")
	}

	var r0 context.Context
	if returnFunc, ok := ret.Get(0).(func() context.Context); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(context.Context)
		}
	}
	return r0
}

// SpanReaderPlugin_FindTracesServer_Context_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Context'
type SpanReaderPlugin_FindTracesServer_Context_Call struct {
	*mock.Call
}

// Context is a helper method to define mock.On call
func (_e *SpanReaderPlugin_FindTracesServer_Expecter) Context() *SpanReaderPlugin_FindTracesServer_Context_Call {
	return &SpanReaderPlugin_FindTracesServer_Context_Call{Call: _e.mock.On("Context")}
}

func (_c *SpanReaderPlugin_FindTracesServer_Context_Call) Run(run func()) *SpanReaderPlugin_FindTracesServer_Context_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *SpanReaderPlugin_FindTracesServer_Context_Call) Return(context1 context.Context) *SpanReaderPlugin_FindTracesServer_Context_Call {
	_c.Call.Return(context1)
	return _c
}

func (_c *SpanReaderPlugin_FindTracesServer_Context_Call) RunAndReturn(run func() context.Context) *SpanReaderPlugin_FindTracesServer_Context_Call {
	_c.Call.Return(run)
	return _c
}

// RecvMsg provides a mock function for the type SpanReaderPlugin_FindTracesServer
func (_mock *SpanReaderPlugin_FindTracesServer) RecvMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for RecvMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_FindTracesServer_RecvMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecvMsg'
type SpanReaderPlugin_FindTracesServer_RecvMsg_Call struct {
	*mock.Call
}

// RecvMsg is a helper method to define mock.On call
//   - m any
func (_e *SpanReaderPlugin_FindTracesServer_Expecter) RecvMsg(m interface{}) *SpanReaderPlugin_FindTracesServer_RecvMsg_Call {
	return &SpanReaderPlugin_FindTracesServer_RecvMsg_Call{Call: _e.mock.On("RecvMsg", m)}
}

func (_c *SpanReaderPlugin_FindTracesServer_RecvMsg_Call) Run(run func(m any)) *SpanReaderPlugin_FindTracesServer_RecvMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_FindTracesServer_RecvMsg_Call) Return(err error) *SpanReaderPlugin_FindTracesServer_RecvMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_FindTracesServer_RecvMsg_Call) RunAndReturn(run func(m any) error) *SpanReaderPlugin_FindTracesServer_RecvMsg_Call {
	_c.Call.Return(run)
	return _c
}

// Send provides a mock function for the type SpanReaderPlugin_FindTracesServer
func (_mock *SpanReaderPlugin_FindTracesServer) Send(spansResponseChunk *storage_v1.SpansResponseChunk) error {
	ret := _mock.Called(spansResponseChunk)

	if len(ret) == 0 {
		panic("no return value specified for Send")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(*storage_v1.SpansResponseChunk) error); ok {
		r0 = returnFunc(spansResponseChunk)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_FindTracesServer_Send_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Send'
type SpanReaderPlugin_FindTracesServer_Send_Call struct {
	*mock.Call
}

// Send is a helper method to define mock.On call
//   - spansResponseChunk *storage_v1.SpansResponseChunk
func (_e *SpanReaderPlugin_FindTracesServer_Expecter) Send(spansResponseChunk interface{}) *SpanReaderPlugin_FindTracesServer_Send_Call {
	return &SpanReaderPlugin_FindTracesServer_Send_Call{Call: _e.mock.On("Send", spansResponseChunk)}
}

func (_c *SpanReaderPlugin_FindTracesServer_Send_Call) Run(run func(spansResponseChunk *storage_v1.SpansResponseChunk)) *SpanReaderPlugin_FindTracesServer_Send_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *storage_v1.SpansResponseChunk
		if args[0] != nil {
			arg0 = args[0].(*storage_v1.SpansResponseChunk)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_FindTracesServer_Send_Call) Return(err error) *SpanReaderPlugin_FindTracesServer_Send_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_FindTracesServer_Send_Call) RunAndReturn(run func(spansResponseChunk *storage_v1.SpansResponseChunk) error) *SpanReaderPlugin_FindTracesServer_Send_Call {
	_c.Call.Return(run)
	return _c
}

// SendHeader provides a mock function for the type SpanReaderPlugin_FindTracesServer
func (_mock *SpanReaderPlugin_FindTracesServer) SendHeader(mD metadata.MD) error {
	ret := _mock.Called(mD)

	if len(ret) == 0 {
		panic("no return value specified for SendHeader")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(metadata.MD) error); ok {
		r0 = returnFunc(mD)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_FindTracesServer_SendHeader_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendHeader'
type SpanReaderPlugin_FindTracesServer_SendHeader_Call struct {
	*mock.Call
}

// SendHeader is a helper method to define mock.On call
//   - mD metadata.MD
func (_e *SpanReaderPlugin_FindTracesServer_Expecter) SendHeader(mD interface{}) *SpanReaderPlugin_FindTracesServer_SendHeader_Call {
	return &SpanReaderPlugin_FindTracesServer_SendHeader_Call{Call: _e.mock.On("SendHeader", mD)}
}

func (_c *SpanReaderPlugin_FindTracesServer_SendHeader_Call) Run(run func(mD metadata.MD)) *SpanReaderPlugin_FindTracesServer_SendHeader_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 metadata.MD
		if args[0] != nil {
			arg0 = args[0].(metadata.MD)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_FindTracesServer_SendHeader_Call) Return(err error) *SpanReaderPlugin_FindTracesServer_SendHeader_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_FindTracesServer_SendHeader_Call) RunAndReturn(run func(mD metadata.MD) error) *SpanReaderPlugin_FindTracesServer_SendHeader_Call {
	_c.Call.Return(run)
	return _c
}

// SendMsg provides a mock function for the type SpanReaderPlugin_FindTracesServer
func (_mock *SpanReaderPlugin_FindTracesServer) SendMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for SendMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_FindTracesServer_SendMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendMsg'
type SpanReaderPlugin_FindTracesServer_SendMsg_Call struct {
	*mock.Call
}

// SendMsg is a helper method to define mock.On call
//   - m any
func (_e *SpanReaderPlugin_FindTracesServer_Expecter) SendMsg(m interface{}) *SpanReaderPlugin_FindTracesServer_SendMsg_Call {
	return &SpanReaderPlugin_FindTracesServer_SendMsg_Call{Call: _e.mock.On("SendMsg", m)}
}

func (_c *SpanReaderPlugin_FindTracesServer_SendMsg_Call) Run(run func(m any)) *SpanReaderPlugin_FindTracesServer_SendMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_FindTracesServer_SendMsg_Call) Return(err error) *SpanReaderPlugin_FindTracesServer_SendMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_FindTracesServer_SendMsg_Call) RunAndReturn(run func(m any) error) *SpanReaderPlugin_FindTracesServer_SendMsg_Call {
	_c.Call.Return(run)
	return _c
}

// SetHeader provides a mock function for the type SpanReaderPlugin_FindTracesServer
func (_mock *SpanReaderPlugin_FindTracesServer) SetHeader(mD metadata.MD) error {
	ret := _mock.Called(mD)

	if len(ret) == 0 {
		panic("no return value specified for SetHeader")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(metadata.MD) error); ok {
		r0 = returnFunc(mD)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// SpanReaderPlugin_FindTracesServer_SetHeader_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetHeader'
type SpanReaderPlugin_FindTracesServer_SetHeader_Call struct {
	*mock.Call
}

// SetHeader is a helper method to define mock.On call
//   - mD metadata.MD
func (_e *SpanReaderPlugin_FindTracesServer_Expecter) SetHeader(mD interface{}) *SpanReaderPlugin_FindTracesServer_SetHeader_Call {
	return &SpanReaderPlugin_FindTracesServer_SetHeader_Call{Call: _e.mock.On("SetHeader", mD)}
}

func (_c *SpanReaderPlugin_FindTracesServer_SetHeader_Call) Run(run func(mD metadata.MD)) *SpanReaderPlugin_FindTracesServer_SetHeader_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 metadata.MD
		if args[0] != nil {
			arg0 = args[0].(metadata.MD)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_FindTracesServer_SetHeader_Call) Return(err error) *SpanReaderPlugin_FindTracesServer_SetHeader_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *SpanReaderPlugin_FindTracesServer_SetHeader_Call) RunAndReturn(run func(mD metadata.MD) error) *SpanReaderPlugin_FindTracesServer_SetHeader_Call {
	_c.Call.Return(run)
	return _c
}

// SetTrailer provides a mock function for the type SpanReaderPlugin_FindTracesServer
func (_mock *SpanReaderPlugin_FindTracesServer) SetTrailer(mD metadata.MD) {
	_mock.Called(mD)
	return
}

// SpanReaderPlugin_FindTracesServer_SetTrailer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetTrailer'
type SpanReaderPlugin_FindTracesServer_SetTrailer_Call struct {
	*mock.Call
}

// SetTrailer is a helper method to define mock.On call
//   - mD metadata.MD
func (_e *SpanReaderPlugin_FindTracesServer_Expecter) SetTrailer(mD interface{}) *SpanReaderPlugin_FindTracesServer_SetTrailer_Call {
	return &SpanReaderPlugin_FindTracesServer_SetTrailer_Call{Call: _e.mock.On("SetTrailer", mD)}
}

func (_c *SpanReaderPlugin_FindTracesServer_SetTrailer_Call) Run(run func(mD metadata.MD)) *SpanReaderPlugin_FindTracesServer_SetTrailer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 metadata.MD
		if args[0] != nil {
			arg0 = args[0].(metadata.MD)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SpanReaderPlugin_FindTracesServer_SetTrailer_Call) Return() *SpanReaderPlugin_FindTracesServer_SetTrailer_Call {
	_c.Call.Return()
	return _c
}

func (_c *SpanReaderPlugin_FindTracesServer_SetTrailer_Call) RunAndReturn(run func(mD metadata.MD)) *SpanReaderPlugin_FindTracesServer_SetTrailer_Call {
	_c.Run(run)
	return _c
}

// NewArchiveSpanWriterPluginClient creates a new instance of ArchiveSpanWriterPluginClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewArchiveSpanWriterPluginClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *ArchiveSpanWriterPluginClient {
	mock := &ArchiveSpanWriterPluginClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// ArchiveSpanWriterPluginClient is an autogenerated mock type for the ArchiveSpanWriterPluginClient type
type ArchiveSpanWriterPluginClient struct {
	mock.Mock
}

type ArchiveSpanWriterPluginClient_Expecter struct {
	mock *mock.Mock
}

func (_m *ArchiveSpanWriterPluginClient) EXPECT() *ArchiveSpanWriterPluginClient_Expecter {
	return &ArchiveSpanWriterPluginClient_Expecter{mock: &_m.Mock}
}

// WriteArchiveSpan provides a mock function for the type ArchiveSpanWriterPluginClient
func (_mock *ArchiveSpanWriterPluginClient) WriteArchiveSpan(ctx context.Context, in *storage_v1.WriteSpanRequest, opts ...grpc.CallOption) (*storage_v1.WriteSpanResponse, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, in, opts)
	} else {
		tmpRet = _mock.Called(ctx, in)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for WriteArchiveSpan")
	}

	var r0 *storage_v1.WriteSpanResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.WriteSpanRequest, ...grpc.CallOption) (*storage_v1.WriteSpanResponse, error)); ok {
		return returnFunc(ctx, in, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.WriteSpanRequest, ...grpc.CallOption) *storage_v1.WriteSpanResponse); ok {
		r0 = returnFunc(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.WriteSpanResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.WriteSpanRequest, ...grpc.CallOption) error); ok {
		r1 = returnFunc(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// ArchiveSpanWriterPluginClient_WriteArchiveSpan_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WriteArchiveSpan'
type ArchiveSpanWriterPluginClient_WriteArchiveSpan_Call struct {
	*mock.Call
}

// WriteArchiveSpan is a helper method to define mock.On call
//   - ctx context.Context
//   - in *storage_v1.WriteSpanRequest
//   - opts ...grpc.CallOption
func (_e *ArchiveSpanWriterPluginClient_Expecter) WriteArchiveSpan(ctx interface{}, in interface{}, opts ...interface{}) *ArchiveSpanWriterPluginClient_WriteArchiveSpan_Call {
	return &ArchiveSpanWriterPluginClient_WriteArchiveSpan_Call{Call: _e.mock.On("WriteArchiveSpan",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *ArchiveSpanWriterPluginClient_WriteArchiveSpan_Call) Run(run func(ctx context.Context, in *storage_v1.WriteSpanRequest, opts ...grpc.CallOption)) *ArchiveSpanWriterPluginClient_WriteArchiveSpan_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.WriteSpanRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.WriteSpanRequest)
		}
		var arg2 []grpc.CallOption
		var variadicArgs []grpc.CallOption
		if len(args) > 2 {
			variadicArgs = args[2].([]grpc.CallOption)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *ArchiveSpanWriterPluginClient_WriteArchiveSpan_Call) Return(writeSpanResponse *storage_v1.WriteSpanResponse, err error) *ArchiveSpanWriterPluginClient_WriteArchiveSpan_Call {
	_c.Call.Return(writeSpanResponse, err)
	return _c
}

func (_c *ArchiveSpanWriterPluginClient_WriteArchiveSpan_Call) RunAndReturn(run func(ctx context.Context, in *storage_v1.WriteSpanRequest, opts ...grpc.CallOption) (*storage_v1.WriteSpanResponse, error)) *ArchiveSpanWriterPluginClient_WriteArchiveSpan_Call {
	_c.Call.Return(run)
	return _c
}

// NewArchiveSpanWriterPluginServer creates a new instance of ArchiveSpanWriterPluginServer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewArchiveSpanWriterPluginServer(t interface {
	mock.TestingT
	Cleanup(func())
}) *ArchiveSpanWriterPluginServer {
	mock := &ArchiveSpanWriterPluginServer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// ArchiveSpanWriterPluginServer is an autogenerated mock type for the ArchiveSpanWriterPluginServer type
type ArchiveSpanWriterPluginServer struct {
	mock.Mock
}

type ArchiveSpanWriterPluginServer_Expecter struct {
	mock *mock.Mock
}

func (_m *ArchiveSpanWriterPluginServer) EXPECT() *ArchiveSpanWriterPluginServer_Expecter {
	return &ArchiveSpanWriterPluginServer_Expecter{mock: &_m.Mock}
}

// WriteArchiveSpan provides a mock function for the type ArchiveSpanWriterPluginServer
func (_mock *ArchiveSpanWriterPluginServer) WriteArchiveSpan(context1 context.Context, writeSpanRequest *storage_v1.WriteSpanRequest) (*storage_v1.WriteSpanResponse, error) {
	ret := _mock.Called(context1, writeSpanRequest)

	if len(ret) == 0 {
		panic("no return value specified for WriteArchiveSpan")
	}

	var r0 *storage_v1.WriteSpanResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.WriteSpanRequest) (*storage_v1.WriteSpanResponse, error)); ok {
		return returnFunc(context1, writeSpanRequest)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.WriteSpanRequest) *storage_v1.WriteSpanResponse); ok {
		r0 = returnFunc(context1, writeSpanRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.WriteSpanResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.WriteSpanRequest) error); ok {
		r1 = returnFunc(context1, writeSpanRequest)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// ArchiveSpanWriterPluginServer_WriteArchiveSpan_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WriteArchiveSpan'
type ArchiveSpanWriterPluginServer_WriteArchiveSpan_Call struct {
	*mock.Call
}

// WriteArchiveSpan is a helper method to define mock.On call
//   - context1 context.Context
//   - writeSpanRequest *storage_v1.WriteSpanRequest
func (_e *ArchiveSpanWriterPluginServer_Expecter) WriteArchiveSpan(context1 interface{}, writeSpanRequest interface{}) *ArchiveSpanWriterPluginServer_WriteArchiveSpan_Call {
	return &ArchiveSpanWriterPluginServer_WriteArchiveSpan_Call{Call: _e.mock.On("WriteArchiveSpan", context1, writeSpanRequest)}
}

func (_c *ArchiveSpanWriterPluginServer_WriteArchiveSpan_Call) Run(run func(context1 context.Context, writeSpanRequest *storage_v1.WriteSpanRequest)) *ArchiveSpanWriterPluginServer_WriteArchiveSpan_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.WriteSpanRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.WriteSpanRequest)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *ArchiveSpanWriterPluginServer_WriteArchiveSpan_Call) Return(writeSpanResponse *storage_v1.WriteSpanResponse, err error) *ArchiveSpanWriterPluginServer_WriteArchiveSpan_Call {
	_c.Call.Return(writeSpanResponse, err)
	return _c
}

func (_c *ArchiveSpanWriterPluginServer_WriteArchiveSpan_Call) RunAndReturn(run func(context1 context.Context, writeSpanRequest *storage_v1.WriteSpanRequest) (*storage_v1.WriteSpanResponse, error)) *ArchiveSpanWriterPluginServer_WriteArchiveSpan_Call {
	_c.Call.Return(run)
	return _c
}

// NewArchiveSpanReaderPluginClient creates a new instance of ArchiveSpanReaderPluginClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewArchiveSpanReaderPluginClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *ArchiveSpanReaderPluginClient {
	mock := &ArchiveSpanReaderPluginClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// ArchiveSpanReaderPluginClient is an autogenerated mock type for the ArchiveSpanReaderPluginClient type
type ArchiveSpanReaderPluginClient struct {
	mock.Mock
}

type ArchiveSpanReaderPluginClient_Expecter struct {
	mock *mock.Mock
}

func (_m *ArchiveSpanReaderPluginClient) EXPECT() *ArchiveSpanReaderPluginClient_Expecter {
	return &ArchiveSpanReaderPluginClient_Expecter{mock: &_m.Mock}
}

// GetArchiveTrace provides a mock function for the type ArchiveSpanReaderPluginClient
func (_mock *ArchiveSpanReaderPluginClient) GetArchiveTrace(ctx context.Context, in *storage_v1.GetTraceRequest, opts ...grpc.CallOption) (storage_v1.ArchiveSpanReaderPlugin_GetArchiveTraceClient, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, in, opts)
	} else {
		tmpRet = _mock.Called(ctx, in)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for GetArchiveTrace")
	}

	var r0 storage_v1.ArchiveSpanReaderPlugin_GetArchiveTraceClient
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetTraceRequest, ...grpc.CallOption) (storage_v1.ArchiveSpanReaderPlugin_GetArchiveTraceClient, error)); ok {
		return returnFunc(ctx, in, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetTraceRequest, ...grpc.CallOption) storage_v1.ArchiveSpanReaderPlugin_GetArchiveTraceClient); ok {
		r0 = returnFunc(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(storage_v1.ArchiveSpanReaderPlugin_GetArchiveTraceClient)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.GetTraceRequest, ...grpc.CallOption) error); ok {
		r1 = returnFunc(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// ArchiveSpanReaderPluginClient_GetArchiveTrace_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetArchiveTrace'
type ArchiveSpanReaderPluginClient_GetArchiveTrace_Call struct {
	*mock.Call
}

// GetArchiveTrace is a helper method to define mock.On call
//   - ctx context.Context
//   - in *storage_v1.GetTraceRequest
//   - opts ...grpc.CallOption
func (_e *ArchiveSpanReaderPluginClient_Expecter) GetArchiveTrace(ctx interface{}, in interface{}, opts ...interface{}) *ArchiveSpanReaderPluginClient_GetArchiveTrace_Call {
	return &ArchiveSpanReaderPluginClient_GetArchiveTrace_Call{Call: _e.mock.On("GetArchiveTrace",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *ArchiveSpanReaderPluginClient_GetArchiveTrace_Call) Run(run func(ctx context.Context, in *storage_v1.GetTraceRequest, opts ...grpc.CallOption)) *ArchiveSpanReaderPluginClient_GetArchiveTrace_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.GetTraceRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.GetTraceRequest)
		}
		var arg2 []grpc.CallOption
		var variadicArgs []grpc.CallOption
		if len(args) > 2 {
			variadicArgs = args[2].([]grpc.CallOption)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *ArchiveSpanReaderPluginClient_GetArchiveTrace_Call) Return(archiveSpanReaderPlugin_GetArchiveTraceClient storage_v1.ArchiveSpanReaderPlugin_GetArchiveTraceClient, err error) *ArchiveSpanReaderPluginClient_GetArchiveTrace_Call {
	_c.Call.Return(archiveSpanReaderPlugin_GetArchiveTraceClient, err)
	return _c
}

func (_c *ArchiveSpanReaderPluginClient_GetArchiveTrace_Call) RunAndReturn(run func(ctx context.Context, in *storage_v1.GetTraceRequest, opts ...grpc.CallOption) (storage_v1.ArchiveSpanReaderPlugin_GetArchiveTraceClient, error)) *ArchiveSpanReaderPluginClient_GetArchiveTrace_Call {
	_c.Call.Return(run)
	return _c
}

// NewArchiveSpanReaderPlugin_GetArchiveTraceClient creates a new instance of ArchiveSpanReaderPlugin_GetArchiveTraceClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewArchiveSpanReaderPlugin_GetArchiveTraceClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *ArchiveSpanReaderPlugin_GetArchiveTraceClient {
	mock := &ArchiveSpanReaderPlugin_GetArchiveTraceClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// ArchiveSpanReaderPlugin_GetArchiveTraceClient is an autogenerated mock type for the ArchiveSpanReaderPlugin_GetArchiveTraceClient type
type ArchiveSpanReaderPlugin_GetArchiveTraceClient struct {
	mock.Mock
}

type ArchiveSpanReaderPlugin_GetArchiveTraceClient_Expecter struct {
	mock *mock.Mock
}

func (_m *ArchiveSpanReaderPlugin_GetArchiveTraceClient) EXPECT() *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Expecter {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceClient_Expecter{mock: &_m.Mock}
}

// CloseSend provides a mock function for the type ArchiveSpanReaderPlugin_GetArchiveTraceClient
func (_mock *ArchiveSpanReaderPlugin_GetArchiveTraceClient) CloseSend() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for CloseSend")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// ArchiveSpanReaderPlugin_GetArchiveTraceClient_CloseSend_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CloseSend'
type ArchiveSpanReaderPlugin_GetArchiveTraceClient_CloseSend_Call struct {
	*mock.Call
}

// CloseSend is a helper method to define mock.On call
func (_e *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Expecter) CloseSend() *ArchiveSpanReaderPlugin_GetArchiveTraceClient_CloseSend_Call {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceClient_CloseSend_Call{Call: _e.mock.On("CloseSend")}
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_CloseSend_Call) Run(run func()) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_CloseSend_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_CloseSend_Call) Return(err error) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_CloseSend_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_CloseSend_Call) RunAndReturn(run func() error) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_CloseSend_Call {
	_c.Call.Return(run)
	return _c
}

// Context provides a mock function for the type ArchiveSpanReaderPlugin_GetArchiveTraceClient
func (_mock *ArchiveSpanReaderPlugin_GetArchiveTraceClient) Context() context.Context {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Context")
	}

	var r0 context.Context
	if returnFunc, ok := ret.Get(0).(func() context.Context); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(context.Context)
		}
	}
	return r0
}

// ArchiveSpanReaderPlugin_GetArchiveTraceClient_Context_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Context'
type ArchiveSpanReaderPlugin_GetArchiveTraceClient_Context_Call struct {
	*mock.Call
}

// Context is a helper method to define mock.On call
func (_e *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Expecter) Context() *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Context_Call {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceClient_Context_Call{Call: _e.mock.On("Context")}
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Context_Call) Run(run func()) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Context_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Context_Call) Return(context1 context.Context) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Context_Call {
	_c.Call.Return(context1)
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Context_Call) RunAndReturn(run func() context.Context) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Context_Call {
	_c.Call.Return(run)
	return _c
}

// Header provides a mock function for the type ArchiveSpanReaderPlugin_GetArchiveTraceClient
func (_mock *ArchiveSpanReaderPlugin_GetArchiveTraceClient) Header() (metadata.MD, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Header")
	}

	var r0 metadata.MD
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (metadata.MD, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() metadata.MD); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(metadata.MD)
		}
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// ArchiveSpanReaderPlugin_GetArchiveTraceClient_Header_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Header'
type ArchiveSpanReaderPlugin_GetArchiveTraceClient_Header_Call struct {
	*mock.Call
}

// Header is a helper method to define mock.On call
func (_e *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Expecter) Header() *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Header_Call {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceClient_Header_Call{Call: _e.mock.On("Header")}
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Header_Call) Run(run func()) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Header_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Header_Call) Return(mD metadata.MD, err error) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Header_Call {
	_c.Call.Return(mD, err)
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Header_Call) RunAndReturn(run func() (metadata.MD, error)) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Header_Call {
	_c.Call.Return(run)
	return _c
}

// Recv provides a mock function for the type ArchiveSpanReaderPlugin_GetArchiveTraceClient
func (_mock *ArchiveSpanReaderPlugin_GetArchiveTraceClient) Recv() (*storage_v1.SpansResponseChunk, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Recv")
	}

	var r0 *storage_v1.SpansResponseChunk
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (*storage_v1.SpansResponseChunk, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() *storage_v1.SpansResponseChunk); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.SpansResponseChunk)
		}
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// ArchiveSpanReaderPlugin_GetArchiveTraceClient_Recv_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Recv'
type ArchiveSpanReaderPlugin_GetArchiveTraceClient_Recv_Call struct {
	*mock.Call
}

// Recv is a helper method to define mock.On call
func (_e *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Expecter) Recv() *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Recv_Call {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceClient_Recv_Call{Call: _e.mock.On("Recv")}
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Recv_Call) Run(run func()) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Recv_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Recv_Call) Return(spansResponseChunk *storage_v1.SpansResponseChunk, err error) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Recv_Call {
	_c.Call.Return(spansResponseChunk, err)
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Recv_Call) RunAndReturn(run func() (*storage_v1.SpansResponseChunk, error)) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Recv_Call {
	_c.Call.Return(run)
	return _c
}

// RecvMsg provides a mock function for the type ArchiveSpanReaderPlugin_GetArchiveTraceClient
func (_mock *ArchiveSpanReaderPlugin_GetArchiveTraceClient) RecvMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for RecvMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// ArchiveSpanReaderPlugin_GetArchiveTraceClient_RecvMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecvMsg'
type ArchiveSpanReaderPlugin_GetArchiveTraceClient_RecvMsg_Call struct {
	*mock.Call
}

// RecvMsg is a helper method to define mock.On call
//   - m any
func (_e *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Expecter) RecvMsg(m interface{}) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_RecvMsg_Call {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceClient_RecvMsg_Call{Call: _e.mock.On("RecvMsg", m)}
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_RecvMsg_Call) Run(run func(m any)) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_RecvMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_RecvMsg_Call) Return(err error) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_RecvMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_RecvMsg_Call) RunAndReturn(run func(m any) error) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_RecvMsg_Call {
	_c.Call.Return(run)
	return _c
}

// SendMsg provides a mock function for the type ArchiveSpanReaderPlugin_GetArchiveTraceClient
func (_mock *ArchiveSpanReaderPlugin_GetArchiveTraceClient) SendMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for SendMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// ArchiveSpanReaderPlugin_GetArchiveTraceClient_SendMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendMsg'
type ArchiveSpanReaderPlugin_GetArchiveTraceClient_SendMsg_Call struct {
	*mock.Call
}

// SendMsg is a helper method to define mock.On call
//   - m any
func (_e *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Expecter) SendMsg(m interface{}) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_SendMsg_Call {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceClient_SendMsg_Call{Call: _e.mock.On("SendMsg", m)}
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_SendMsg_Call) Run(run func(m any)) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_SendMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_SendMsg_Call) Return(err error) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_SendMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_SendMsg_Call) RunAndReturn(run func(m any) error) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_SendMsg_Call {
	_c.Call.Return(run)
	return _c
}

// Trailer provides a mock function for the type ArchiveSpanReaderPlugin_GetArchiveTraceClient
func (_mock *ArchiveSpanReaderPlugin_GetArchiveTraceClient) Trailer() metadata.MD {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Trailer")
	}

	var r0 metadata.MD
	if returnFunc, ok := ret.Get(0).(func() metadata.MD); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(metadata.MD)
		}
	}
	return r0
}

// ArchiveSpanReaderPlugin_GetArchiveTraceClient_Trailer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Trailer'
type ArchiveSpanReaderPlugin_GetArchiveTraceClient_Trailer_Call struct {
	*mock.Call
}

// Trailer is a helper method to define mock.On call
func (_e *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Expecter) Trailer() *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Trailer_Call {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceClient_Trailer_Call{Call: _e.mock.On("Trailer")}
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Trailer_Call) Run(run func()) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Trailer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Trailer_Call) Return(mD metadata.MD) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Trailer_Call {
	_c.Call.Return(mD)
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Trailer_Call) RunAndReturn(run func() metadata.MD) *ArchiveSpanReaderPlugin_GetArchiveTraceClient_Trailer_Call {
	_c.Call.Return(run)
	return _c
}

// NewArchiveSpanReaderPluginServer creates a new instance of ArchiveSpanReaderPluginServer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewArchiveSpanReaderPluginServer(t interface {
	mock.TestingT
	Cleanup(func())
}) *ArchiveSpanReaderPluginServer {
	mock := &ArchiveSpanReaderPluginServer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// ArchiveSpanReaderPluginServer is an autogenerated mock type for the ArchiveSpanReaderPluginServer type
type ArchiveSpanReaderPluginServer struct {
	mock.Mock
}

type ArchiveSpanReaderPluginServer_Expecter struct {
	mock *mock.Mock
}

func (_m *ArchiveSpanReaderPluginServer) EXPECT() *ArchiveSpanReaderPluginServer_Expecter {
	return &ArchiveSpanReaderPluginServer_Expecter{mock: &_m.Mock}
}

// GetArchiveTrace provides a mock function for the type ArchiveSpanReaderPluginServer
func (_mock *ArchiveSpanReaderPluginServer) GetArchiveTrace(getTraceRequest *storage_v1.GetTraceRequest, archiveSpanReaderPlugin_GetArchiveTraceServer storage_v1.ArchiveSpanReaderPlugin_GetArchiveTraceServer) error {
	ret := _mock.Called(getTraceRequest, archiveSpanReaderPlugin_GetArchiveTraceServer)

	if len(ret) == 0 {
		panic("no return value specified for GetArchiveTrace")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(*storage_v1.GetTraceRequest, storage_v1.ArchiveSpanReaderPlugin_GetArchiveTraceServer) error); ok {
		r0 = returnFunc(getTraceRequest, archiveSpanReaderPlugin_GetArchiveTraceServer)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// ArchiveSpanReaderPluginServer_GetArchiveTrace_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetArchiveTrace'
type ArchiveSpanReaderPluginServer_GetArchiveTrace_Call struct {
	*mock.Call
}

// GetArchiveTrace is a helper method to define mock.On call
//   - getTraceRequest *storage_v1.GetTraceRequest
//   - archiveSpanReaderPlugin_GetArchiveTraceServer storage_v1.ArchiveSpanReaderPlugin_GetArchiveTraceServer
func (_e *ArchiveSpanReaderPluginServer_Expecter) GetArchiveTrace(getTraceRequest interface{}, archiveSpanReaderPlugin_GetArchiveTraceServer interface{}) *ArchiveSpanReaderPluginServer_GetArchiveTrace_Call {
	return &ArchiveSpanReaderPluginServer_GetArchiveTrace_Call{Call: _e.mock.On("GetArchiveTrace", getTraceRequest, archiveSpanReaderPlugin_GetArchiveTraceServer)}
}

func (_c *ArchiveSpanReaderPluginServer_GetArchiveTrace_Call) Run(run func(getTraceRequest *storage_v1.GetTraceRequest, archiveSpanReaderPlugin_GetArchiveTraceServer storage_v1.ArchiveSpanReaderPlugin_GetArchiveTraceServer)) *ArchiveSpanReaderPluginServer_GetArchiveTrace_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *storage_v1.GetTraceRequest
		if args[0] != nil {
			arg0 = args[0].(*storage_v1.GetTraceRequest)
		}
		var arg1 storage_v1.ArchiveSpanReaderPlugin_GetArchiveTraceServer
		if args[1] != nil {
			arg1 = args[1].(storage_v1.ArchiveSpanReaderPlugin_GetArchiveTraceServer)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *ArchiveSpanReaderPluginServer_GetArchiveTrace_Call) Return(err error) *ArchiveSpanReaderPluginServer_GetArchiveTrace_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *ArchiveSpanReaderPluginServer_GetArchiveTrace_Call) RunAndReturn(run func(getTraceRequest *storage_v1.GetTraceRequest, archiveSpanReaderPlugin_GetArchiveTraceServer storage_v1.ArchiveSpanReaderPlugin_GetArchiveTraceServer) error) *ArchiveSpanReaderPluginServer_GetArchiveTrace_Call {
	_c.Call.Return(run)
	return _c
}

// NewArchiveSpanReaderPlugin_GetArchiveTraceServer creates a new instance of ArchiveSpanReaderPlugin_GetArchiveTraceServer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewArchiveSpanReaderPlugin_GetArchiveTraceServer(t interface {
	mock.TestingT
	Cleanup(func())
}) *ArchiveSpanReaderPlugin_GetArchiveTraceServer {
	mock := &ArchiveSpanReaderPlugin_GetArchiveTraceServer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// ArchiveSpanReaderPlugin_GetArchiveTraceServer is an autogenerated mock type for the ArchiveSpanReaderPlugin_GetArchiveTraceServer type
type ArchiveSpanReaderPlugin_GetArchiveTraceServer struct {
	mock.Mock
}

type ArchiveSpanReaderPlugin_GetArchiveTraceServer_Expecter struct {
	mock *mock.Mock
}

func (_m *ArchiveSpanReaderPlugin_GetArchiveTraceServer) EXPECT() *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Expecter {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceServer_Expecter{mock: &_m.Mock}
}

// Context provides a mock function for the type ArchiveSpanReaderPlugin_GetArchiveTraceServer
func (_mock *ArchiveSpanReaderPlugin_GetArchiveTraceServer) Context() context.Context {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Context")
	}

	var r0 context.Context
	if returnFunc, ok := ret.Get(0).(func() context.Context); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(context.Context)
		}
	}
	return r0
}

// ArchiveSpanReaderPlugin_GetArchiveTraceServer_Context_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Context'
type ArchiveSpanReaderPlugin_GetArchiveTraceServer_Context_Call struct {
	*mock.Call
}

// Context is a helper method to define mock.On call
func (_e *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Expecter) Context() *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Context_Call {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceServer_Context_Call{Call: _e.mock.On("Context")}
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Context_Call) Run(run func()) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Context_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Context_Call) Return(context1 context.Context) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Context_Call {
	_c.Call.Return(context1)
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Context_Call) RunAndReturn(run func() context.Context) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Context_Call {
	_c.Call.Return(run)
	return _c
}

// RecvMsg provides a mock function for the type ArchiveSpanReaderPlugin_GetArchiveTraceServer
func (_mock *ArchiveSpanReaderPlugin_GetArchiveTraceServer) RecvMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for RecvMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// ArchiveSpanReaderPlugin_GetArchiveTraceServer_RecvMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RecvMsg'
type ArchiveSpanReaderPlugin_GetArchiveTraceServer_RecvMsg_Call struct {
	*mock.Call
}

// RecvMsg is a helper method to define mock.On call
//   - m any
func (_e *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Expecter) RecvMsg(m interface{}) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_RecvMsg_Call {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceServer_RecvMsg_Call{Call: _e.mock.On("RecvMsg", m)}
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_RecvMsg_Call) Run(run func(m any)) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_RecvMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_RecvMsg_Call) Return(err error) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_RecvMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_RecvMsg_Call) RunAndReturn(run func(m any) error) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_RecvMsg_Call {
	_c.Call.Return(run)
	return _c
}

// Send provides a mock function for the type ArchiveSpanReaderPlugin_GetArchiveTraceServer
func (_mock *ArchiveSpanReaderPlugin_GetArchiveTraceServer) Send(spansResponseChunk *storage_v1.SpansResponseChunk) error {
	ret := _mock.Called(spansResponseChunk)

	if len(ret) == 0 {
		panic("no return value specified for Send")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(*storage_v1.SpansResponseChunk) error); ok {
		r0 = returnFunc(spansResponseChunk)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// ArchiveSpanReaderPlugin_GetArchiveTraceServer_Send_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Send'
type ArchiveSpanReaderPlugin_GetArchiveTraceServer_Send_Call struct {
	*mock.Call
}

// Send is a helper method to define mock.On call
//   - spansResponseChunk *storage_v1.SpansResponseChunk
func (_e *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Expecter) Send(spansResponseChunk interface{}) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Send_Call {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceServer_Send_Call{Call: _e.mock.On("Send", spansResponseChunk)}
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Send_Call) Run(run func(spansResponseChunk *storage_v1.SpansResponseChunk)) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Send_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *storage_v1.SpansResponseChunk
		if args[0] != nil {
			arg0 = args[0].(*storage_v1.SpansResponseChunk)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Send_Call) Return(err error) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Send_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Send_Call) RunAndReturn(run func(spansResponseChunk *storage_v1.SpansResponseChunk) error) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Send_Call {
	_c.Call.Return(run)
	return _c
}

// SendHeader provides a mock function for the type ArchiveSpanReaderPlugin_GetArchiveTraceServer
func (_mock *ArchiveSpanReaderPlugin_GetArchiveTraceServer) SendHeader(mD metadata.MD) error {
	ret := _mock.Called(mD)

	if len(ret) == 0 {
		panic("no return value specified for SendHeader")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(metadata.MD) error); ok {
		r0 = returnFunc(mD)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendHeader_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendHeader'
type ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendHeader_Call struct {
	*mock.Call
}

// SendHeader is a helper method to define mock.On call
//   - mD metadata.MD
func (_e *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Expecter) SendHeader(mD interface{}) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendHeader_Call {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendHeader_Call{Call: _e.mock.On("SendHeader", mD)}
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendHeader_Call) Run(run func(mD metadata.MD)) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendHeader_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 metadata.MD
		if args[0] != nil {
			arg0 = args[0].(metadata.MD)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendHeader_Call) Return(err error) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendHeader_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendHeader_Call) RunAndReturn(run func(mD metadata.MD) error) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendHeader_Call {
	_c.Call.Return(run)
	return _c
}

// SendMsg provides a mock function for the type ArchiveSpanReaderPlugin_GetArchiveTraceServer
func (_mock *ArchiveSpanReaderPlugin_GetArchiveTraceServer) SendMsg(m any) error {
	ret := _mock.Called(m)

	if len(ret) == 0 {
		panic("no return value specified for SendMsg")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(any) error); ok {
		r0 = returnFunc(m)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendMsg_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendMsg'
type ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendMsg_Call struct {
	*mock.Call
}

// SendMsg is a helper method to define mock.On call
//   - m any
func (_e *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Expecter) SendMsg(m interface{}) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendMsg_Call {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendMsg_Call{Call: _e.mock.On("SendMsg", m)}
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendMsg_Call) Run(run func(m any)) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendMsg_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendMsg_Call) Return(err error) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendMsg_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendMsg_Call) RunAndReturn(run func(m any) error) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SendMsg_Call {
	_c.Call.Return(run)
	return _c
}

// SetHeader provides a mock function for the type ArchiveSpanReaderPlugin_GetArchiveTraceServer
func (_mock *ArchiveSpanReaderPlugin_GetArchiveTraceServer) SetHeader(mD metadata.MD) error {
	ret := _mock.Called(mD)

	if len(ret) == 0 {
		panic("no return value specified for SetHeader")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(metadata.MD) error); ok {
		r0 = returnFunc(mD)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetHeader_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetHeader'
type ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetHeader_Call struct {
	*mock.Call
}

// SetHeader is a helper method to define mock.On call
//   - mD metadata.MD
func (_e *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Expecter) SetHeader(mD interface{}) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetHeader_Call {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetHeader_Call{Call: _e.mock.On("SetHeader", mD)}
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetHeader_Call) Run(run func(mD metadata.MD)) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetHeader_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 metadata.MD
		if args[0] != nil {
			arg0 = args[0].(metadata.MD)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetHeader_Call) Return(err error) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetHeader_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetHeader_Call) RunAndReturn(run func(mD metadata.MD) error) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetHeader_Call {
	_c.Call.Return(run)
	return _c
}

// SetTrailer provides a mock function for the type ArchiveSpanReaderPlugin_GetArchiveTraceServer
func (_mock *ArchiveSpanReaderPlugin_GetArchiveTraceServer) SetTrailer(mD metadata.MD) {
	_mock.Called(mD)
	return
}

// ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetTrailer_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SetTrailer'
type ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetTrailer_Call struct {
	*mock.Call
}

// SetTrailer is a helper method to define mock.On call
//   - mD metadata.MD
func (_e *ArchiveSpanReaderPlugin_GetArchiveTraceServer_Expecter) SetTrailer(mD interface{}) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetTrailer_Call {
	return &ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetTrailer_Call{Call: _e.mock.On("SetTrailer", mD)}
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetTrailer_Call) Run(run func(mD metadata.MD)) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetTrailer_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 metadata.MD
		if args[0] != nil {
			arg0 = args[0].(metadata.MD)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetTrailer_Call) Return() *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetTrailer_Call {
	_c.Call.Return()
	return _c
}

func (_c *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetTrailer_Call) RunAndReturn(run func(mD metadata.MD)) *ArchiveSpanReaderPlugin_GetArchiveTraceServer_SetTrailer_Call {
	_c.Run(run)
	return _c
}

// NewDependenciesReaderPluginClient creates a new instance of DependenciesReaderPluginClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDependenciesReaderPluginClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *DependenciesReaderPluginClient {
	mock := &DependenciesReaderPluginClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// DependenciesReaderPluginClient is an autogenerated mock type for the DependenciesReaderPluginClient type
type DependenciesReaderPluginClient struct {
	mock.Mock
}

type DependenciesReaderPluginClient_Expecter struct {
	mock *mock.Mock
}

func (_m *DependenciesReaderPluginClient) EXPECT() *DependenciesReaderPluginClient_Expecter {
	return &DependenciesReaderPluginClient_Expecter{mock: &_m.Mock}
}

// GetDependencies provides a mock function for the type DependenciesReaderPluginClient
func (_mock *DependenciesReaderPluginClient) GetDependencies(ctx context.Context, in *storage_v1.GetDependenciesRequest, opts ...grpc.CallOption) (*storage_v1.GetDependenciesResponse, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, in, opts)
	} else {
		tmpRet = _mock.Called(ctx, in)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for GetDependencies")
	}

	var r0 *storage_v1.GetDependenciesResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetDependenciesRequest, ...grpc.CallOption) (*storage_v1.GetDependenciesResponse, error)); ok {
		return returnFunc(ctx, in, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetDependenciesRequest, ...grpc.CallOption) *storage_v1.GetDependenciesResponse); ok {
		r0 = returnFunc(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.GetDependenciesResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.GetDependenciesRequest, ...grpc.CallOption) error); ok {
		r1 = returnFunc(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// DependenciesReaderPluginClient_GetDependencies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDependencies'
type DependenciesReaderPluginClient_GetDependencies_Call struct {
	*mock.Call
}

// GetDependencies is a helper method to define mock.On call
//   - ctx context.Context
//   - in *storage_v1.GetDependenciesRequest
//   - opts ...grpc.CallOption
func (_e *DependenciesReaderPluginClient_Expecter) GetDependencies(ctx interface{}, in interface{}, opts ...interface{}) *DependenciesReaderPluginClient_GetDependencies_Call {
	return &DependenciesReaderPluginClient_GetDependencies_Call{Call: _e.mock.On("GetDependencies",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *DependenciesReaderPluginClient_GetDependencies_Call) Run(run func(ctx context.Context, in *storage_v1.GetDependenciesRequest, opts ...grpc.CallOption)) *DependenciesReaderPluginClient_GetDependencies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.GetDependenciesRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.GetDependenciesRequest)
		}
		var arg2 []grpc.CallOption
		var variadicArgs []grpc.CallOption
		if len(args) > 2 {
			variadicArgs = args[2].([]grpc.CallOption)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *DependenciesReaderPluginClient_GetDependencies_Call) Return(getDependenciesResponse *storage_v1.GetDependenciesResponse, err error) *DependenciesReaderPluginClient_GetDependencies_Call {
	_c.Call.Return(getDependenciesResponse, err)
	return _c
}

func (_c *DependenciesReaderPluginClient_GetDependencies_Call) RunAndReturn(run func(ctx context.Context, in *storage_v1.GetDependenciesRequest, opts ...grpc.CallOption) (*storage_v1.GetDependenciesResponse, error)) *DependenciesReaderPluginClient_GetDependencies_Call {
	_c.Call.Return(run)
	return _c
}

// NewDependenciesReaderPluginServer creates a new instance of DependenciesReaderPluginServer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDependenciesReaderPluginServer(t interface {
	mock.TestingT
	Cleanup(func())
}) *DependenciesReaderPluginServer {
	mock := &DependenciesReaderPluginServer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// DependenciesReaderPluginServer is an autogenerated mock type for the DependenciesReaderPluginServer type
type DependenciesReaderPluginServer struct {
	mock.Mock
}

type DependenciesReaderPluginServer_Expecter struct {
	mock *mock.Mock
}

func (_m *DependenciesReaderPluginServer) EXPECT() *DependenciesReaderPluginServer_Expecter {
	return &DependenciesReaderPluginServer_Expecter{mock: &_m.Mock}
}

// GetDependencies provides a mock function for the type DependenciesReaderPluginServer
func (_mock *DependenciesReaderPluginServer) GetDependencies(context1 context.Context, getDependenciesRequest *storage_v1.GetDependenciesRequest) (*storage_v1.GetDependenciesResponse, error) {
	ret := _mock.Called(context1, getDependenciesRequest)

	if len(ret) == 0 {
		panic("no return value specified for GetDependencies")
	}

	var r0 *storage_v1.GetDependenciesResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetDependenciesRequest) (*storage_v1.GetDependenciesResponse, error)); ok {
		return returnFunc(context1, getDependenciesRequest)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.GetDependenciesRequest) *storage_v1.GetDependenciesResponse); ok {
		r0 = returnFunc(context1, getDependenciesRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.GetDependenciesResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.GetDependenciesRequest) error); ok {
		r1 = returnFunc(context1, getDependenciesRequest)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// DependenciesReaderPluginServer_GetDependencies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDependencies'
type DependenciesReaderPluginServer_GetDependencies_Call struct {
	*mock.Call
}

// GetDependencies is a helper method to define mock.On call
//   - context1 context.Context
//   - getDependenciesRequest *storage_v1.GetDependenciesRequest
func (_e *DependenciesReaderPluginServer_Expecter) GetDependencies(context1 interface{}, getDependenciesRequest interface{}) *DependenciesReaderPluginServer_GetDependencies_Call {
	return &DependenciesReaderPluginServer_GetDependencies_Call{Call: _e.mock.On("GetDependencies", context1, getDependenciesRequest)}
}

func (_c *DependenciesReaderPluginServer_GetDependencies_Call) Run(run func(context1 context.Context, getDependenciesRequest *storage_v1.GetDependenciesRequest)) *DependenciesReaderPluginServer_GetDependencies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.GetDependenciesRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.GetDependenciesRequest)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *DependenciesReaderPluginServer_GetDependencies_Call) Return(getDependenciesResponse *storage_v1.GetDependenciesResponse, err error) *DependenciesReaderPluginServer_GetDependencies_Call {
	_c.Call.Return(getDependenciesResponse, err)
	return _c
}

func (_c *DependenciesReaderPluginServer_GetDependencies_Call) RunAndReturn(run func(context1 context.Context, getDependenciesRequest *storage_v1.GetDependenciesRequest) (*storage_v1.GetDependenciesResponse, error)) *DependenciesReaderPluginServer_GetDependencies_Call {
	_c.Call.Return(run)
	return _c
}

// NewPluginCapabilitiesClient creates a new instance of PluginCapabilitiesClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPluginCapabilitiesClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *PluginCapabilitiesClient {
	mock := &PluginCapabilitiesClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// PluginCapabilitiesClient is an autogenerated mock type for the PluginCapabilitiesClient type
type PluginCapabilitiesClient struct {
	mock.Mock
}

type PluginCapabilitiesClient_Expecter struct {
	mock *mock.Mock
}

func (_m *PluginCapabilitiesClient) EXPECT() *PluginCapabilitiesClient_Expecter {
	return &PluginCapabilitiesClient_Expecter{mock: &_m.Mock}
}

// Capabilities provides a mock function for the type PluginCapabilitiesClient
func (_mock *PluginCapabilitiesClient) Capabilities(ctx context.Context, in *storage_v1.CapabilitiesRequest, opts ...grpc.CallOption) (*storage_v1.CapabilitiesResponse, error) {
	var tmpRet mock.Arguments
	if len(opts) > 0 {
		tmpRet = _mock.Called(ctx, in, opts)
	} else {
		tmpRet = _mock.Called(ctx, in)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Capabilities")
	}

	var r0 *storage_v1.CapabilitiesResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.CapabilitiesRequest, ...grpc.CallOption) (*storage_v1.CapabilitiesResponse, error)); ok {
		return returnFunc(ctx, in, opts...)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.CapabilitiesRequest, ...grpc.CallOption) *storage_v1.CapabilitiesResponse); ok {
		r0 = returnFunc(ctx, in, opts...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.CapabilitiesResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.CapabilitiesRequest, ...grpc.CallOption) error); ok {
		r1 = returnFunc(ctx, in, opts...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// PluginCapabilitiesClient_Capabilities_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Capabilities'
type PluginCapabilitiesClient_Capabilities_Call struct {
	*mock.Call
}

// Capabilities is a helper method to define mock.On call
//   - ctx context.Context
//   - in *storage_v1.CapabilitiesRequest
//   - opts ...grpc.CallOption
func (_e *PluginCapabilitiesClient_Expecter) Capabilities(ctx interface{}, in interface{}, opts ...interface{}) *PluginCapabilitiesClient_Capabilities_Call {
	return &PluginCapabilitiesClient_Capabilities_Call{Call: _e.mock.On("Capabilities",
		append([]interface{}{ctx, in}, opts...)...)}
}

func (_c *PluginCapabilitiesClient_Capabilities_Call) Run(run func(ctx context.Context, in *storage_v1.CapabilitiesRequest, opts ...grpc.CallOption)) *PluginCapabilitiesClient_Capabilities_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.CapabilitiesRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.CapabilitiesRequest)
		}
		var arg2 []grpc.CallOption
		var variadicArgs []grpc.CallOption
		if len(args) > 2 {
			variadicArgs = args[2].([]grpc.CallOption)
		}
		arg2 = variadicArgs
		run(
			arg0,
			arg1,
			arg2...,
		)
	})
	return _c
}

func (_c *PluginCapabilitiesClient_Capabilities_Call) Return(capabilitiesResponse *storage_v1.CapabilitiesResponse, err error) *PluginCapabilitiesClient_Capabilities_Call {
	_c.Call.Return(capabilitiesResponse, err)
	return _c
}

func (_c *PluginCapabilitiesClient_Capabilities_Call) RunAndReturn(run func(ctx context.Context, in *storage_v1.CapabilitiesRequest, opts ...grpc.CallOption) (*storage_v1.CapabilitiesResponse, error)) *PluginCapabilitiesClient_Capabilities_Call {
	_c.Call.Return(run)
	return _c
}

// NewPluginCapabilitiesServer creates a new instance of PluginCapabilitiesServer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPluginCapabilitiesServer(t interface {
	mock.TestingT
	Cleanup(func())
}) *PluginCapabilitiesServer {
	mock := &PluginCapabilitiesServer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// PluginCapabilitiesServer is an autogenerated mock type for the PluginCapabilitiesServer type
type PluginCapabilitiesServer struct {
	mock.Mock
}

type PluginCapabilitiesServer_Expecter struct {
	mock *mock.Mock
}

func (_m *PluginCapabilitiesServer) EXPECT() *PluginCapabilitiesServer_Expecter {
	return &PluginCapabilitiesServer_Expecter{mock: &_m.Mock}
}

// Capabilities provides a mock function for the type PluginCapabilitiesServer
func (_mock *PluginCapabilitiesServer) Capabilities(context1 context.Context, capabilitiesRequest *storage_v1.CapabilitiesRequest) (*storage_v1.CapabilitiesResponse, error) {
	ret := _mock.Called(context1, capabilitiesRequest)

	if len(ret) == 0 {
		panic("no return value specified for Capabilities")
	}

	var r0 *storage_v1.CapabilitiesResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.CapabilitiesRequest) (*storage_v1.CapabilitiesResponse, error)); ok {
		return returnFunc(context1, capabilitiesRequest)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *storage_v1.CapabilitiesRequest) *storage_v1.CapabilitiesResponse); ok {
		r0 = returnFunc(context1, capabilitiesRequest)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*storage_v1.CapabilitiesResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *storage_v1.CapabilitiesRequest) error); ok {
		r1 = returnFunc(context1, capabilitiesRequest)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// PluginCapabilitiesServer_Capabilities_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Capabilities'
type PluginCapabilitiesServer_Capabilities_Call struct {
	*mock.Call
}

// Capabilities is a helper method to define mock.On call
//   - context1 context.Context
//   - capabilitiesRequest *storage_v1.CapabilitiesRequest
func (_e *PluginCapabilitiesServer_Expecter) Capabilities(context1 interface{}, capabilitiesRequest interface{}) *PluginCapabilitiesServer_Capabilities_Call {
	return &PluginCapabilitiesServer_Capabilities_Call{Call: _e.mock.On("Capabilities", context1, capabilitiesRequest)}
}

func (_c *PluginCapabilitiesServer_Capabilities_Call) Run(run func(context1 context.Context, capabilitiesRequest *storage_v1.CapabilitiesRequest)) *PluginCapabilitiesServer_Capabilities_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *storage_v1.CapabilitiesRequest
		if args[1] != nil {
			arg1 = args[1].(*storage_v1.CapabilitiesRequest)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *PluginCapabilitiesServer_Capabilities_Call) Return(capabilitiesResponse *storage_v1.CapabilitiesResponse, err error) *PluginCapabilitiesServer_Capabilities_Call {
	_c.Call.Return(capabilitiesResponse, err)
	return _c
}

func (_c *PluginCapabilitiesServer_Capabilities_Call) RunAndReturn(run func(context1 context.Context, capabilitiesRequest *storage_v1.CapabilitiesRequest) (*storage_v1.CapabilitiesResponse, error)) *PluginCapabilitiesServer_Capabilities_Call {
	_c.Call.Return(run)
	return _c
}
