{"spans": [{"traceId": "AAAAAAAAAAAAAAAAAAAAEg==", "spanId": "AAAAAAAAAAQ=", "operationName": "query12-operation", "references": [{"refType": "CHILD_OF", "traceId": "AAAAAAAAAAAAAAAAAAAA/w==", "spanId": "AAAAAAAAAP8="}, {"refType": "CHILD_OF", "traceId": "AAAAAAAAAAAAAAAAAAAAAQ==", "spanId": "AAAAAAAAAAI="}, {"refType": "FOLLOWS_FROM", "traceId": "AAAAAAAAAAAAAAAAAAAAAQ==", "spanId": "AAAAAAAAAAI="}], "tags": [{"key": "sameplacetag1", "vType": "STRING", "vStr": "sameplacevalue"}, {"key": "sameplacetag2", "vType": "INT64", "vInt64": 123}, {"key": "sameplacetag4", "vType": "BOOL", "vBool": true}, {"key": "sameplacetag3", "vType": "FLOAT64", "vFloat64": 72.5}, {"key": "blob", "vType": "BINARY", "vBinary": "AAAwOQ=="}], "startTime": "2017-01-26T16:46:31.639875Z", "duration": "2000ns", "process": {"serviceName": "query12-service", "tags": []}, "logs": []}]}