// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify
// Copyright (c) The Jaeger Authors.
// SPDX-License-Identifier: Apache-2.0
//
// Run 'make generate-mocks' to regenerate.

package mocks

import (
	"context"
	"io"

	"github.com/jaegertracing/jaeger/internal/storage/elasticsearch"
	"github.com/olivere/elastic/v7"
	mock "github.com/stretchr/testify/mock"
)

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// Client is an autogenerated mock type for the Client type
type Client struct {
	mock.Mock
}

type Client_Expecter struct {
	mock *mock.Mock
}

func (_m *Client) EXPECT() *Client_Expecter {
	return &Client_Expecter{mock: &_m.Mock}
}

// Close provides a mock function for the type Client
func (_mock *Client) Close() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// Client_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type Client_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *Client_Expecter) Close() *Client_Close_Call {
	return &Client_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *Client_Close_Call) Run(run func()) *Client_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Client_Close_Call) Return(err error) *Client_Close_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *Client_Close_Call) RunAndReturn(run func() error) *Client_Close_Call {
	_c.Call.Return(run)
	return _c
}

// CreateIndex provides a mock function for the type Client
func (_mock *Client) CreateIndex(index string) elasticsearch.IndicesCreateService {
	ret := _mock.Called(index)

	if len(ret) == 0 {
		panic("no return value specified for CreateIndex")
	}

	var r0 elasticsearch.IndicesCreateService
	if returnFunc, ok := ret.Get(0).(func(string) elasticsearch.IndicesCreateService); ok {
		r0 = returnFunc(index)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.IndicesCreateService)
		}
	}
	return r0
}

// Client_CreateIndex_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateIndex'
type Client_CreateIndex_Call struct {
	*mock.Call
}

// CreateIndex is a helper method to define mock.On call
//   - index string
func (_e *Client_Expecter) CreateIndex(index interface{}) *Client_CreateIndex_Call {
	return &Client_CreateIndex_Call{Call: _e.mock.On("CreateIndex", index)}
}

func (_c *Client_CreateIndex_Call) Run(run func(index string)) *Client_CreateIndex_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *Client_CreateIndex_Call) Return(indicesCreateService elasticsearch.IndicesCreateService) *Client_CreateIndex_Call {
	_c.Call.Return(indicesCreateService)
	return _c
}

func (_c *Client_CreateIndex_Call) RunAndReturn(run func(index string) elasticsearch.IndicesCreateService) *Client_CreateIndex_Call {
	_c.Call.Return(run)
	return _c
}

// CreateTemplate provides a mock function for the type Client
func (_mock *Client) CreateTemplate(id string) elasticsearch.TemplateCreateService {
	ret := _mock.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for CreateTemplate")
	}

	var r0 elasticsearch.TemplateCreateService
	if returnFunc, ok := ret.Get(0).(func(string) elasticsearch.TemplateCreateService); ok {
		r0 = returnFunc(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.TemplateCreateService)
		}
	}
	return r0
}

// Client_CreateTemplate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateTemplate'
type Client_CreateTemplate_Call struct {
	*mock.Call
}

// CreateTemplate is a helper method to define mock.On call
//   - id string
func (_e *Client_Expecter) CreateTemplate(id interface{}) *Client_CreateTemplate_Call {
	return &Client_CreateTemplate_Call{Call: _e.mock.On("CreateTemplate", id)}
}

func (_c *Client_CreateTemplate_Call) Run(run func(id string)) *Client_CreateTemplate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *Client_CreateTemplate_Call) Return(templateCreateService elasticsearch.TemplateCreateService) *Client_CreateTemplate_Call {
	_c.Call.Return(templateCreateService)
	return _c
}

func (_c *Client_CreateTemplate_Call) RunAndReturn(run func(id string) elasticsearch.TemplateCreateService) *Client_CreateTemplate_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteIndex provides a mock function for the type Client
func (_mock *Client) DeleteIndex(index string) elasticsearch.IndicesDeleteService {
	ret := _mock.Called(index)

	if len(ret) == 0 {
		panic("no return value specified for DeleteIndex")
	}

	var r0 elasticsearch.IndicesDeleteService
	if returnFunc, ok := ret.Get(0).(func(string) elasticsearch.IndicesDeleteService); ok {
		r0 = returnFunc(index)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.IndicesDeleteService)
		}
	}
	return r0
}

// Client_DeleteIndex_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteIndex'
type Client_DeleteIndex_Call struct {
	*mock.Call
}

// DeleteIndex is a helper method to define mock.On call
//   - index string
func (_e *Client_Expecter) DeleteIndex(index interface{}) *Client_DeleteIndex_Call {
	return &Client_DeleteIndex_Call{Call: _e.mock.On("DeleteIndex", index)}
}

func (_c *Client_DeleteIndex_Call) Run(run func(index string)) *Client_DeleteIndex_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *Client_DeleteIndex_Call) Return(indicesDeleteService elasticsearch.IndicesDeleteService) *Client_DeleteIndex_Call {
	_c.Call.Return(indicesDeleteService)
	return _c
}

func (_c *Client_DeleteIndex_Call) RunAndReturn(run func(index string) elasticsearch.IndicesDeleteService) *Client_DeleteIndex_Call {
	_c.Call.Return(run)
	return _c
}

// GetVersion provides a mock function for the type Client
func (_mock *Client) GetVersion() uint {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetVersion")
	}

	var r0 uint
	if returnFunc, ok := ret.Get(0).(func() uint); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Get(0).(uint)
	}
	return r0
}

// Client_GetVersion_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetVersion'
type Client_GetVersion_Call struct {
	*mock.Call
}

// GetVersion is a helper method to define mock.On call
func (_e *Client_Expecter) GetVersion() *Client_GetVersion_Call {
	return &Client_GetVersion_Call{Call: _e.mock.On("GetVersion")}
}

func (_c *Client_GetVersion_Call) Run(run func()) *Client_GetVersion_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Client_GetVersion_Call) Return(v uint) *Client_GetVersion_Call {
	_c.Call.Return(v)
	return _c
}

func (_c *Client_GetVersion_Call) RunAndReturn(run func() uint) *Client_GetVersion_Call {
	_c.Call.Return(run)
	return _c
}

// Index provides a mock function for the type Client
func (_mock *Client) Index() elasticsearch.IndexService {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Index")
	}

	var r0 elasticsearch.IndexService
	if returnFunc, ok := ret.Get(0).(func() elasticsearch.IndexService); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.IndexService)
		}
	}
	return r0
}

// Client_Index_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Index'
type Client_Index_Call struct {
	*mock.Call
}

// Index is a helper method to define mock.On call
func (_e *Client_Expecter) Index() *Client_Index_Call {
	return &Client_Index_Call{Call: _e.mock.On("Index")}
}

func (_c *Client_Index_Call) Run(run func()) *Client_Index_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Client_Index_Call) Return(indexService elasticsearch.IndexService) *Client_Index_Call {
	_c.Call.Return(indexService)
	return _c
}

func (_c *Client_Index_Call) RunAndReturn(run func() elasticsearch.IndexService) *Client_Index_Call {
	_c.Call.Return(run)
	return _c
}

// IndexExists provides a mock function for the type Client
func (_mock *Client) IndexExists(index string) elasticsearch.IndicesExistsService {
	ret := _mock.Called(index)

	if len(ret) == 0 {
		panic("no return value specified for IndexExists")
	}

	var r0 elasticsearch.IndicesExistsService
	if returnFunc, ok := ret.Get(0).(func(string) elasticsearch.IndicesExistsService); ok {
		r0 = returnFunc(index)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.IndicesExistsService)
		}
	}
	return r0
}

// Client_IndexExists_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IndexExists'
type Client_IndexExists_Call struct {
	*mock.Call
}

// IndexExists is a helper method to define mock.On call
//   - index string
func (_e *Client_Expecter) IndexExists(index interface{}) *Client_IndexExists_Call {
	return &Client_IndexExists_Call{Call: _e.mock.On("IndexExists", index)}
}

func (_c *Client_IndexExists_Call) Run(run func(index string)) *Client_IndexExists_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *Client_IndexExists_Call) Return(indicesExistsService elasticsearch.IndicesExistsService) *Client_IndexExists_Call {
	_c.Call.Return(indicesExistsService)
	return _c
}

func (_c *Client_IndexExists_Call) RunAndReturn(run func(index string) elasticsearch.IndicesExistsService) *Client_IndexExists_Call {
	_c.Call.Return(run)
	return _c
}

// MultiSearch provides a mock function for the type Client
func (_mock *Client) MultiSearch() elasticsearch.MultiSearchService {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for MultiSearch")
	}

	var r0 elasticsearch.MultiSearchService
	if returnFunc, ok := ret.Get(0).(func() elasticsearch.MultiSearchService); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.MultiSearchService)
		}
	}
	return r0
}

// Client_MultiSearch_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'MultiSearch'
type Client_MultiSearch_Call struct {
	*mock.Call
}

// MultiSearch is a helper method to define mock.On call
func (_e *Client_Expecter) MultiSearch() *Client_MultiSearch_Call {
	return &Client_MultiSearch_Call{Call: _e.mock.On("MultiSearch")}
}

func (_c *Client_MultiSearch_Call) Run(run func()) *Client_MultiSearch_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Client_MultiSearch_Call) Return(multiSearchService elasticsearch.MultiSearchService) *Client_MultiSearch_Call {
	_c.Call.Return(multiSearchService)
	return _c
}

func (_c *Client_MultiSearch_Call) RunAndReturn(run func() elasticsearch.MultiSearchService) *Client_MultiSearch_Call {
	_c.Call.Return(run)
	return _c
}

// Search provides a mock function for the type Client
func (_mock *Client) Search(indices ...string) elasticsearch.SearchService {
	var tmpRet mock.Arguments
	if len(indices) > 0 {
		tmpRet = _mock.Called(indices)
	} else {
		tmpRet = _mock.Called()
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Search")
	}

	var r0 elasticsearch.SearchService
	if returnFunc, ok := ret.Get(0).(func(...string) elasticsearch.SearchService); ok {
		r0 = returnFunc(indices...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.SearchService)
		}
	}
	return r0
}

// Client_Search_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Search'
type Client_Search_Call struct {
	*mock.Call
}

// Search is a helper method to define mock.On call
//   - indices ...string
func (_e *Client_Expecter) Search(indices ...interface{}) *Client_Search_Call {
	return &Client_Search_Call{Call: _e.mock.On("Search",
		append([]interface{}{}, indices...)...)}
}

func (_c *Client_Search_Call) Run(run func(indices ...string)) *Client_Search_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 []string
		var variadicArgs []string
		if len(args) > 0 {
			variadicArgs = args[0].([]string)
		}
		arg0 = variadicArgs
		run(
			arg0...,
		)
	})
	return _c
}

func (_c *Client_Search_Call) Return(searchService elasticsearch.SearchService) *Client_Search_Call {
	_c.Call.Return(searchService)
	return _c
}

func (_c *Client_Search_Call) RunAndReturn(run func(indices ...string) elasticsearch.SearchService) *Client_Search_Call {
	_c.Call.Return(run)
	return _c
}

// NewIndicesExistsService creates a new instance of IndicesExistsService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewIndicesExistsService(t interface {
	mock.TestingT
	Cleanup(func())
}) *IndicesExistsService {
	mock := &IndicesExistsService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// IndicesExistsService is an autogenerated mock type for the IndicesExistsService type
type IndicesExistsService struct {
	mock.Mock
}

type IndicesExistsService_Expecter struct {
	mock *mock.Mock
}

func (_m *IndicesExistsService) EXPECT() *IndicesExistsService_Expecter {
	return &IndicesExistsService_Expecter{mock: &_m.Mock}
}

// Do provides a mock function for the type IndicesExistsService
func (_mock *IndicesExistsService) Do(ctx context.Context) (bool, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (bool, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) bool); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// IndicesExistsService_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type IndicesExistsService_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - ctx context.Context
func (_e *IndicesExistsService_Expecter) Do(ctx interface{}) *IndicesExistsService_Do_Call {
	return &IndicesExistsService_Do_Call{Call: _e.mock.On("Do", ctx)}
}

func (_c *IndicesExistsService_Do_Call) Run(run func(ctx context.Context)) *IndicesExistsService_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndicesExistsService_Do_Call) Return(b bool, err error) *IndicesExistsService_Do_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *IndicesExistsService_Do_Call) RunAndReturn(run func(ctx context.Context) (bool, error)) *IndicesExistsService_Do_Call {
	_c.Call.Return(run)
	return _c
}

// NewIndicesCreateService creates a new instance of IndicesCreateService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewIndicesCreateService(t interface {
	mock.TestingT
	Cleanup(func())
}) *IndicesCreateService {
	mock := &IndicesCreateService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// IndicesCreateService is an autogenerated mock type for the IndicesCreateService type
type IndicesCreateService struct {
	mock.Mock
}

type IndicesCreateService_Expecter struct {
	mock *mock.Mock
}

func (_m *IndicesCreateService) EXPECT() *IndicesCreateService_Expecter {
	return &IndicesCreateService_Expecter{mock: &_m.Mock}
}

// Body provides a mock function for the type IndicesCreateService
func (_mock *IndicesCreateService) Body(mapping string) elasticsearch.IndicesCreateService {
	ret := _mock.Called(mapping)

	if len(ret) == 0 {
		panic("no return value specified for Body")
	}

	var r0 elasticsearch.IndicesCreateService
	if returnFunc, ok := ret.Get(0).(func(string) elasticsearch.IndicesCreateService); ok {
		r0 = returnFunc(mapping)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.IndicesCreateService)
		}
	}
	return r0
}

// IndicesCreateService_Body_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Body'
type IndicesCreateService_Body_Call struct {
	*mock.Call
}

// Body is a helper method to define mock.On call
//   - mapping string
func (_e *IndicesCreateService_Expecter) Body(mapping interface{}) *IndicesCreateService_Body_Call {
	return &IndicesCreateService_Body_Call{Call: _e.mock.On("Body", mapping)}
}

func (_c *IndicesCreateService_Body_Call) Run(run func(mapping string)) *IndicesCreateService_Body_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndicesCreateService_Body_Call) Return(indicesCreateService elasticsearch.IndicesCreateService) *IndicesCreateService_Body_Call {
	_c.Call.Return(indicesCreateService)
	return _c
}

func (_c *IndicesCreateService_Body_Call) RunAndReturn(run func(mapping string) elasticsearch.IndicesCreateService) *IndicesCreateService_Body_Call {
	_c.Call.Return(run)
	return _c
}

// Do provides a mock function for the type IndicesCreateService
func (_mock *IndicesCreateService) Do(ctx context.Context) (*elastic.IndicesCreateResult, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 *elastic.IndicesCreateResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*elastic.IndicesCreateResult, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *elastic.IndicesCreateResult); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*elastic.IndicesCreateResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// IndicesCreateService_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type IndicesCreateService_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - ctx context.Context
func (_e *IndicesCreateService_Expecter) Do(ctx interface{}) *IndicesCreateService_Do_Call {
	return &IndicesCreateService_Do_Call{Call: _e.mock.On("Do", ctx)}
}

func (_c *IndicesCreateService_Do_Call) Run(run func(ctx context.Context)) *IndicesCreateService_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndicesCreateService_Do_Call) Return(indicesCreateResult *elastic.IndicesCreateResult, err error) *IndicesCreateService_Do_Call {
	_c.Call.Return(indicesCreateResult, err)
	return _c
}

func (_c *IndicesCreateService_Do_Call) RunAndReturn(run func(ctx context.Context) (*elastic.IndicesCreateResult, error)) *IndicesCreateService_Do_Call {
	_c.Call.Return(run)
	return _c
}

// NewIndicesDeleteService creates a new instance of IndicesDeleteService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewIndicesDeleteService(t interface {
	mock.TestingT
	Cleanup(func())
}) *IndicesDeleteService {
	mock := &IndicesDeleteService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// IndicesDeleteService is an autogenerated mock type for the IndicesDeleteService type
type IndicesDeleteService struct {
	mock.Mock
}

type IndicesDeleteService_Expecter struct {
	mock *mock.Mock
}

func (_m *IndicesDeleteService) EXPECT() *IndicesDeleteService_Expecter {
	return &IndicesDeleteService_Expecter{mock: &_m.Mock}
}

// Do provides a mock function for the type IndicesDeleteService
func (_mock *IndicesDeleteService) Do(ctx context.Context) (*elastic.IndicesDeleteResponse, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 *elastic.IndicesDeleteResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*elastic.IndicesDeleteResponse, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *elastic.IndicesDeleteResponse); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*elastic.IndicesDeleteResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// IndicesDeleteService_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type IndicesDeleteService_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - ctx context.Context
func (_e *IndicesDeleteService_Expecter) Do(ctx interface{}) *IndicesDeleteService_Do_Call {
	return &IndicesDeleteService_Do_Call{Call: _e.mock.On("Do", ctx)}
}

func (_c *IndicesDeleteService_Do_Call) Run(run func(ctx context.Context)) *IndicesDeleteService_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndicesDeleteService_Do_Call) Return(indicesDeleteResponse *elastic.IndicesDeleteResponse, err error) *IndicesDeleteService_Do_Call {
	_c.Call.Return(indicesDeleteResponse, err)
	return _c
}

func (_c *IndicesDeleteService_Do_Call) RunAndReturn(run func(ctx context.Context) (*elastic.IndicesDeleteResponse, error)) *IndicesDeleteService_Do_Call {
	_c.Call.Return(run)
	return _c
}

// NewTemplateCreateService creates a new instance of TemplateCreateService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTemplateCreateService(t interface {
	mock.TestingT
	Cleanup(func())
}) *TemplateCreateService {
	mock := &TemplateCreateService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// TemplateCreateService is an autogenerated mock type for the TemplateCreateService type
type TemplateCreateService struct {
	mock.Mock
}

type TemplateCreateService_Expecter struct {
	mock *mock.Mock
}

func (_m *TemplateCreateService) EXPECT() *TemplateCreateService_Expecter {
	return &TemplateCreateService_Expecter{mock: &_m.Mock}
}

// Body provides a mock function for the type TemplateCreateService
func (_mock *TemplateCreateService) Body(mapping string) elasticsearch.TemplateCreateService {
	ret := _mock.Called(mapping)

	if len(ret) == 0 {
		panic("no return value specified for Body")
	}

	var r0 elasticsearch.TemplateCreateService
	if returnFunc, ok := ret.Get(0).(func(string) elasticsearch.TemplateCreateService); ok {
		r0 = returnFunc(mapping)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.TemplateCreateService)
		}
	}
	return r0
}

// TemplateCreateService_Body_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Body'
type TemplateCreateService_Body_Call struct {
	*mock.Call
}

// Body is a helper method to define mock.On call
//   - mapping string
func (_e *TemplateCreateService_Expecter) Body(mapping interface{}) *TemplateCreateService_Body_Call {
	return &TemplateCreateService_Body_Call{Call: _e.mock.On("Body", mapping)}
}

func (_c *TemplateCreateService_Body_Call) Run(run func(mapping string)) *TemplateCreateService_Body_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *TemplateCreateService_Body_Call) Return(templateCreateService elasticsearch.TemplateCreateService) *TemplateCreateService_Body_Call {
	_c.Call.Return(templateCreateService)
	return _c
}

func (_c *TemplateCreateService_Body_Call) RunAndReturn(run func(mapping string) elasticsearch.TemplateCreateService) *TemplateCreateService_Body_Call {
	_c.Call.Return(run)
	return _c
}

// Do provides a mock function for the type TemplateCreateService
func (_mock *TemplateCreateService) Do(ctx context.Context) (*elastic.IndicesPutTemplateResponse, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 *elastic.IndicesPutTemplateResponse
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*elastic.IndicesPutTemplateResponse, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *elastic.IndicesPutTemplateResponse); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*elastic.IndicesPutTemplateResponse)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TemplateCreateService_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type TemplateCreateService_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - ctx context.Context
func (_e *TemplateCreateService_Expecter) Do(ctx interface{}) *TemplateCreateService_Do_Call {
	return &TemplateCreateService_Do_Call{Call: _e.mock.On("Do", ctx)}
}

func (_c *TemplateCreateService_Do_Call) Run(run func(ctx context.Context)) *TemplateCreateService_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *TemplateCreateService_Do_Call) Return(indicesPutTemplateResponse *elastic.IndicesPutTemplateResponse, err error) *TemplateCreateService_Do_Call {
	_c.Call.Return(indicesPutTemplateResponse, err)
	return _c
}

func (_c *TemplateCreateService_Do_Call) RunAndReturn(run func(ctx context.Context) (*elastic.IndicesPutTemplateResponse, error)) *TemplateCreateService_Do_Call {
	_c.Call.Return(run)
	return _c
}

// NewIndexService creates a new instance of IndexService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewIndexService(t interface {
	mock.TestingT
	Cleanup(func())
}) *IndexService {
	mock := &IndexService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// IndexService is an autogenerated mock type for the IndexService type
type IndexService struct {
	mock.Mock
}

type IndexService_Expecter struct {
	mock *mock.Mock
}

func (_m *IndexService) EXPECT() *IndexService_Expecter {
	return &IndexService_Expecter{mock: &_m.Mock}
}

// Add provides a mock function for the type IndexService
func (_mock *IndexService) Add() {
	_mock.Called()
	return
}

// IndexService_Add_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Add'
type IndexService_Add_Call struct {
	*mock.Call
}

// Add is a helper method to define mock.On call
func (_e *IndexService_Expecter) Add() *IndexService_Add_Call {
	return &IndexService_Add_Call{Call: _e.mock.On("Add")}
}

func (_c *IndexService_Add_Call) Run(run func()) *IndexService_Add_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *IndexService_Add_Call) Return() *IndexService_Add_Call {
	_c.Call.Return()
	return _c
}

func (_c *IndexService_Add_Call) RunAndReturn(run func()) *IndexService_Add_Call {
	_c.Run(run)
	return _c
}

// BodyJson provides a mock function for the type IndexService
func (_mock *IndexService) BodyJson(body any) elasticsearch.IndexService {
	ret := _mock.Called(body)

	if len(ret) == 0 {
		panic("no return value specified for BodyJson")
	}

	var r0 elasticsearch.IndexService
	if returnFunc, ok := ret.Get(0).(func(any) elasticsearch.IndexService); ok {
		r0 = returnFunc(body)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.IndexService)
		}
	}
	return r0
}

// IndexService_BodyJson_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'BodyJson'
type IndexService_BodyJson_Call struct {
	*mock.Call
}

// BodyJson is a helper method to define mock.On call
//   - body any
func (_e *IndexService_Expecter) BodyJson(body interface{}) *IndexService_BodyJson_Call {
	return &IndexService_BodyJson_Call{Call: _e.mock.On("BodyJson", body)}
}

func (_c *IndexService_BodyJson_Call) Run(run func(body any)) *IndexService_BodyJson_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 any
		if args[0] != nil {
			arg0 = args[0].(any)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndexService_BodyJson_Call) Return(indexService elasticsearch.IndexService) *IndexService_BodyJson_Call {
	_c.Call.Return(indexService)
	return _c
}

func (_c *IndexService_BodyJson_Call) RunAndReturn(run func(body any) elasticsearch.IndexService) *IndexService_BodyJson_Call {
	_c.Call.Return(run)
	return _c
}

// Id provides a mock function for the type IndexService
func (_mock *IndexService) Id(id string) elasticsearch.IndexService {
	ret := _mock.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for Id")
	}

	var r0 elasticsearch.IndexService
	if returnFunc, ok := ret.Get(0).(func(string) elasticsearch.IndexService); ok {
		r0 = returnFunc(id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.IndexService)
		}
	}
	return r0
}

// IndexService_Id_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Id'
type IndexService_Id_Call struct {
	*mock.Call
}

// Id is a helper method to define mock.On call
//   - id string
func (_e *IndexService_Expecter) Id(id interface{}) *IndexService_Id_Call {
	return &IndexService_Id_Call{Call: _e.mock.On("Id", id)}
}

func (_c *IndexService_Id_Call) Run(run func(id string)) *IndexService_Id_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndexService_Id_Call) Return(indexService elasticsearch.IndexService) *IndexService_Id_Call {
	_c.Call.Return(indexService)
	return _c
}

func (_c *IndexService_Id_Call) RunAndReturn(run func(id string) elasticsearch.IndexService) *IndexService_Id_Call {
	_c.Call.Return(run)
	return _c
}

// Index provides a mock function for the type IndexService
func (_mock *IndexService) Index(index string) elasticsearch.IndexService {
	ret := _mock.Called(index)

	if len(ret) == 0 {
		panic("no return value specified for Index")
	}

	var r0 elasticsearch.IndexService
	if returnFunc, ok := ret.Get(0).(func(string) elasticsearch.IndexService); ok {
		r0 = returnFunc(index)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.IndexService)
		}
	}
	return r0
}

// IndexService_Index_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Index'
type IndexService_Index_Call struct {
	*mock.Call
}

// Index is a helper method to define mock.On call
//   - index string
func (_e *IndexService_Expecter) Index(index interface{}) *IndexService_Index_Call {
	return &IndexService_Index_Call{Call: _e.mock.On("Index", index)}
}

func (_c *IndexService_Index_Call) Run(run func(index string)) *IndexService_Index_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndexService_Index_Call) Return(indexService elasticsearch.IndexService) *IndexService_Index_Call {
	_c.Call.Return(indexService)
	return _c
}

func (_c *IndexService_Index_Call) RunAndReturn(run func(index string) elasticsearch.IndexService) *IndexService_Index_Call {
	_c.Call.Return(run)
	return _c
}

// Type provides a mock function for the type IndexService
func (_mock *IndexService) Type(typ string) elasticsearch.IndexService {
	ret := _mock.Called(typ)

	if len(ret) == 0 {
		panic("no return value specified for Type")
	}

	var r0 elasticsearch.IndexService
	if returnFunc, ok := ret.Get(0).(func(string) elasticsearch.IndexService); ok {
		r0 = returnFunc(typ)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.IndexService)
		}
	}
	return r0
}

// IndexService_Type_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Type'
type IndexService_Type_Call struct {
	*mock.Call
}

// Type is a helper method to define mock.On call
//   - typ string
func (_e *IndexService_Expecter) Type(typ interface{}) *IndexService_Type_Call {
	return &IndexService_Type_Call{Call: _e.mock.On("Type", typ)}
}

func (_c *IndexService_Type_Call) Run(run func(typ string)) *IndexService_Type_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndexService_Type_Call) Return(indexService elasticsearch.IndexService) *IndexService_Type_Call {
	_c.Call.Return(indexService)
	return _c
}

func (_c *IndexService_Type_Call) RunAndReturn(run func(typ string) elasticsearch.IndexService) *IndexService_Type_Call {
	_c.Call.Return(run)
	return _c
}

// NewSearchService creates a new instance of SearchService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSearchService(t interface {
	mock.TestingT
	Cleanup(func())
}) *SearchService {
	mock := &SearchService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// SearchService is an autogenerated mock type for the SearchService type
type SearchService struct {
	mock.Mock
}

type SearchService_Expecter struct {
	mock *mock.Mock
}

func (_m *SearchService) EXPECT() *SearchService_Expecter {
	return &SearchService_Expecter{mock: &_m.Mock}
}

// Aggregation provides a mock function for the type SearchService
func (_mock *SearchService) Aggregation(name string, aggregation elastic.Aggregation) elasticsearch.SearchService {
	ret := _mock.Called(name, aggregation)

	if len(ret) == 0 {
		panic("no return value specified for Aggregation")
	}

	var r0 elasticsearch.SearchService
	if returnFunc, ok := ret.Get(0).(func(string, elastic.Aggregation) elasticsearch.SearchService); ok {
		r0 = returnFunc(name, aggregation)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.SearchService)
		}
	}
	return r0
}

// SearchService_Aggregation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Aggregation'
type SearchService_Aggregation_Call struct {
	*mock.Call
}

// Aggregation is a helper method to define mock.On call
//   - name string
//   - aggregation elastic.Aggregation
func (_e *SearchService_Expecter) Aggregation(name interface{}, aggregation interface{}) *SearchService_Aggregation_Call {
	return &SearchService_Aggregation_Call{Call: _e.mock.On("Aggregation", name, aggregation)}
}

func (_c *SearchService_Aggregation_Call) Run(run func(name string, aggregation elastic.Aggregation)) *SearchService_Aggregation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 elastic.Aggregation
		if args[1] != nil {
			arg1 = args[1].(elastic.Aggregation)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *SearchService_Aggregation_Call) Return(searchService elasticsearch.SearchService) *SearchService_Aggregation_Call {
	_c.Call.Return(searchService)
	return _c
}

func (_c *SearchService_Aggregation_Call) RunAndReturn(run func(name string, aggregation elastic.Aggregation) elasticsearch.SearchService) *SearchService_Aggregation_Call {
	_c.Call.Return(run)
	return _c
}

// Do provides a mock function for the type SearchService
func (_mock *SearchService) Do(ctx context.Context) (*elastic.SearchResult, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 *elastic.SearchResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*elastic.SearchResult, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *elastic.SearchResult); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*elastic.SearchResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// SearchService_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type SearchService_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - ctx context.Context
func (_e *SearchService_Expecter) Do(ctx interface{}) *SearchService_Do_Call {
	return &SearchService_Do_Call{Call: _e.mock.On("Do", ctx)}
}

func (_c *SearchService_Do_Call) Run(run func(ctx context.Context)) *SearchService_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SearchService_Do_Call) Return(searchResult *elastic.SearchResult, err error) *SearchService_Do_Call {
	_c.Call.Return(searchResult, err)
	return _c
}

func (_c *SearchService_Do_Call) RunAndReturn(run func(ctx context.Context) (*elastic.SearchResult, error)) *SearchService_Do_Call {
	_c.Call.Return(run)
	return _c
}

// IgnoreUnavailable provides a mock function for the type SearchService
func (_mock *SearchService) IgnoreUnavailable(ignoreUnavailable bool) elasticsearch.SearchService {
	ret := _mock.Called(ignoreUnavailable)

	if len(ret) == 0 {
		panic("no return value specified for IgnoreUnavailable")
	}

	var r0 elasticsearch.SearchService
	if returnFunc, ok := ret.Get(0).(func(bool) elasticsearch.SearchService); ok {
		r0 = returnFunc(ignoreUnavailable)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.SearchService)
		}
	}
	return r0
}

// SearchService_IgnoreUnavailable_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IgnoreUnavailable'
type SearchService_IgnoreUnavailable_Call struct {
	*mock.Call
}

// IgnoreUnavailable is a helper method to define mock.On call
//   - ignoreUnavailable bool
func (_e *SearchService_Expecter) IgnoreUnavailable(ignoreUnavailable interface{}) *SearchService_IgnoreUnavailable_Call {
	return &SearchService_IgnoreUnavailable_Call{Call: _e.mock.On("IgnoreUnavailable", ignoreUnavailable)}
}

func (_c *SearchService_IgnoreUnavailable_Call) Run(run func(ignoreUnavailable bool)) *SearchService_IgnoreUnavailable_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 bool
		if args[0] != nil {
			arg0 = args[0].(bool)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SearchService_IgnoreUnavailable_Call) Return(searchService elasticsearch.SearchService) *SearchService_IgnoreUnavailable_Call {
	_c.Call.Return(searchService)
	return _c
}

func (_c *SearchService_IgnoreUnavailable_Call) RunAndReturn(run func(ignoreUnavailable bool) elasticsearch.SearchService) *SearchService_IgnoreUnavailable_Call {
	_c.Call.Return(run)
	return _c
}

// Query provides a mock function for the type SearchService
func (_mock *SearchService) Query(query elastic.Query) elasticsearch.SearchService {
	ret := _mock.Called(query)

	if len(ret) == 0 {
		panic("no return value specified for Query")
	}

	var r0 elasticsearch.SearchService
	if returnFunc, ok := ret.Get(0).(func(elastic.Query) elasticsearch.SearchService); ok {
		r0 = returnFunc(query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.SearchService)
		}
	}
	return r0
}

// SearchService_Query_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Query'
type SearchService_Query_Call struct {
	*mock.Call
}

// Query is a helper method to define mock.On call
//   - query elastic.Query
func (_e *SearchService_Expecter) Query(query interface{}) *SearchService_Query_Call {
	return &SearchService_Query_Call{Call: _e.mock.On("Query", query)}
}

func (_c *SearchService_Query_Call) Run(run func(query elastic.Query)) *SearchService_Query_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 elastic.Query
		if args[0] != nil {
			arg0 = args[0].(elastic.Query)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SearchService_Query_Call) Return(searchService elasticsearch.SearchService) *SearchService_Query_Call {
	_c.Call.Return(searchService)
	return _c
}

func (_c *SearchService_Query_Call) RunAndReturn(run func(query elastic.Query) elasticsearch.SearchService) *SearchService_Query_Call {
	_c.Call.Return(run)
	return _c
}

// Size provides a mock function for the type SearchService
func (_mock *SearchService) Size(size int) elasticsearch.SearchService {
	ret := _mock.Called(size)

	if len(ret) == 0 {
		panic("no return value specified for Size")
	}

	var r0 elasticsearch.SearchService
	if returnFunc, ok := ret.Get(0).(func(int) elasticsearch.SearchService); ok {
		r0 = returnFunc(size)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.SearchService)
		}
	}
	return r0
}

// SearchService_Size_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Size'
type SearchService_Size_Call struct {
	*mock.Call
}

// Size is a helper method to define mock.On call
//   - size int
func (_e *SearchService_Expecter) Size(size interface{}) *SearchService_Size_Call {
	return &SearchService_Size_Call{Call: _e.mock.On("Size", size)}
}

func (_c *SearchService_Size_Call) Run(run func(size int)) *SearchService_Size_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 int
		if args[0] != nil {
			arg0 = args[0].(int)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *SearchService_Size_Call) Return(searchService elasticsearch.SearchService) *SearchService_Size_Call {
	_c.Call.Return(searchService)
	return _c
}

func (_c *SearchService_Size_Call) RunAndReturn(run func(size int) elasticsearch.SearchService) *SearchService_Size_Call {
	_c.Call.Return(run)
	return _c
}

// NewMultiSearchService creates a new instance of MultiSearchService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMultiSearchService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MultiSearchService {
	mock := &MultiSearchService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MultiSearchService is an autogenerated mock type for the MultiSearchService type
type MultiSearchService struct {
	mock.Mock
}

type MultiSearchService_Expecter struct {
	mock *mock.Mock
}

func (_m *MultiSearchService) EXPECT() *MultiSearchService_Expecter {
	return &MultiSearchService_Expecter{mock: &_m.Mock}
}

// Add provides a mock function for the type MultiSearchService
func (_mock *MultiSearchService) Add(requests ...*elastic.SearchRequest) elasticsearch.MultiSearchService {
	var tmpRet mock.Arguments
	if len(requests) > 0 {
		tmpRet = _mock.Called(requests)
	} else {
		tmpRet = _mock.Called()
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Add")
	}

	var r0 elasticsearch.MultiSearchService
	if returnFunc, ok := ret.Get(0).(func(...*elastic.SearchRequest) elasticsearch.MultiSearchService); ok {
		r0 = returnFunc(requests...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.MultiSearchService)
		}
	}
	return r0
}

// MultiSearchService_Add_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Add'
type MultiSearchService_Add_Call struct {
	*mock.Call
}

// Add is a helper method to define mock.On call
//   - requests ...*elastic.SearchRequest
func (_e *MultiSearchService_Expecter) Add(requests ...interface{}) *MultiSearchService_Add_Call {
	return &MultiSearchService_Add_Call{Call: _e.mock.On("Add",
		append([]interface{}{}, requests...)...)}
}

func (_c *MultiSearchService_Add_Call) Run(run func(requests ...*elastic.SearchRequest)) *MultiSearchService_Add_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 []*elastic.SearchRequest
		var variadicArgs []*elastic.SearchRequest
		if len(args) > 0 {
			variadicArgs = args[0].([]*elastic.SearchRequest)
		}
		arg0 = variadicArgs
		run(
			arg0...,
		)
	})
	return _c
}

func (_c *MultiSearchService_Add_Call) Return(multiSearchService elasticsearch.MultiSearchService) *MultiSearchService_Add_Call {
	_c.Call.Return(multiSearchService)
	return _c
}

func (_c *MultiSearchService_Add_Call) RunAndReturn(run func(requests ...*elastic.SearchRequest) elasticsearch.MultiSearchService) *MultiSearchService_Add_Call {
	_c.Call.Return(run)
	return _c
}

// Do provides a mock function for the type MultiSearchService
func (_mock *MultiSearchService) Do(ctx context.Context) (*elastic.MultiSearchResult, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for Do")
	}

	var r0 *elastic.MultiSearchResult
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*elastic.MultiSearchResult, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *elastic.MultiSearchResult); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*elastic.MultiSearchResult)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MultiSearchService_Do_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Do'
type MultiSearchService_Do_Call struct {
	*mock.Call
}

// Do is a helper method to define mock.On call
//   - ctx context.Context
func (_e *MultiSearchService_Expecter) Do(ctx interface{}) *MultiSearchService_Do_Call {
	return &MultiSearchService_Do_Call{Call: _e.mock.On("Do", ctx)}
}

func (_c *MultiSearchService_Do_Call) Run(run func(ctx context.Context)) *MultiSearchService_Do_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MultiSearchService_Do_Call) Return(multiSearchResult *elastic.MultiSearchResult, err error) *MultiSearchService_Do_Call {
	_c.Call.Return(multiSearchResult, err)
	return _c
}

func (_c *MultiSearchService_Do_Call) RunAndReturn(run func(ctx context.Context) (*elastic.MultiSearchResult, error)) *MultiSearchService_Do_Call {
	_c.Call.Return(run)
	return _c
}

// Index provides a mock function for the type MultiSearchService
func (_mock *MultiSearchService) Index(indices ...string) elasticsearch.MultiSearchService {
	var tmpRet mock.Arguments
	if len(indices) > 0 {
		tmpRet = _mock.Called(indices)
	} else {
		tmpRet = _mock.Called()
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Index")
	}

	var r0 elasticsearch.MultiSearchService
	if returnFunc, ok := ret.Get(0).(func(...string) elasticsearch.MultiSearchService); ok {
		r0 = returnFunc(indices...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.MultiSearchService)
		}
	}
	return r0
}

// MultiSearchService_Index_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Index'
type MultiSearchService_Index_Call struct {
	*mock.Call
}

// Index is a helper method to define mock.On call
//   - indices ...string
func (_e *MultiSearchService_Expecter) Index(indices ...interface{}) *MultiSearchService_Index_Call {
	return &MultiSearchService_Index_Call{Call: _e.mock.On("Index",
		append([]interface{}{}, indices...)...)}
}

func (_c *MultiSearchService_Index_Call) Run(run func(indices ...string)) *MultiSearchService_Index_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 []string
		var variadicArgs []string
		if len(args) > 0 {
			variadicArgs = args[0].([]string)
		}
		arg0 = variadicArgs
		run(
			arg0...,
		)
	})
	return _c
}

func (_c *MultiSearchService_Index_Call) Return(multiSearchService elasticsearch.MultiSearchService) *MultiSearchService_Index_Call {
	_c.Call.Return(multiSearchService)
	return _c
}

func (_c *MultiSearchService_Index_Call) RunAndReturn(run func(indices ...string) elasticsearch.MultiSearchService) *MultiSearchService_Index_Call {
	_c.Call.Return(run)
	return _c
}

// NewTemplateApplier creates a new instance of TemplateApplier. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTemplateApplier(t interface {
	mock.TestingT
	Cleanup(func())
}) *TemplateApplier {
	mock := &TemplateApplier{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// TemplateApplier is an autogenerated mock type for the TemplateApplier type
type TemplateApplier struct {
	mock.Mock
}

type TemplateApplier_Expecter struct {
	mock *mock.Mock
}

func (_m *TemplateApplier) EXPECT() *TemplateApplier_Expecter {
	return &TemplateApplier_Expecter{mock: &_m.Mock}
}

// Execute provides a mock function for the type TemplateApplier
func (_mock *TemplateApplier) Execute(wr io.Writer, data any) error {
	ret := _mock.Called(wr, data)

	if len(ret) == 0 {
		panic("no return value specified for Execute")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(io.Writer, any) error); ok {
		r0 = returnFunc(wr, data)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// TemplateApplier_Execute_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Execute'
type TemplateApplier_Execute_Call struct {
	*mock.Call
}

// Execute is a helper method to define mock.On call
//   - wr io.Writer
//   - data any
func (_e *TemplateApplier_Expecter) Execute(wr interface{}, data interface{}) *TemplateApplier_Execute_Call {
	return &TemplateApplier_Execute_Call{Call: _e.mock.On("Execute", wr, data)}
}

func (_c *TemplateApplier_Execute_Call) Run(run func(wr io.Writer, data any)) *TemplateApplier_Execute_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 io.Writer
		if args[0] != nil {
			arg0 = args[0].(io.Writer)
		}
		var arg1 any
		if args[1] != nil {
			arg1 = args[1].(any)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *TemplateApplier_Execute_Call) Return(err error) *TemplateApplier_Execute_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *TemplateApplier_Execute_Call) RunAndReturn(run func(wr io.Writer, data any) error) *TemplateApplier_Execute_Call {
	_c.Call.Return(run)
	return _c
}

// NewTemplateBuilder creates a new instance of TemplateBuilder. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewTemplateBuilder(t interface {
	mock.TestingT
	Cleanup(func())
}) *TemplateBuilder {
	mock := &TemplateBuilder{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// TemplateBuilder is an autogenerated mock type for the TemplateBuilder type
type TemplateBuilder struct {
	mock.Mock
}

type TemplateBuilder_Expecter struct {
	mock *mock.Mock
}

func (_m *TemplateBuilder) EXPECT() *TemplateBuilder_Expecter {
	return &TemplateBuilder_Expecter{mock: &_m.Mock}
}

// Parse provides a mock function for the type TemplateBuilder
func (_mock *TemplateBuilder) Parse(text string) (elasticsearch.TemplateApplier, error) {
	ret := _mock.Called(text)

	if len(ret) == 0 {
		panic("no return value specified for Parse")
	}

	var r0 elasticsearch.TemplateApplier
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(string) (elasticsearch.TemplateApplier, error)); ok {
		return returnFunc(text)
	}
	if returnFunc, ok := ret.Get(0).(func(string) elasticsearch.TemplateApplier); ok {
		r0 = returnFunc(text)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(elasticsearch.TemplateApplier)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(string) error); ok {
		r1 = returnFunc(text)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// TemplateBuilder_Parse_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Parse'
type TemplateBuilder_Parse_Call struct {
	*mock.Call
}

// Parse is a helper method to define mock.On call
//   - text string
func (_e *TemplateBuilder_Expecter) Parse(text interface{}) *TemplateBuilder_Parse_Call {
	return &TemplateBuilder_Parse_Call{Call: _e.mock.On("Parse", text)}
}

func (_c *TemplateBuilder_Parse_Call) Run(run func(text string)) *TemplateBuilder_Parse_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *TemplateBuilder_Parse_Call) Return(templateApplier elasticsearch.TemplateApplier, err error) *TemplateBuilder_Parse_Call {
	_c.Call.Return(templateApplier, err)
	return _c
}

func (_c *TemplateBuilder_Parse_Call) RunAndReturn(run func(text string) (elasticsearch.TemplateApplier, error)) *TemplateBuilder_Parse_Call {
	_c.Call.Return(run)
	return _c
}
