// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify
// Copyright (c) The Jaeger Authors.
// SPDX-License-Identifier: Apache-2.0
//
// Run 'make generate-mocks' to regenerate.

package mocks

import (
	"github.com/jaegertracing/jaeger/internal/storage/elasticsearch/client"
	mock "github.com/stretchr/testify/mock"
)

// NewIndexAPI creates a new instance of IndexAPI. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewIndexAPI(t interface {
	mock.TestingT
	Cleanup(func())
}) *IndexAPI {
	mock := &IndexAPI{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// IndexAPI is an autogenerated mock type for the IndexAPI type
type IndexAPI struct {
	mock.Mock
}

type IndexAPI_Expecter struct {
	mock *mock.Mock
}

func (_m *IndexAPI) EXPECT() *IndexAPI_Expecter {
	return &IndexAPI_Expecter{mock: &_m.Mock}
}

// AliasExists provides a mock function for the type IndexAPI
func (_mock *IndexAPI) AliasExists(alias string) (bool, error) {
	ret := _mock.Called(alias)

	if len(ret) == 0 {
		panic("no return value specified for AliasExists")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(string) (bool, error)); ok {
		return returnFunc(alias)
	}
	if returnFunc, ok := ret.Get(0).(func(string) bool); ok {
		r0 = returnFunc(alias)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(string) error); ok {
		r1 = returnFunc(alias)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// IndexAPI_AliasExists_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AliasExists'
type IndexAPI_AliasExists_Call struct {
	*mock.Call
}

// AliasExists is a helper method to define mock.On call
//   - alias string
func (_e *IndexAPI_Expecter) AliasExists(alias interface{}) *IndexAPI_AliasExists_Call {
	return &IndexAPI_AliasExists_Call{Call: _e.mock.On("AliasExists", alias)}
}

func (_c *IndexAPI_AliasExists_Call) Run(run func(alias string)) *IndexAPI_AliasExists_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndexAPI_AliasExists_Call) Return(b bool, err error) *IndexAPI_AliasExists_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *IndexAPI_AliasExists_Call) RunAndReturn(run func(alias string) (bool, error)) *IndexAPI_AliasExists_Call {
	_c.Call.Return(run)
	return _c
}

// CreateAlias provides a mock function for the type IndexAPI
func (_mock *IndexAPI) CreateAlias(aliases []client.Alias) error {
	ret := _mock.Called(aliases)

	if len(ret) == 0 {
		panic("no return value specified for CreateAlias")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func([]client.Alias) error); ok {
		r0 = returnFunc(aliases)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// IndexAPI_CreateAlias_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateAlias'
type IndexAPI_CreateAlias_Call struct {
	*mock.Call
}

// CreateAlias is a helper method to define mock.On call
//   - aliases []client.Alias
func (_e *IndexAPI_Expecter) CreateAlias(aliases interface{}) *IndexAPI_CreateAlias_Call {
	return &IndexAPI_CreateAlias_Call{Call: _e.mock.On("CreateAlias", aliases)}
}

func (_c *IndexAPI_CreateAlias_Call) Run(run func(aliases []client.Alias)) *IndexAPI_CreateAlias_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 []client.Alias
		if args[0] != nil {
			arg0 = args[0].([]client.Alias)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndexAPI_CreateAlias_Call) Return(err error) *IndexAPI_CreateAlias_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *IndexAPI_CreateAlias_Call) RunAndReturn(run func(aliases []client.Alias) error) *IndexAPI_CreateAlias_Call {
	_c.Call.Return(run)
	return _c
}

// CreateIndex provides a mock function for the type IndexAPI
func (_mock *IndexAPI) CreateIndex(index string) error {
	ret := _mock.Called(index)

	if len(ret) == 0 {
		panic("no return value specified for CreateIndex")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string) error); ok {
		r0 = returnFunc(index)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// IndexAPI_CreateIndex_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateIndex'
type IndexAPI_CreateIndex_Call struct {
	*mock.Call
}

// CreateIndex is a helper method to define mock.On call
//   - index string
func (_e *IndexAPI_Expecter) CreateIndex(index interface{}) *IndexAPI_CreateIndex_Call {
	return &IndexAPI_CreateIndex_Call{Call: _e.mock.On("CreateIndex", index)}
}

func (_c *IndexAPI_CreateIndex_Call) Run(run func(index string)) *IndexAPI_CreateIndex_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndexAPI_CreateIndex_Call) Return(err error) *IndexAPI_CreateIndex_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *IndexAPI_CreateIndex_Call) RunAndReturn(run func(index string) error) *IndexAPI_CreateIndex_Call {
	_c.Call.Return(run)
	return _c
}

// CreateTemplate provides a mock function for the type IndexAPI
func (_mock *IndexAPI) CreateTemplate(template string, name string) error {
	ret := _mock.Called(template, name)

	if len(ret) == 0 {
		panic("no return value specified for CreateTemplate")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = returnFunc(template, name)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// IndexAPI_CreateTemplate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateTemplate'
type IndexAPI_CreateTemplate_Call struct {
	*mock.Call
}

// CreateTemplate is a helper method to define mock.On call
//   - template string
//   - name string
func (_e *IndexAPI_Expecter) CreateTemplate(template interface{}, name interface{}) *IndexAPI_CreateTemplate_Call {
	return &IndexAPI_CreateTemplate_Call{Call: _e.mock.On("CreateTemplate", template, name)}
}

func (_c *IndexAPI_CreateTemplate_Call) Run(run func(template string, name string)) *IndexAPI_CreateTemplate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *IndexAPI_CreateTemplate_Call) Return(err error) *IndexAPI_CreateTemplate_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *IndexAPI_CreateTemplate_Call) RunAndReturn(run func(template string, name string) error) *IndexAPI_CreateTemplate_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAlias provides a mock function for the type IndexAPI
func (_mock *IndexAPI) DeleteAlias(aliases []client.Alias) error {
	ret := _mock.Called(aliases)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAlias")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func([]client.Alias) error); ok {
		r0 = returnFunc(aliases)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// IndexAPI_DeleteAlias_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAlias'
type IndexAPI_DeleteAlias_Call struct {
	*mock.Call
}

// DeleteAlias is a helper method to define mock.On call
//   - aliases []client.Alias
func (_e *IndexAPI_Expecter) DeleteAlias(aliases interface{}) *IndexAPI_DeleteAlias_Call {
	return &IndexAPI_DeleteAlias_Call{Call: _e.mock.On("DeleteAlias", aliases)}
}

func (_c *IndexAPI_DeleteAlias_Call) Run(run func(aliases []client.Alias)) *IndexAPI_DeleteAlias_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 []client.Alias
		if args[0] != nil {
			arg0 = args[0].([]client.Alias)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndexAPI_DeleteAlias_Call) Return(err error) *IndexAPI_DeleteAlias_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *IndexAPI_DeleteAlias_Call) RunAndReturn(run func(aliases []client.Alias) error) *IndexAPI_DeleteAlias_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteIndices provides a mock function for the type IndexAPI
func (_mock *IndexAPI) DeleteIndices(indices []client.Index) error {
	ret := _mock.Called(indices)

	if len(ret) == 0 {
		panic("no return value specified for DeleteIndices")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func([]client.Index) error); ok {
		r0 = returnFunc(indices)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// IndexAPI_DeleteIndices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteIndices'
type IndexAPI_DeleteIndices_Call struct {
	*mock.Call
}

// DeleteIndices is a helper method to define mock.On call
//   - indices []client.Index
func (_e *IndexAPI_Expecter) DeleteIndices(indices interface{}) *IndexAPI_DeleteIndices_Call {
	return &IndexAPI_DeleteIndices_Call{Call: _e.mock.On("DeleteIndices", indices)}
}

func (_c *IndexAPI_DeleteIndices_Call) Run(run func(indices []client.Index)) *IndexAPI_DeleteIndices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 []client.Index
		if args[0] != nil {
			arg0 = args[0].([]client.Index)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndexAPI_DeleteIndices_Call) Return(err error) *IndexAPI_DeleteIndices_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *IndexAPI_DeleteIndices_Call) RunAndReturn(run func(indices []client.Index) error) *IndexAPI_DeleteIndices_Call {
	_c.Call.Return(run)
	return _c
}

// GetJaegerIndices provides a mock function for the type IndexAPI
func (_mock *IndexAPI) GetJaegerIndices(prefix string) ([]client.Index, error) {
	ret := _mock.Called(prefix)

	if len(ret) == 0 {
		panic("no return value specified for GetJaegerIndices")
	}

	var r0 []client.Index
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(string) ([]client.Index, error)); ok {
		return returnFunc(prefix)
	}
	if returnFunc, ok := ret.Get(0).(func(string) []client.Index); ok {
		r0 = returnFunc(prefix)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]client.Index)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(string) error); ok {
		r1 = returnFunc(prefix)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// IndexAPI_GetJaegerIndices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetJaegerIndices'
type IndexAPI_GetJaegerIndices_Call struct {
	*mock.Call
}

// GetJaegerIndices is a helper method to define mock.On call
//   - prefix string
func (_e *IndexAPI_Expecter) GetJaegerIndices(prefix interface{}) *IndexAPI_GetJaegerIndices_Call {
	return &IndexAPI_GetJaegerIndices_Call{Call: _e.mock.On("GetJaegerIndices", prefix)}
}

func (_c *IndexAPI_GetJaegerIndices_Call) Run(run func(prefix string)) *IndexAPI_GetJaegerIndices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndexAPI_GetJaegerIndices_Call) Return(indexs []client.Index, err error) *IndexAPI_GetJaegerIndices_Call {
	_c.Call.Return(indexs, err)
	return _c
}

func (_c *IndexAPI_GetJaegerIndices_Call) RunAndReturn(run func(prefix string) ([]client.Index, error)) *IndexAPI_GetJaegerIndices_Call {
	_c.Call.Return(run)
	return _c
}

// IndexExists provides a mock function for the type IndexAPI
func (_mock *IndexAPI) IndexExists(index string) (bool, error) {
	ret := _mock.Called(index)

	if len(ret) == 0 {
		panic("no return value specified for IndexExists")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(string) (bool, error)); ok {
		return returnFunc(index)
	}
	if returnFunc, ok := ret.Get(0).(func(string) bool); ok {
		r0 = returnFunc(index)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(string) error); ok {
		r1 = returnFunc(index)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// IndexAPI_IndexExists_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IndexExists'
type IndexAPI_IndexExists_Call struct {
	*mock.Call
}

// IndexExists is a helper method to define mock.On call
//   - index string
func (_e *IndexAPI_Expecter) IndexExists(index interface{}) *IndexAPI_IndexExists_Call {
	return &IndexAPI_IndexExists_Call{Call: _e.mock.On("IndexExists", index)}
}

func (_c *IndexAPI_IndexExists_Call) Run(run func(index string)) *IndexAPI_IndexExists_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndexAPI_IndexExists_Call) Return(b bool, err error) *IndexAPI_IndexExists_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *IndexAPI_IndexExists_Call) RunAndReturn(run func(index string) (bool, error)) *IndexAPI_IndexExists_Call {
	_c.Call.Return(run)
	return _c
}

// Rollover provides a mock function for the type IndexAPI
func (_mock *IndexAPI) Rollover(rolloverTarget string, conditions map[string]any) error {
	ret := _mock.Called(rolloverTarget, conditions)

	if len(ret) == 0 {
		panic("no return value specified for Rollover")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string, map[string]any) error); ok {
		r0 = returnFunc(rolloverTarget, conditions)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// IndexAPI_Rollover_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Rollover'
type IndexAPI_Rollover_Call struct {
	*mock.Call
}

// Rollover is a helper method to define mock.On call
//   - rolloverTarget string
//   - conditions map[string]any
func (_e *IndexAPI_Expecter) Rollover(rolloverTarget interface{}, conditions interface{}) *IndexAPI_Rollover_Call {
	return &IndexAPI_Rollover_Call{Call: _e.mock.On("Rollover", rolloverTarget, conditions)}
}

func (_c *IndexAPI_Rollover_Call) Run(run func(rolloverTarget string, conditions map[string]any)) *IndexAPI_Rollover_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 map[string]any
		if args[1] != nil {
			arg1 = args[1].(map[string]any)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *IndexAPI_Rollover_Call) Return(err error) *IndexAPI_Rollover_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *IndexAPI_Rollover_Call) RunAndReturn(run func(rolloverTarget string, conditions map[string]any) error) *IndexAPI_Rollover_Call {
	_c.Call.Return(run)
	return _c
}

// NewClusterAPI creates a new instance of ClusterAPI. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClusterAPI(t interface {
	mock.TestingT
	Cleanup(func())
}) *ClusterAPI {
	mock := &ClusterAPI{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// ClusterAPI is an autogenerated mock type for the ClusterAPI type
type ClusterAPI struct {
	mock.Mock
}

type ClusterAPI_Expecter struct {
	mock *mock.Mock
}

func (_m *ClusterAPI) EXPECT() *ClusterAPI_Expecter {
	return &ClusterAPI_Expecter{mock: &_m.Mock}
}

// Version provides a mock function for the type ClusterAPI
func (_mock *ClusterAPI) Version() (uint, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Version")
	}

	var r0 uint
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (uint, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() uint); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Get(0).(uint)
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// ClusterAPI_Version_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Version'
type ClusterAPI_Version_Call struct {
	*mock.Call
}

// Version is a helper method to define mock.On call
func (_e *ClusterAPI_Expecter) Version() *ClusterAPI_Version_Call {
	return &ClusterAPI_Version_Call{Call: _e.mock.On("Version")}
}

func (_c *ClusterAPI_Version_Call) Run(run func()) *ClusterAPI_Version_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *ClusterAPI_Version_Call) Return(v uint, err error) *ClusterAPI_Version_Call {
	_c.Call.Return(v, err)
	return _c
}

func (_c *ClusterAPI_Version_Call) RunAndReturn(run func() (uint, error)) *ClusterAPI_Version_Call {
	_c.Call.Return(run)
	return _c
}

// NewIndexManagementLifecycleAPI creates a new instance of IndexManagementLifecycleAPI. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewIndexManagementLifecycleAPI(t interface {
	mock.TestingT
	Cleanup(func())
}) *IndexManagementLifecycleAPI {
	mock := &IndexManagementLifecycleAPI{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// IndexManagementLifecycleAPI is an autogenerated mock type for the IndexManagementLifecycleAPI type
type IndexManagementLifecycleAPI struct {
	mock.Mock
}

type IndexManagementLifecycleAPI_Expecter struct {
	mock *mock.Mock
}

func (_m *IndexManagementLifecycleAPI) EXPECT() *IndexManagementLifecycleAPI_Expecter {
	return &IndexManagementLifecycleAPI_Expecter{mock: &_m.Mock}
}

// Exists provides a mock function for the type IndexManagementLifecycleAPI
func (_mock *IndexManagementLifecycleAPI) Exists(name string) (bool, error) {
	ret := _mock.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for Exists")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(string) (bool, error)); ok {
		return returnFunc(name)
	}
	if returnFunc, ok := ret.Get(0).(func(string) bool); ok {
		r0 = returnFunc(name)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(string) error); ok {
		r1 = returnFunc(name)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// IndexManagementLifecycleAPI_Exists_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Exists'
type IndexManagementLifecycleAPI_Exists_Call struct {
	*mock.Call
}

// Exists is a helper method to define mock.On call
//   - name string
func (_e *IndexManagementLifecycleAPI_Expecter) Exists(name interface{}) *IndexManagementLifecycleAPI_Exists_Call {
	return &IndexManagementLifecycleAPI_Exists_Call{Call: _e.mock.On("Exists", name)}
}

func (_c *IndexManagementLifecycleAPI_Exists_Call) Run(run func(name string)) *IndexManagementLifecycleAPI_Exists_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *IndexManagementLifecycleAPI_Exists_Call) Return(b bool, err error) *IndexManagementLifecycleAPI_Exists_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *IndexManagementLifecycleAPI_Exists_Call) RunAndReturn(run func(name string) (bool, error)) *IndexManagementLifecycleAPI_Exists_Call {
	_c.Call.Return(run)
	return _c
}
