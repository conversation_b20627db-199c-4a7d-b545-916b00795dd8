// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify
// Copyright (c) The Jaeger Authors.
// SPDX-License-Identifier: Apache-2.0
//
// Run 'make generate-mocks' to regenerate.

package mocks

import (
	"github.com/jaegertracing/jaeger/internal/storage/cassandra"
	mock "github.com/stretchr/testify/mock"
)

// NewSession creates a new instance of Session. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewSession(t interface {
	mock.TestingT
	Cleanup(func())
}) *Session {
	mock := &Session{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// Session is an autogenerated mock type for the Session type
type Session struct {
	mock.Mock
}

type Session_Expecter struct {
	mock *mock.Mock
}

func (_m *Session) EXPECT() *Session_Expecter {
	return &Session_Expecter{mock: &_m.Mock}
}

// Close provides a mock function for the type Session
func (_mock *Session) Close() {
	_mock.Called()
	return
}

// Session_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type Session_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *Session_Expecter) Close() *Session_Close_Call {
	return &Session_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *Session_Close_Call) Run(run func()) *Session_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Session_Close_Call) Return() *Session_Close_Call {
	_c.Call.Return()
	return _c
}

func (_c *Session_Close_Call) RunAndReturn(run func()) *Session_Close_Call {
	_c.Run(run)
	return _c
}

// Query provides a mock function for the type Session
func (_mock *Session) Query(stmt string, values ...any) cassandra.Query {
	var tmpRet mock.Arguments
	if len(values) > 0 {
		tmpRet = _mock.Called(stmt, values)
	} else {
		tmpRet = _mock.Called(stmt)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Query")
	}

	var r0 cassandra.Query
	if returnFunc, ok := ret.Get(0).(func(string, ...any) cassandra.Query); ok {
		r0 = returnFunc(stmt, values...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(cassandra.Query)
		}
	}
	return r0
}

// Session_Query_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Query'
type Session_Query_Call struct {
	*mock.Call
}

// Query is a helper method to define mock.On call
//   - stmt string
//   - values ...any
func (_e *Session_Expecter) Query(stmt interface{}, values ...interface{}) *Session_Query_Call {
	return &Session_Query_Call{Call: _e.mock.On("Query",
		append([]interface{}{stmt}, values...)...)}
}

func (_c *Session_Query_Call) Run(run func(stmt string, values ...any)) *Session_Query_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 []any
		var variadicArgs []any
		if len(args) > 1 {
			variadicArgs = args[1].([]any)
		}
		arg1 = variadicArgs
		run(
			arg0,
			arg1...,
		)
	})
	return _c
}

func (_c *Session_Query_Call) Return(query cassandra.Query) *Session_Query_Call {
	_c.Call.Return(query)
	return _c
}

func (_c *Session_Query_Call) RunAndReturn(run func(stmt string, values ...any) cassandra.Query) *Session_Query_Call {
	_c.Call.Return(run)
	return _c
}

// NewQuery creates a new instance of Query. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewQuery(t interface {
	mock.TestingT
	Cleanup(func())
}) *Query {
	mock := &Query{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// Query is an autogenerated mock type for the Query type
type Query struct {
	mock.Mock
}

type Query_Expecter struct {
	mock *mock.Mock
}

func (_m *Query) EXPECT() *Query_Expecter {
	return &Query_Expecter{mock: &_m.Mock}
}

// Bind provides a mock function for the type Query
func (_mock *Query) Bind(v ...any) cassandra.Query {
	var tmpRet mock.Arguments
	if len(v) > 0 {
		tmpRet = _mock.Called(v)
	} else {
		tmpRet = _mock.Called()
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Bind")
	}

	var r0 cassandra.Query
	if returnFunc, ok := ret.Get(0).(func(...any) cassandra.Query); ok {
		r0 = returnFunc(v...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(cassandra.Query)
		}
	}
	return r0
}

// Query_Bind_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Bind'
type Query_Bind_Call struct {
	*mock.Call
}

// Bind is a helper method to define mock.On call
//   - v ...any
func (_e *Query_Expecter) Bind(v ...interface{}) *Query_Bind_Call {
	return &Query_Bind_Call{Call: _e.mock.On("Bind",
		append([]interface{}{}, v...)...)}
}

func (_c *Query_Bind_Call) Run(run func(v ...any)) *Query_Bind_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 []any
		var variadicArgs []any
		if len(args) > 0 {
			variadicArgs = args[0].([]any)
		}
		arg0 = variadicArgs
		run(
			arg0...,
		)
	})
	return _c
}

func (_c *Query_Bind_Call) Return(query cassandra.Query) *Query_Bind_Call {
	_c.Call.Return(query)
	return _c
}

func (_c *Query_Bind_Call) RunAndReturn(run func(v ...any) cassandra.Query) *Query_Bind_Call {
	_c.Call.Return(run)
	return _c
}

// Consistency provides a mock function for the type Query
func (_mock *Query) Consistency(level cassandra.Consistency) cassandra.Query {
	ret := _mock.Called(level)

	if len(ret) == 0 {
		panic("no return value specified for Consistency")
	}

	var r0 cassandra.Query
	if returnFunc, ok := ret.Get(0).(func(cassandra.Consistency) cassandra.Query); ok {
		r0 = returnFunc(level)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(cassandra.Query)
		}
	}
	return r0
}

// Query_Consistency_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Consistency'
type Query_Consistency_Call struct {
	*mock.Call
}

// Consistency is a helper method to define mock.On call
//   - level cassandra.Consistency
func (_e *Query_Expecter) Consistency(level interface{}) *Query_Consistency_Call {
	return &Query_Consistency_Call{Call: _e.mock.On("Consistency", level)}
}

func (_c *Query_Consistency_Call) Run(run func(level cassandra.Consistency)) *Query_Consistency_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 cassandra.Consistency
		if args[0] != nil {
			arg0 = args[0].(cassandra.Consistency)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *Query_Consistency_Call) Return(query cassandra.Query) *Query_Consistency_Call {
	_c.Call.Return(query)
	return _c
}

func (_c *Query_Consistency_Call) RunAndReturn(run func(level cassandra.Consistency) cassandra.Query) *Query_Consistency_Call {
	_c.Call.Return(run)
	return _c
}

// Exec provides a mock function for the type Query
func (_mock *Query) Exec() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Exec")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// Query_Exec_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Exec'
type Query_Exec_Call struct {
	*mock.Call
}

// Exec is a helper method to define mock.On call
func (_e *Query_Expecter) Exec() *Query_Exec_Call {
	return &Query_Exec_Call{Call: _e.mock.On("Exec")}
}

func (_c *Query_Exec_Call) Run(run func()) *Query_Exec_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Query_Exec_Call) Return(err error) *Query_Exec_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *Query_Exec_Call) RunAndReturn(run func() error) *Query_Exec_Call {
	_c.Call.Return(run)
	return _c
}

// Iter provides a mock function for the type Query
func (_mock *Query) Iter() cassandra.Iterator {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Iter")
	}

	var r0 cassandra.Iterator
	if returnFunc, ok := ret.Get(0).(func() cassandra.Iterator); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(cassandra.Iterator)
		}
	}
	return r0
}

// Query_Iter_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Iter'
type Query_Iter_Call struct {
	*mock.Call
}

// Iter is a helper method to define mock.On call
func (_e *Query_Expecter) Iter() *Query_Iter_Call {
	return &Query_Iter_Call{Call: _e.mock.On("Iter")}
}

func (_c *Query_Iter_Call) Run(run func()) *Query_Iter_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Query_Iter_Call) Return(iterator cassandra.Iterator) *Query_Iter_Call {
	_c.Call.Return(iterator)
	return _c
}

func (_c *Query_Iter_Call) RunAndReturn(run func() cassandra.Iterator) *Query_Iter_Call {
	_c.Call.Return(run)
	return _c
}

// PageSize provides a mock function for the type Query
func (_mock *Query) PageSize(n int) cassandra.Query {
	ret := _mock.Called(n)

	if len(ret) == 0 {
		panic("no return value specified for PageSize")
	}

	var r0 cassandra.Query
	if returnFunc, ok := ret.Get(0).(func(int) cassandra.Query); ok {
		r0 = returnFunc(n)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(cassandra.Query)
		}
	}
	return r0
}

// Query_PageSize_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PageSize'
type Query_PageSize_Call struct {
	*mock.Call
}

// PageSize is a helper method to define mock.On call
//   - n int
func (_e *Query_Expecter) PageSize(n interface{}) *Query_PageSize_Call {
	return &Query_PageSize_Call{Call: _e.mock.On("PageSize", n)}
}

func (_c *Query_PageSize_Call) Run(run func(n int)) *Query_PageSize_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 int
		if args[0] != nil {
			arg0 = args[0].(int)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *Query_PageSize_Call) Return(query cassandra.Query) *Query_PageSize_Call {
	_c.Call.Return(query)
	return _c
}

func (_c *Query_PageSize_Call) RunAndReturn(run func(n int) cassandra.Query) *Query_PageSize_Call {
	_c.Call.Return(run)
	return _c
}

// ScanCAS provides a mock function for the type Query
func (_mock *Query) ScanCAS(dest ...any) (bool, error) {
	var tmpRet mock.Arguments
	if len(dest) > 0 {
		tmpRet = _mock.Called(dest)
	} else {
		tmpRet = _mock.Called()
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for ScanCAS")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(...any) (bool, error)); ok {
		return returnFunc(dest...)
	}
	if returnFunc, ok := ret.Get(0).(func(...any) bool); ok {
		r0 = returnFunc(dest...)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(...any) error); ok {
		r1 = returnFunc(dest...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Query_ScanCAS_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ScanCAS'
type Query_ScanCAS_Call struct {
	*mock.Call
}

// ScanCAS is a helper method to define mock.On call
//   - dest ...any
func (_e *Query_Expecter) ScanCAS(dest ...interface{}) *Query_ScanCAS_Call {
	return &Query_ScanCAS_Call{Call: _e.mock.On("ScanCAS",
		append([]interface{}{}, dest...)...)}
}

func (_c *Query_ScanCAS_Call) Run(run func(dest ...any)) *Query_ScanCAS_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 []any
		var variadicArgs []any
		if len(args) > 0 {
			variadicArgs = args[0].([]any)
		}
		arg0 = variadicArgs
		run(
			arg0...,
		)
	})
	return _c
}

func (_c *Query_ScanCAS_Call) Return(b bool, err error) *Query_ScanCAS_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *Query_ScanCAS_Call) RunAndReturn(run func(dest ...any) (bool, error)) *Query_ScanCAS_Call {
	_c.Call.Return(run)
	return _c
}

// String provides a mock function for the type Query
func (_mock *Query) String() string {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for String")
	}

	var r0 string
	if returnFunc, ok := ret.Get(0).(func() string); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Get(0).(string)
	}
	return r0
}

// Query_String_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'String'
type Query_String_Call struct {
	*mock.Call
}

// String is a helper method to define mock.On call
func (_e *Query_Expecter) String() *Query_String_Call {
	return &Query_String_Call{Call: _e.mock.On("String")}
}

func (_c *Query_String_Call) Run(run func()) *Query_String_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Query_String_Call) Return(s string) *Query_String_Call {
	_c.Call.Return(s)
	return _c
}

func (_c *Query_String_Call) RunAndReturn(run func() string) *Query_String_Call {
	_c.Call.Return(run)
	return _c
}

// NewIterator creates a new instance of Iterator. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewIterator(t interface {
	mock.TestingT
	Cleanup(func())
}) *Iterator {
	mock := &Iterator{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// Iterator is an autogenerated mock type for the Iterator type
type Iterator struct {
	mock.Mock
}

type Iterator_Expecter struct {
	mock *mock.Mock
}

func (_m *Iterator) EXPECT() *Iterator_Expecter {
	return &Iterator_Expecter{mock: &_m.Mock}
}

// Close provides a mock function for the type Iterator
func (_mock *Iterator) Close() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// Iterator_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type Iterator_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *Iterator_Expecter) Close() *Iterator_Close_Call {
	return &Iterator_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *Iterator_Close_Call) Run(run func()) *Iterator_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Iterator_Close_Call) Return(err error) *Iterator_Close_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *Iterator_Close_Call) RunAndReturn(run func() error) *Iterator_Close_Call {
	_c.Call.Return(run)
	return _c
}

// Scan provides a mock function for the type Iterator
func (_mock *Iterator) Scan(dest ...any) bool {
	var tmpRet mock.Arguments
	if len(dest) > 0 {
		tmpRet = _mock.Called(dest)
	} else {
		tmpRet = _mock.Called()
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for Scan")
	}

	var r0 bool
	if returnFunc, ok := ret.Get(0).(func(...any) bool); ok {
		r0 = returnFunc(dest...)
	} else {
		r0 = ret.Get(0).(bool)
	}
	return r0
}

// Iterator_Scan_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Scan'
type Iterator_Scan_Call struct {
	*mock.Call
}

// Scan is a helper method to define mock.On call
//   - dest ...any
func (_e *Iterator_Expecter) Scan(dest ...interface{}) *Iterator_Scan_Call {
	return &Iterator_Scan_Call{Call: _e.mock.On("Scan",
		append([]interface{}{}, dest...)...)}
}

func (_c *Iterator_Scan_Call) Run(run func(dest ...any)) *Iterator_Scan_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 []any
		var variadicArgs []any
		if len(args) > 0 {
			variadicArgs = args[0].([]any)
		}
		arg0 = variadicArgs
		run(
			arg0...,
		)
	})
	return _c
}

func (_c *Iterator_Scan_Call) Return(b bool) *Iterator_Scan_Call {
	_c.Call.Return(b)
	return _c
}

func (_c *Iterator_Scan_Call) RunAndReturn(run func(dest ...any) bool) *Iterator_Scan_Call {
	_c.Call.Return(run)
	return _c
}
