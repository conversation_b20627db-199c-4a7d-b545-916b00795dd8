// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify
// Copyright (c) The Jaeger Authors.
// SPDX-License-Identifier: Apache-2.0
//
// Run 'make generate-mocks' to regenerate.

package mocks

import (
	"time"

	"github.com/jaegertracing/jaeger/internal/storage/v1/api/samplingstore/model"
	mock "github.com/stretchr/testify/mock"
)

// NewStore creates a new instance of Store. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *Store {
	mock := &Store{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// Store is an autogenerated mock type for the Store type
type Store struct {
	mock.Mock
}

type Store_Expecter struct {
	mock *mock.Mock
}

func (_m *Store) EXPECT() *Store_Expecter {
	return &Store_Expecter{mock: &_m.Mock}
}

// GetLatestProbabilities provides a mock function for the type Store
func (_mock *Store) GetLatestProbabilities() (model.ServiceOperationProbabilities, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetLatestProbabilities")
	}

	var r0 model.ServiceOperationProbabilities
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (model.ServiceOperationProbabilities, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() model.ServiceOperationProbabilities); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(model.ServiceOperationProbabilities)
		}
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Store_GetLatestProbabilities_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLatestProbabilities'
type Store_GetLatestProbabilities_Call struct {
	*mock.Call
}

// GetLatestProbabilities is a helper method to define mock.On call
func (_e *Store_Expecter) GetLatestProbabilities() *Store_GetLatestProbabilities_Call {
	return &Store_GetLatestProbabilities_Call{Call: _e.mock.On("GetLatestProbabilities")}
}

func (_c *Store_GetLatestProbabilities_Call) Run(run func()) *Store_GetLatestProbabilities_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Store_GetLatestProbabilities_Call) Return(serviceOperationProbabilities model.ServiceOperationProbabilities, err error) *Store_GetLatestProbabilities_Call {
	_c.Call.Return(serviceOperationProbabilities, err)
	return _c
}

func (_c *Store_GetLatestProbabilities_Call) RunAndReturn(run func() (model.ServiceOperationProbabilities, error)) *Store_GetLatestProbabilities_Call {
	_c.Call.Return(run)
	return _c
}

// GetThroughput provides a mock function for the type Store
func (_mock *Store) GetThroughput(start time.Time, end time.Time) ([]*model.Throughput, error) {
	ret := _mock.Called(start, end)

	if len(ret) == 0 {
		panic("no return value specified for GetThroughput")
	}

	var r0 []*model.Throughput
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(time.Time, time.Time) ([]*model.Throughput, error)); ok {
		return returnFunc(start, end)
	}
	if returnFunc, ok := ret.Get(0).(func(time.Time, time.Time) []*model.Throughput); ok {
		r0 = returnFunc(start, end)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.Throughput)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(time.Time, time.Time) error); ok {
		r1 = returnFunc(start, end)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Store_GetThroughput_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetThroughput'
type Store_GetThroughput_Call struct {
	*mock.Call
}

// GetThroughput is a helper method to define mock.On call
//   - start time.Time
//   - end time.Time
func (_e *Store_Expecter) GetThroughput(start interface{}, end interface{}) *Store_GetThroughput_Call {
	return &Store_GetThroughput_Call{Call: _e.mock.On("GetThroughput", start, end)}
}

func (_c *Store_GetThroughput_Call) Run(run func(start time.Time, end time.Time)) *Store_GetThroughput_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 time.Time
		if args[0] != nil {
			arg0 = args[0].(time.Time)
		}
		var arg1 time.Time
		if args[1] != nil {
			arg1 = args[1].(time.Time)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *Store_GetThroughput_Call) Return(throughputs []*model.Throughput, err error) *Store_GetThroughput_Call {
	_c.Call.Return(throughputs, err)
	return _c
}

func (_c *Store_GetThroughput_Call) RunAndReturn(run func(start time.Time, end time.Time) ([]*model.Throughput, error)) *Store_GetThroughput_Call {
	_c.Call.Return(run)
	return _c
}

// InsertProbabilitiesAndQPS provides a mock function for the type Store
func (_mock *Store) InsertProbabilitiesAndQPS(hostname string, probabilities model.ServiceOperationProbabilities, qps model.ServiceOperationQPS) error {
	ret := _mock.Called(hostname, probabilities, qps)

	if len(ret) == 0 {
		panic("no return value specified for InsertProbabilitiesAndQPS")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string, model.ServiceOperationProbabilities, model.ServiceOperationQPS) error); ok {
		r0 = returnFunc(hostname, probabilities, qps)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// Store_InsertProbabilitiesAndQPS_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InsertProbabilitiesAndQPS'
type Store_InsertProbabilitiesAndQPS_Call struct {
	*mock.Call
}

// InsertProbabilitiesAndQPS is a helper method to define mock.On call
//   - hostname string
//   - probabilities model.ServiceOperationProbabilities
//   - qps model.ServiceOperationQPS
func (_e *Store_Expecter) InsertProbabilitiesAndQPS(hostname interface{}, probabilities interface{}, qps interface{}) *Store_InsertProbabilitiesAndQPS_Call {
	return &Store_InsertProbabilitiesAndQPS_Call{Call: _e.mock.On("InsertProbabilitiesAndQPS", hostname, probabilities, qps)}
}

func (_c *Store_InsertProbabilitiesAndQPS_Call) Run(run func(hostname string, probabilities model.ServiceOperationProbabilities, qps model.ServiceOperationQPS)) *Store_InsertProbabilitiesAndQPS_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 model.ServiceOperationProbabilities
		if args[1] != nil {
			arg1 = args[1].(model.ServiceOperationProbabilities)
		}
		var arg2 model.ServiceOperationQPS
		if args[2] != nil {
			arg2 = args[2].(model.ServiceOperationQPS)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *Store_InsertProbabilitiesAndQPS_Call) Return(err error) *Store_InsertProbabilitiesAndQPS_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *Store_InsertProbabilitiesAndQPS_Call) RunAndReturn(run func(hostname string, probabilities model.ServiceOperationProbabilities, qps model.ServiceOperationQPS) error) *Store_InsertProbabilitiesAndQPS_Call {
	_c.Call.Return(run)
	return _c
}

// InsertThroughput provides a mock function for the type Store
func (_mock *Store) InsertThroughput(throughput []*model.Throughput) error {
	ret := _mock.Called(throughput)

	if len(ret) == 0 {
		panic("no return value specified for InsertThroughput")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func([]*model.Throughput) error); ok {
		r0 = returnFunc(throughput)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// Store_InsertThroughput_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InsertThroughput'
type Store_InsertThroughput_Call struct {
	*mock.Call
}

// InsertThroughput is a helper method to define mock.On call
//   - throughput []*model.Throughput
func (_e *Store_Expecter) InsertThroughput(throughput interface{}) *Store_InsertThroughput_Call {
	return &Store_InsertThroughput_Call{Call: _e.mock.On("InsertThroughput", throughput)}
}

func (_c *Store_InsertThroughput_Call) Run(run func(throughput []*model.Throughput)) *Store_InsertThroughput_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 []*model.Throughput
		if args[0] != nil {
			arg0 = args[0].([]*model.Throughput)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *Store_InsertThroughput_Call) Return(err error) *Store_InsertThroughput_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *Store_InsertThroughput_Call) RunAndReturn(run func(throughput []*model.Throughput) error) *Store_InsertThroughput_Call {
	_c.Call.Return(run)
	return _c
}
