// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify
// Copyright (c) The Jaeger Authors.
// SPDX-License-Identifier: Apache-2.0
//
// Run 'make generate-mocks' to regenerate.

package mocks

import (
	"context"

	"github.com/jaegertracing/jaeger-idl/model/v1"
	"github.com/jaegertracing/jaeger/internal/storage/v1/api/spanstore"
	mock "github.com/stretchr/testify/mock"
)

// NewWriter creates a new instance of Writer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewWriter(t interface {
	mock.TestingT
	Cleanup(func())
}) *Writer {
	mock := &Writer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// Writer is an autogenerated mock type for the Writer type
type Writer struct {
	mock.Mock
}

type Writer_Expecter struct {
	mock *mock.Mock
}

func (_m *Writer) EXPECT() *Writer_Expecter {
	return &Writer_Expecter{mock: &_m.Mock}
}

// WriteSpan provides a mock function for the type Writer
func (_mock *Writer) WriteSpan(ctx context.Context, span *model.Span) error {
	ret := _mock.Called(ctx, span)

	if len(ret) == 0 {
		panic("no return value specified for WriteSpan")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *model.Span) error); ok {
		r0 = returnFunc(ctx, span)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// Writer_WriteSpan_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WriteSpan'
type Writer_WriteSpan_Call struct {
	*mock.Call
}

// WriteSpan is a helper method to define mock.On call
//   - ctx context.Context
//   - span *model.Span
func (_e *Writer_Expecter) WriteSpan(ctx interface{}, span interface{}) *Writer_WriteSpan_Call {
	return &Writer_WriteSpan_Call{Call: _e.mock.On("WriteSpan", ctx, span)}
}

func (_c *Writer_WriteSpan_Call) Run(run func(ctx context.Context, span *model.Span)) *Writer_WriteSpan_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *model.Span
		if args[1] != nil {
			arg1 = args[1].(*model.Span)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *Writer_WriteSpan_Call) Return(err error) *Writer_WriteSpan_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *Writer_WriteSpan_Call) RunAndReturn(run func(ctx context.Context, span *model.Span) error) *Writer_WriteSpan_Call {
	_c.Call.Return(run)
	return _c
}

// NewReader creates a new instance of Reader. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewReader(t interface {
	mock.TestingT
	Cleanup(func())
}) *Reader {
	mock := &Reader{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// Reader is an autogenerated mock type for the Reader type
type Reader struct {
	mock.Mock
}

type Reader_Expecter struct {
	mock *mock.Mock
}

func (_m *Reader) EXPECT() *Reader_Expecter {
	return &Reader_Expecter{mock: &_m.Mock}
}

// FindTraceIDs provides a mock function for the type Reader
func (_mock *Reader) FindTraceIDs(ctx context.Context, query *spanstore.TraceQueryParameters) ([]model.TraceID, error) {
	ret := _mock.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for FindTraceIDs")
	}

	var r0 []model.TraceID
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *spanstore.TraceQueryParameters) ([]model.TraceID, error)); ok {
		return returnFunc(ctx, query)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *spanstore.TraceQueryParameters) []model.TraceID); ok {
		r0 = returnFunc(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.TraceID)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *spanstore.TraceQueryParameters) error); ok {
		r1 = returnFunc(ctx, query)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Reader_FindTraceIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTraceIDs'
type Reader_FindTraceIDs_Call struct {
	*mock.Call
}

// FindTraceIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - query *spanstore.TraceQueryParameters
func (_e *Reader_Expecter) FindTraceIDs(ctx interface{}, query interface{}) *Reader_FindTraceIDs_Call {
	return &Reader_FindTraceIDs_Call{Call: _e.mock.On("FindTraceIDs", ctx, query)}
}

func (_c *Reader_FindTraceIDs_Call) Run(run func(ctx context.Context, query *spanstore.TraceQueryParameters)) *Reader_FindTraceIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *spanstore.TraceQueryParameters
		if args[1] != nil {
			arg1 = args[1].(*spanstore.TraceQueryParameters)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *Reader_FindTraceIDs_Call) Return(traceIDs []model.TraceID, err error) *Reader_FindTraceIDs_Call {
	_c.Call.Return(traceIDs, err)
	return _c
}

func (_c *Reader_FindTraceIDs_Call) RunAndReturn(run func(ctx context.Context, query *spanstore.TraceQueryParameters) ([]model.TraceID, error)) *Reader_FindTraceIDs_Call {
	_c.Call.Return(run)
	return _c
}

// FindTraces provides a mock function for the type Reader
func (_mock *Reader) FindTraces(ctx context.Context, query *spanstore.TraceQueryParameters) ([]*model.Trace, error) {
	ret := _mock.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for FindTraces")
	}

	var r0 []*model.Trace
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, *spanstore.TraceQueryParameters) ([]*model.Trace, error)); ok {
		return returnFunc(ctx, query)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, *spanstore.TraceQueryParameters) []*model.Trace); ok {
		r0 = returnFunc(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*model.Trace)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, *spanstore.TraceQueryParameters) error); ok {
		r1 = returnFunc(ctx, query)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Reader_FindTraces_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTraces'
type Reader_FindTraces_Call struct {
	*mock.Call
}

// FindTraces is a helper method to define mock.On call
//   - ctx context.Context
//   - query *spanstore.TraceQueryParameters
func (_e *Reader_Expecter) FindTraces(ctx interface{}, query interface{}) *Reader_FindTraces_Call {
	return &Reader_FindTraces_Call{Call: _e.mock.On("FindTraces", ctx, query)}
}

func (_c *Reader_FindTraces_Call) Run(run func(ctx context.Context, query *spanstore.TraceQueryParameters)) *Reader_FindTraces_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 *spanstore.TraceQueryParameters
		if args[1] != nil {
			arg1 = args[1].(*spanstore.TraceQueryParameters)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *Reader_FindTraces_Call) Return(traces []*model.Trace, err error) *Reader_FindTraces_Call {
	_c.Call.Return(traces, err)
	return _c
}

func (_c *Reader_FindTraces_Call) RunAndReturn(run func(ctx context.Context, query *spanstore.TraceQueryParameters) ([]*model.Trace, error)) *Reader_FindTraces_Call {
	_c.Call.Return(run)
	return _c
}

// GetOperations provides a mock function for the type Reader
func (_mock *Reader) GetOperations(ctx context.Context, query spanstore.OperationQueryParameters) ([]spanstore.Operation, error) {
	ret := _mock.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for GetOperations")
	}

	var r0 []spanstore.Operation
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, spanstore.OperationQueryParameters) ([]spanstore.Operation, error)); ok {
		return returnFunc(ctx, query)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, spanstore.OperationQueryParameters) []spanstore.Operation); ok {
		r0 = returnFunc(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]spanstore.Operation)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, spanstore.OperationQueryParameters) error); ok {
		r1 = returnFunc(ctx, query)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Reader_GetOperations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOperations'
type Reader_GetOperations_Call struct {
	*mock.Call
}

// GetOperations is a helper method to define mock.On call
//   - ctx context.Context
//   - query spanstore.OperationQueryParameters
func (_e *Reader_Expecter) GetOperations(ctx interface{}, query interface{}) *Reader_GetOperations_Call {
	return &Reader_GetOperations_Call{Call: _e.mock.On("GetOperations", ctx, query)}
}

func (_c *Reader_GetOperations_Call) Run(run func(ctx context.Context, query spanstore.OperationQueryParameters)) *Reader_GetOperations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 spanstore.OperationQueryParameters
		if args[1] != nil {
			arg1 = args[1].(spanstore.OperationQueryParameters)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *Reader_GetOperations_Call) Return(operations []spanstore.Operation, err error) *Reader_GetOperations_Call {
	_c.Call.Return(operations, err)
	return _c
}

func (_c *Reader_GetOperations_Call) RunAndReturn(run func(ctx context.Context, query spanstore.OperationQueryParameters) ([]spanstore.Operation, error)) *Reader_GetOperations_Call {
	_c.Call.Return(run)
	return _c
}

// GetServices provides a mock function for the type Reader
func (_mock *Reader) GetServices(ctx context.Context) ([]string, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetServices")
	}

	var r0 []string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]string, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []string); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Reader_GetServices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetServices'
type Reader_GetServices_Call struct {
	*mock.Call
}

// GetServices is a helper method to define mock.On call
//   - ctx context.Context
func (_e *Reader_Expecter) GetServices(ctx interface{}) *Reader_GetServices_Call {
	return &Reader_GetServices_Call{Call: _e.mock.On("GetServices", ctx)}
}

func (_c *Reader_GetServices_Call) Run(run func(ctx context.Context)) *Reader_GetServices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *Reader_GetServices_Call) Return(strings []string, err error) *Reader_GetServices_Call {
	_c.Call.Return(strings, err)
	return _c
}

func (_c *Reader_GetServices_Call) RunAndReturn(run func(ctx context.Context) ([]string, error)) *Reader_GetServices_Call {
	_c.Call.Return(run)
	return _c
}

// GetTrace provides a mock function for the type Reader
func (_mock *Reader) GetTrace(ctx context.Context, query spanstore.GetTraceParameters) (*model.Trace, error) {
	ret := _mock.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for GetTrace")
	}

	var r0 *model.Trace
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, spanstore.GetTraceParameters) (*model.Trace, error)); ok {
		return returnFunc(ctx, query)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, spanstore.GetTraceParameters) *model.Trace); ok {
		r0 = returnFunc(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Trace)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, spanstore.GetTraceParameters) error); ok {
		r1 = returnFunc(ctx, query)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Reader_GetTrace_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTrace'
type Reader_GetTrace_Call struct {
	*mock.Call
}

// GetTrace is a helper method to define mock.On call
//   - ctx context.Context
//   - query spanstore.GetTraceParameters
func (_e *Reader_Expecter) GetTrace(ctx interface{}, query interface{}) *Reader_GetTrace_Call {
	return &Reader_GetTrace_Call{Call: _e.mock.On("GetTrace", ctx, query)}
}

func (_c *Reader_GetTrace_Call) Run(run func(ctx context.Context, query spanstore.GetTraceParameters)) *Reader_GetTrace_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 spanstore.GetTraceParameters
		if args[1] != nil {
			arg1 = args[1].(spanstore.GetTraceParameters)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *Reader_GetTrace_Call) Return(trace *model.Trace, err error) *Reader_GetTrace_Call {
	_c.Call.Return(trace, err)
	return _c
}

func (_c *Reader_GetTrace_Call) RunAndReturn(run func(ctx context.Context, query spanstore.GetTraceParameters) (*model.Trace, error)) *Reader_GetTrace_Call {
	_c.Call.Return(run)
	return _c
}
