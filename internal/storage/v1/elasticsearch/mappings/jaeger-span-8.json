{
  "priority": {{ .Priority }},
  "index_patterns": "{{ .IndexPrefix }}jaeger-span-*",
  "template": {

    {{- if .UseILM}}
    "aliases": {
      "{{ .IndexPrefix }}jaeger-span-read": {}
    },
    {{- end}}
    "settings": {
      "index.number_of_shards": {{ .Shards }},
      "index.number_of_replicas": {{ .Replicas }},
      "index.mapping.nested_fields.limit": 50,
      "index.requests.cache.enable": true
      {{- if .UseILM }},
      "lifecycle": {
        "name": "{{ .ILMPolicyName }}",
        "rollover_alias": "{{ .IndexPrefix }}jaeger-span-write"
      }
      {{- end }}
    },
    "mappings": {
      "dynamic_templates": [
        {
          "span_tags_map": {
            "mapping": {
              "type": "keyword",
              "ignore_above": 256
            },
            "path_match": "tag.*"
          }
        },
        {
          "process_tags_map": {
            "mapping": {
              "type": "keyword",
              "ignore_above": 256
            },
            "path_match": "process.tag.*"
          }
        }
      ],
      "properties": {
        "traceID": {
          "type": "keyword",
          "ignore_above": 256
        },
        "parentSpanID": {
          "type": "keyword",
          "ignore_above": 256
        },
        "spanID": {
          "type": "keyword",
          "ignore_above": 256
        },
        "operationName": {
          "type": "keyword",
          "ignore_above": 256
        },
        "startTime": {
          "type": "long"
        },
        "startTimeMillis": {
          "type": "date",
          "format": "epoch_millis"
        },
        "duration": {
          "type": "long"
        },
        "flags": {
          "type": "integer"
        },
        "logs": {
          "type": "nested",
          "dynamic": false,
          "properties": {
            "timestamp": {
              "type": "long"
            },
            "fields": {
              "type": "nested",
              "dynamic": false,
              "properties": {
                "key": {
                  "type": "keyword",
                  "ignore_above": 256
                },
                "value": {
                  "type": "keyword",
                  "ignore_above": 256
                },
                "type": {
                  "type": "keyword",
                  "ignore_above": 256
                }
              }
            }
          }
        },
        "process": {
          "properties": {
            "serviceName": {
              "type": "keyword",
              "ignore_above": 256
            },
            "tag": {
              "type": "object"
            },
            "tags": {
              "type": "nested",
              "dynamic": false,
              "properties": {
                "key": {
                  "type": "keyword",
                  "ignore_above": 256
                },
                "value": {
                  "type": "keyword",
                  "ignore_above": 256
                },
                "type": {
                  "type": "keyword",
                  "ignore_above": 256
                }
              }
            }
          }
        },
        "references": {
          "type": "nested",
          "dynamic": false,
          "properties": {
            "refType": {
              "type": "keyword",
              "ignore_above": 256
            },
            "traceID": {
              "type": "keyword",
              "ignore_above": 256
            },
            "spanID": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "tag": {
          "type": "object"
        },
        "tags": {
          "type": "nested",
          "dynamic": false,
          "properties": {
            "key": {
              "type": "keyword",
              "ignore_above": 256
            },
            "value": {
              "type": "keyword",
              "ignore_above": 256
            },
            "type": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        }
      }
    }
  }
}
