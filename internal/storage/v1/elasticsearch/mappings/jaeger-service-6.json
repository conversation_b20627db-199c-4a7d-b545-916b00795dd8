{
  "template": "*jaeger-service-*",
  "settings":{
    "index.number_of_shards": {{ .Shards }},
    "index.number_of_replicas": {{ .Replicas }},
    "index.mapping.nested_fields.limit":50,
    "index.requests.cache.enable":true,
    "index.mapper.dynamic":false
  },
  "mappings":{
    "_default_":{
      "_all":{
        "enabled":false
      },
      "dynamic_templates":[
        {
          "span_tags_map":{
            "mapping":{
              "type":"keyword",
              "ignore_above":256
            },
            "path_match":"tag.*"
          }
        },
        {
          "process_tags_map":{
            "mapping":{
              "type":"keyword",
              "ignore_above":256
            },
            "path_match":"process.tag.*"
          }
        }
      ]
    },
    "service":{
      "properties":{
        "serviceName":{
          "type":"keyword",
          "ignore_above":256
        },
        "operationName":{
          "type":"keyword",
          "ignore_above":256
        }
      }
    }
  }
}
