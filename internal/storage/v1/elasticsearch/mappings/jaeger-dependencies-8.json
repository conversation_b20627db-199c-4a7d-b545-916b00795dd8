{
  "priority": {{ .Priority }},
  "index_patterns": "{{ .IndexPrefix }}jaeger-dependencies-*",
  "template": {
    {{- if .UseILM }}
    "aliases": {
      "{{ .IndexPrefix }}jaeger-dependencies-read": {}
    },
    {{- end }}
    "settings": {
      "index.number_of_shards": {{ .Shards }},
      "index.number_of_replicas": {{ .Replicas }},
      "index.mapping.nested_fields.limit": 50,
      "index.requests.cache.enable": true
      {{- if .UseILM }},
      "lifecycle": {
        "name": "{{ .ILMPolicyName }}",
        "rollover_alias": "{{ .IndexPrefix }}jaeger-dependencies-write"
      }
      {{- end }}
    },
    "mappings": {}
  }
}
