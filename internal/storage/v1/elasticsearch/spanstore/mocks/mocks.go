// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify
// Copyright (c) The Jaeger Authors.
// SPDX-License-Identifier: Apache-2.0
//
// Run 'make generate-mocks' to regenerate.

package mocks

import (
	"context"
	"time"

	"github.com/jaegertracing/jaeger/internal/storage/elasticsearch/dbmodel"
	mock "github.com/stretchr/testify/mock"
)

// NewCoreSpanReader creates a new instance of CoreSpanReader. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCoreSpanReader(t interface {
	mock.TestingT
	Cleanup(func())
}) *CoreSpanReader {
	mock := &CoreSpanReader{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// CoreSpanReader is an autogenerated mock type for the CoreSpanReader type
type CoreSpanReader struct {
	mock.Mock
}

type CoreSpanReader_Expecter struct {
	mock *mock.Mock
}

func (_m *CoreSpanReader) EXPECT() *CoreSpanReader_Expecter {
	return &CoreSpanReader_Expecter{mock: &_m.Mock}
}

// FindTraceIDs provides a mock function for the type CoreSpanReader
func (_mock *CoreSpanReader) FindTraceIDs(ctx context.Context, traceQuery dbmodel.TraceQueryParameters) ([]dbmodel.TraceID, error) {
	ret := _mock.Called(ctx, traceQuery)

	if len(ret) == 0 {
		panic("no return value specified for FindTraceIDs")
	}

	var r0 []dbmodel.TraceID
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, dbmodel.TraceQueryParameters) ([]dbmodel.TraceID, error)); ok {
		return returnFunc(ctx, traceQuery)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, dbmodel.TraceQueryParameters) []dbmodel.TraceID); ok {
		r0 = returnFunc(ctx, traceQuery)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dbmodel.TraceID)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, dbmodel.TraceQueryParameters) error); ok {
		r1 = returnFunc(ctx, traceQuery)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// CoreSpanReader_FindTraceIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTraceIDs'
type CoreSpanReader_FindTraceIDs_Call struct {
	*mock.Call
}

// FindTraceIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - traceQuery dbmodel.TraceQueryParameters
func (_e *CoreSpanReader_Expecter) FindTraceIDs(ctx interface{}, traceQuery interface{}) *CoreSpanReader_FindTraceIDs_Call {
	return &CoreSpanReader_FindTraceIDs_Call{Call: _e.mock.On("FindTraceIDs", ctx, traceQuery)}
}

func (_c *CoreSpanReader_FindTraceIDs_Call) Run(run func(ctx context.Context, traceQuery dbmodel.TraceQueryParameters)) *CoreSpanReader_FindTraceIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 dbmodel.TraceQueryParameters
		if args[1] != nil {
			arg1 = args[1].(dbmodel.TraceQueryParameters)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *CoreSpanReader_FindTraceIDs_Call) Return(traceIDs []dbmodel.TraceID, err error) *CoreSpanReader_FindTraceIDs_Call {
	_c.Call.Return(traceIDs, err)
	return _c
}

func (_c *CoreSpanReader_FindTraceIDs_Call) RunAndReturn(run func(ctx context.Context, traceQuery dbmodel.TraceQueryParameters) ([]dbmodel.TraceID, error)) *CoreSpanReader_FindTraceIDs_Call {
	_c.Call.Return(run)
	return _c
}

// FindTraces provides a mock function for the type CoreSpanReader
func (_mock *CoreSpanReader) FindTraces(ctx context.Context, traceQuery dbmodel.TraceQueryParameters) ([]dbmodel.Trace, error) {
	ret := _mock.Called(ctx, traceQuery)

	if len(ret) == 0 {
		panic("no return value specified for FindTraces")
	}

	var r0 []dbmodel.Trace
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, dbmodel.TraceQueryParameters) ([]dbmodel.Trace, error)); ok {
		return returnFunc(ctx, traceQuery)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, dbmodel.TraceQueryParameters) []dbmodel.Trace); ok {
		r0 = returnFunc(ctx, traceQuery)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dbmodel.Trace)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, dbmodel.TraceQueryParameters) error); ok {
		r1 = returnFunc(ctx, traceQuery)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// CoreSpanReader_FindTraces_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTraces'
type CoreSpanReader_FindTraces_Call struct {
	*mock.Call
}

// FindTraces is a helper method to define mock.On call
//   - ctx context.Context
//   - traceQuery dbmodel.TraceQueryParameters
func (_e *CoreSpanReader_Expecter) FindTraces(ctx interface{}, traceQuery interface{}) *CoreSpanReader_FindTraces_Call {
	return &CoreSpanReader_FindTraces_Call{Call: _e.mock.On("FindTraces", ctx, traceQuery)}
}

func (_c *CoreSpanReader_FindTraces_Call) Run(run func(ctx context.Context, traceQuery dbmodel.TraceQueryParameters)) *CoreSpanReader_FindTraces_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 dbmodel.TraceQueryParameters
		if args[1] != nil {
			arg1 = args[1].(dbmodel.TraceQueryParameters)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *CoreSpanReader_FindTraces_Call) Return(traces []dbmodel.Trace, err error) *CoreSpanReader_FindTraces_Call {
	_c.Call.Return(traces, err)
	return _c
}

func (_c *CoreSpanReader_FindTraces_Call) RunAndReturn(run func(ctx context.Context, traceQuery dbmodel.TraceQueryParameters) ([]dbmodel.Trace, error)) *CoreSpanReader_FindTraces_Call {
	_c.Call.Return(run)
	return _c
}

// GetOperations provides a mock function for the type CoreSpanReader
func (_mock *CoreSpanReader) GetOperations(ctx context.Context, query dbmodel.OperationQueryParameters) ([]dbmodel.Operation, error) {
	ret := _mock.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for GetOperations")
	}

	var r0 []dbmodel.Operation
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, dbmodel.OperationQueryParameters) ([]dbmodel.Operation, error)); ok {
		return returnFunc(ctx, query)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, dbmodel.OperationQueryParameters) []dbmodel.Operation); ok {
		r0 = returnFunc(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dbmodel.Operation)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, dbmodel.OperationQueryParameters) error); ok {
		r1 = returnFunc(ctx, query)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// CoreSpanReader_GetOperations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOperations'
type CoreSpanReader_GetOperations_Call struct {
	*mock.Call
}

// GetOperations is a helper method to define mock.On call
//   - ctx context.Context
//   - query dbmodel.OperationQueryParameters
func (_e *CoreSpanReader_Expecter) GetOperations(ctx interface{}, query interface{}) *CoreSpanReader_GetOperations_Call {
	return &CoreSpanReader_GetOperations_Call{Call: _e.mock.On("GetOperations", ctx, query)}
}

func (_c *CoreSpanReader_GetOperations_Call) Run(run func(ctx context.Context, query dbmodel.OperationQueryParameters)) *CoreSpanReader_GetOperations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 dbmodel.OperationQueryParameters
		if args[1] != nil {
			arg1 = args[1].(dbmodel.OperationQueryParameters)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *CoreSpanReader_GetOperations_Call) Return(operations []dbmodel.Operation, err error) *CoreSpanReader_GetOperations_Call {
	_c.Call.Return(operations, err)
	return _c
}

func (_c *CoreSpanReader_GetOperations_Call) RunAndReturn(run func(ctx context.Context, query dbmodel.OperationQueryParameters) ([]dbmodel.Operation, error)) *CoreSpanReader_GetOperations_Call {
	_c.Call.Return(run)
	return _c
}

// GetServices provides a mock function for the type CoreSpanReader
func (_mock *CoreSpanReader) GetServices(ctx context.Context) ([]string, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetServices")
	}

	var r0 []string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]string, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []string); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// CoreSpanReader_GetServices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetServices'
type CoreSpanReader_GetServices_Call struct {
	*mock.Call
}

// GetServices is a helper method to define mock.On call
//   - ctx context.Context
func (_e *CoreSpanReader_Expecter) GetServices(ctx interface{}) *CoreSpanReader_GetServices_Call {
	return &CoreSpanReader_GetServices_Call{Call: _e.mock.On("GetServices", ctx)}
}

func (_c *CoreSpanReader_GetServices_Call) Run(run func(ctx context.Context)) *CoreSpanReader_GetServices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *CoreSpanReader_GetServices_Call) Return(strings []string, err error) *CoreSpanReader_GetServices_Call {
	_c.Call.Return(strings, err)
	return _c
}

func (_c *CoreSpanReader_GetServices_Call) RunAndReturn(run func(ctx context.Context) ([]string, error)) *CoreSpanReader_GetServices_Call {
	_c.Call.Return(run)
	return _c
}

// GetTraces provides a mock function for the type CoreSpanReader
func (_mock *CoreSpanReader) GetTraces(ctx context.Context, query []dbmodel.TraceID) ([]dbmodel.Trace, error) {
	ret := _mock.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for GetTraces")
	}

	var r0 []dbmodel.Trace
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, []dbmodel.TraceID) ([]dbmodel.Trace, error)); ok {
		return returnFunc(ctx, query)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, []dbmodel.TraceID) []dbmodel.Trace); ok {
		r0 = returnFunc(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dbmodel.Trace)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, []dbmodel.TraceID) error); ok {
		r1 = returnFunc(ctx, query)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// CoreSpanReader_GetTraces_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTraces'
type CoreSpanReader_GetTraces_Call struct {
	*mock.Call
}

// GetTraces is a helper method to define mock.On call
//   - ctx context.Context
//   - query []dbmodel.TraceID
func (_e *CoreSpanReader_Expecter) GetTraces(ctx interface{}, query interface{}) *CoreSpanReader_GetTraces_Call {
	return &CoreSpanReader_GetTraces_Call{Call: _e.mock.On("GetTraces", ctx, query)}
}

func (_c *CoreSpanReader_GetTraces_Call) Run(run func(ctx context.Context, query []dbmodel.TraceID)) *CoreSpanReader_GetTraces_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []dbmodel.TraceID
		if args[1] != nil {
			arg1 = args[1].([]dbmodel.TraceID)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *CoreSpanReader_GetTraces_Call) Return(traces []dbmodel.Trace, err error) *CoreSpanReader_GetTraces_Call {
	_c.Call.Return(traces, err)
	return _c
}

func (_c *CoreSpanReader_GetTraces_Call) RunAndReturn(run func(ctx context.Context, query []dbmodel.TraceID) ([]dbmodel.Trace, error)) *CoreSpanReader_GetTraces_Call {
	_c.Call.Return(run)
	return _c
}

// NewCoreSpanWriter creates a new instance of CoreSpanWriter. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCoreSpanWriter(t interface {
	mock.TestingT
	Cleanup(func())
}) *CoreSpanWriter {
	mock := &CoreSpanWriter{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// CoreSpanWriter is an autogenerated mock type for the CoreSpanWriter type
type CoreSpanWriter struct {
	mock.Mock
}

type CoreSpanWriter_Expecter struct {
	mock *mock.Mock
}

func (_m *CoreSpanWriter) EXPECT() *CoreSpanWriter_Expecter {
	return &CoreSpanWriter_Expecter{mock: &_m.Mock}
}

// Close provides a mock function for the type CoreSpanWriter
func (_mock *CoreSpanWriter) Close() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// CoreSpanWriter_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type CoreSpanWriter_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *CoreSpanWriter_Expecter) Close() *CoreSpanWriter_Close_Call {
	return &CoreSpanWriter_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *CoreSpanWriter_Close_Call) Run(run func()) *CoreSpanWriter_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *CoreSpanWriter_Close_Call) Return(err error) *CoreSpanWriter_Close_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *CoreSpanWriter_Close_Call) RunAndReturn(run func() error) *CoreSpanWriter_Close_Call {
	_c.Call.Return(run)
	return _c
}

// WriteSpan provides a mock function for the type CoreSpanWriter
func (_mock *CoreSpanWriter) WriteSpan(spanStartTime time.Time, span *dbmodel.Span) {
	_mock.Called(spanStartTime, span)
	return
}

// CoreSpanWriter_WriteSpan_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WriteSpan'
type CoreSpanWriter_WriteSpan_Call struct {
	*mock.Call
}

// WriteSpan is a helper method to define mock.On call
//   - spanStartTime time.Time
//   - span *dbmodel.Span
func (_e *CoreSpanWriter_Expecter) WriteSpan(spanStartTime interface{}, span interface{}) *CoreSpanWriter_WriteSpan_Call {
	return &CoreSpanWriter_WriteSpan_Call{Call: _e.mock.On("WriteSpan", spanStartTime, span)}
}

func (_c *CoreSpanWriter_WriteSpan_Call) Run(run func(spanStartTime time.Time, span *dbmodel.Span)) *CoreSpanWriter_WriteSpan_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 time.Time
		if args[0] != nil {
			arg0 = args[0].(time.Time)
		}
		var arg1 *dbmodel.Span
		if args[1] != nil {
			arg1 = args[1].(*dbmodel.Span)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *CoreSpanWriter_WriteSpan_Call) Return() *CoreSpanWriter_WriteSpan_Call {
	_c.Call.Return()
	return _c
}

func (_c *CoreSpanWriter_WriteSpan_Call) RunAndReturn(run func(spanStartTime time.Time, span *dbmodel.Span)) *CoreSpanWriter_WriteSpan_Call {
	_c.Run(run)
	return _c
}
