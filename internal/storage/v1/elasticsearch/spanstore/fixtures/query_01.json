{"bool": {"should": [{"bool": {"must": {"regexp": {"tag.bat@foo": {"value": "spook"}}}}}, {"bool": {"must": {"regexp": {"process.tag.bat@foo": {"value": "spook"}}}}}, {"nested": {"path": "tags", "query": {"bool": {"must": [{"match": {"tags.key": {"query": "bat.foo"}}}, {"regexp": {"tags.value": {"value": "spook"}}}]}}}}, {"nested": {"path": "process.tags", "query": {"bool": {"must": [{"match": {"process.tags.key": {"query": "bat.foo"}}}, {"regexp": {"process.tags.value": {"value": "spook"}}}]}}}}, {"nested": {"path": "logs.fields", "query": {"bool": {"must": [{"match": {"logs.fields.key": {"query": "bat.foo"}}}, {"regexp": {"logs.fields.value": {"value": "spook"}}}]}}}}]}}