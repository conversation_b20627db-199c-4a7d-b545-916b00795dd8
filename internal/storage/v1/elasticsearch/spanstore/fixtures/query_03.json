{"bool": {"should": [{"bool": {"must": {"regexp": {"tag.bat@foo": {"value": "spo\\*"}}}}}, {"bool": {"must": {"regexp": {"process.tag.bat@foo": {"value": "spo\\*"}}}}}, {"nested": {"path": "tags", "query": {"bool": {"must": [{"match": {"tags.key": {"query": "bat.foo"}}}, {"regexp": {"tags.value": {"value": "spo\\*"}}}]}}}}, {"nested": {"path": "process.tags", "query": {"bool": {"must": [{"match": {"process.tags.key": {"query": "bat.foo"}}}, {"regexp": {"process.tags.value": {"value": "spo\\*"}}}]}}}}, {"nested": {"path": "logs.fields", "query": {"bool": {"must": [{"match": {"logs.fields.key": {"query": "bat.foo"}}}, {"regexp": {"logs.fields.value": {"value": "spo\\*"}}}]}}}}]}}