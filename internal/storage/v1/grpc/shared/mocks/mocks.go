// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify
// Copyright (c) The Jaeger Authors.
// SPDX-License-Identifier: Apache-2.0
//
// Run 'make generate-mocks' to regenerate.

package mocks

import (
	"github.com/jaegertracing/jaeger/internal/storage/v1/grpc/shared"
	mock "github.com/stretchr/testify/mock"
)

// NewPluginCapabilities creates a new instance of PluginCapabilities. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPluginCapabilities(t interface {
	mock.TestingT
	Cleanup(func())
}) *PluginCapabilities {
	mock := &PluginCapabilities{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// PluginCapabilities is an autogenerated mock type for the PluginCapabilities type
type PluginCapabilities struct {
	mock.Mock
}

type PluginCapabilities_Expecter struct {
	mock *mock.Mock
}

func (_m *PluginCapabilities) EXPECT() *PluginCapabilities_Expecter {
	return &PluginCapabilities_Expecter{mock: &_m.Mock}
}

// Capabilities provides a mock function for the type PluginCapabilities
func (_mock *PluginCapabilities) Capabilities() (*shared.Capabilities, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Capabilities")
	}

	var r0 *shared.Capabilities
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (*shared.Capabilities, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() *shared.Capabilities); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*shared.Capabilities)
		}
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// PluginCapabilities_Capabilities_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Capabilities'
type PluginCapabilities_Capabilities_Call struct {
	*mock.Call
}

// Capabilities is a helper method to define mock.On call
func (_e *PluginCapabilities_Expecter) Capabilities() *PluginCapabilities_Capabilities_Call {
	return &PluginCapabilities_Capabilities_Call{Call: _e.mock.On("Capabilities")}
}

func (_c *PluginCapabilities_Capabilities_Call) Run(run func()) *PluginCapabilities_Capabilities_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *PluginCapabilities_Capabilities_Call) Return(capabilities *shared.Capabilities, err error) *PluginCapabilities_Capabilities_Call {
	_c.Call.Return(capabilities, err)
	return _c
}

func (_c *PluginCapabilities_Capabilities_Call) RunAndReturn(run func() (*shared.Capabilities, error)) *PluginCapabilities_Capabilities_Call {
	_c.Call.Return(run)
	return _c
}
