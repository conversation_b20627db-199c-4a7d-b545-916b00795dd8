// Copyright (c) 2019 The Jaeger Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package jaeger.storage.v1;

option go_package = "storage_v1";

import "gogoproto/gogo.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";

import "model.proto";

// Enable gogoprotobuf extensions (https://github.com/gogo/protobuf/blob/master/extensions.md).
// Enable custom Marshal method.
option (gogoproto.marshaler_all) = true;
// Enable custom Unmarshal method.
option (gogoproto.unmarshaler_all) = true;
// Enable custom Size method (Required by Marshal and Unmarshal).
option (gogoproto.sizer_all) = true;

message GetDependenciesRequest {
    google.protobuf.Timestamp start_time = 1 [
      (gogoproto.stdtime) = true,
      (gogoproto.nullable) = false
    ];
    google.protobuf.Timestamp end_time = 2 [
      (gogoproto.stdtime) = true,
      (gogoproto.nullable) = false
    ];
}

message GetDependenciesResponse {
    repeated jaeger.api_v2.DependencyLink dependencies = 1 [
      (gogoproto.nullable) = false
    ];
}

message WriteSpanRequest {
    jaeger.api_v2.Span span = 1;
}

// empty; extensible in the future
message WriteSpanResponse {

}

// empty; extensible in the future
message CloseWriterRequest {
}

// empty; extensible in the future
message CloseWriterResponse {
}

message GetTraceRequest {
    bytes trace_id = 1 [
      (gogoproto.nullable) = false,
      (gogoproto.customtype) = "github.com/jaegertracing/jaeger-idl/model/v1.TraceID",
      (gogoproto.customname) = "TraceID"
    ];
    google.protobuf.Timestamp start_time = 2 [
      (gogoproto.stdtime) = true,
      (gogoproto.nullable) = false
    ];
    google.protobuf.Timestamp end_time = 3 [
      (gogoproto.stdtime) = true,
      (gogoproto.nullable) = false
    ];
}

message GetServicesRequest {}

message GetServicesResponse {
    repeated string services = 1;
}

message GetOperationsRequest {
    string service = 1;
    string span_kind = 2;
}

message Operation {
    string name = 1;
    string span_kind = 2;
}

message GetOperationsResponse {
    repeated string operationNames = 1; // deprecated
    repeated Operation operations = 2;
}

message TraceQueryParameters {
    string service_name = 1;
    string operation_name = 2;
    map<string, string> tags = 3;
    google.protobuf.Timestamp start_time_min = 4 [
      (gogoproto.stdtime) = true,
      (gogoproto.nullable) = false
    ];
    google.protobuf.Timestamp start_time_max = 5 [
      (gogoproto.stdtime) = true,
      (gogoproto.nullable) = false
    ];
    google.protobuf.Duration duration_min = 6 [
      (gogoproto.stdduration) = true,
      (gogoproto.nullable) = false
    ];
    google.protobuf.Duration duration_max = 7 [
      (gogoproto.stdduration) = true,
      (gogoproto.nullable) = false
    ];
    int32 num_traces = 8;
}

message FindTracesRequest {
    TraceQueryParameters query = 1;
}

message SpansResponseChunk {
    repeated jaeger.api_v2.Span spans = 1  [
      (gogoproto.nullable) = false
    ];
}

message FindTraceIDsRequest {
    TraceQueryParameters query = 1;
}

message FindTraceIDsResponse {
    repeated bytes trace_ids = 1 [
      (gogoproto.nullable) = false,
      (gogoproto.customtype) = "github.com/jaegertracing/jaeger-idl/model/v1.TraceID",
      (gogoproto.customname) = "TraceIDs"
    ];
}

service SpanWriterPlugin {
    // spanstore/Writer
    rpc WriteSpan(WriteSpanRequest) returns (WriteSpanResponse);
    rpc Close(CloseWriterRequest) returns (CloseWriterResponse);
}

service StreamingSpanWriterPlugin {
    rpc WriteSpanStream(stream WriteSpanRequest) returns (WriteSpanResponse);
}

service SpanReaderPlugin {
    // spanstore/Reader
    rpc GetTrace(GetTraceRequest) returns (stream SpansResponseChunk);
    rpc GetServices(GetServicesRequest) returns (GetServicesResponse);
    rpc GetOperations(GetOperationsRequest) returns (GetOperationsResponse);
    rpc FindTraces(FindTracesRequest) returns (stream SpansResponseChunk);
    rpc FindTraceIDs(FindTraceIDsRequest) returns (FindTraceIDsResponse);
}

// This plugin is deprecated and is not used anymore.
service ArchiveSpanWriterPlugin {
    // spanstore/Writer
    rpc WriteArchiveSpan(WriteSpanRequest) returns (WriteSpanResponse);
}

// This plugin is deprecated and is not used anymore.
service ArchiveSpanReaderPlugin {
    // spanstore/Reader
    rpc GetArchiveTrace(GetTraceRequest) returns (stream SpansResponseChunk);
}

service DependenciesReaderPlugin {
    // dependencystore/Reader
    rpc GetDependencies(GetDependenciesRequest) returns (GetDependenciesResponse);
}

// empty; extensible in the future
message CapabilitiesRequest {

}

message CapabilitiesResponse {
    bool archiveSpanReader = 1 [deprecated = true];
    bool archiveSpanWriter = 2 [deprecated = true];
    bool streamingSpanWriter = 3;
}

service PluginCapabilities {
    rpc Capabilities(CapabilitiesRequest) returns (CapabilitiesResponse);
}