// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify
// Copyright (c) The Jaeger Authors.
// SPDX-License-Identifier: Apache-2.0
//
// Run 'make generate-mocks' to regenerate.

package mocks

import (
	"context"
	"iter"

	"github.com/jaegertracing/jaeger/internal/storage/v2/api/tracestore"
	mock "github.com/stretchr/testify/mock"
	"go.opentelemetry.io/collector/pdata/ptrace"
)

// NewFactory creates a new instance of Factory. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewFactory(t interface {
	mock.TestingT
	Cleanup(func())
}) *Factory {
	mock := &Factory{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// Factory is an autogenerated mock type for the Factory type
type Factory struct {
	mock.Mock
}

type Factory_Expecter struct {
	mock *mock.Mock
}

func (_m *Factory) EXPECT() *Factory_Expecter {
	return &Factory_Expecter{mock: &_m.Mock}
}

// CreateTraceReader provides a mock function for the type Factory
func (_mock *Factory) CreateTraceReader() (tracestore.Reader, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for CreateTraceReader")
	}

	var r0 tracestore.Reader
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (tracestore.Reader, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() tracestore.Reader); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(tracestore.Reader)
		}
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Factory_CreateTraceReader_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateTraceReader'
type Factory_CreateTraceReader_Call struct {
	*mock.Call
}

// CreateTraceReader is a helper method to define mock.On call
func (_e *Factory_Expecter) CreateTraceReader() *Factory_CreateTraceReader_Call {
	return &Factory_CreateTraceReader_Call{Call: _e.mock.On("CreateTraceReader")}
}

func (_c *Factory_CreateTraceReader_Call) Run(run func()) *Factory_CreateTraceReader_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Factory_CreateTraceReader_Call) Return(reader tracestore.Reader, err error) *Factory_CreateTraceReader_Call {
	_c.Call.Return(reader, err)
	return _c
}

func (_c *Factory_CreateTraceReader_Call) RunAndReturn(run func() (tracestore.Reader, error)) *Factory_CreateTraceReader_Call {
	_c.Call.Return(run)
	return _c
}

// CreateTraceWriter provides a mock function for the type Factory
func (_mock *Factory) CreateTraceWriter() (tracestore.Writer, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for CreateTraceWriter")
	}

	var r0 tracestore.Writer
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (tracestore.Writer, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() tracestore.Writer); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(tracestore.Writer)
		}
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Factory_CreateTraceWriter_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateTraceWriter'
type Factory_CreateTraceWriter_Call struct {
	*mock.Call
}

// CreateTraceWriter is a helper method to define mock.On call
func (_e *Factory_Expecter) CreateTraceWriter() *Factory_CreateTraceWriter_Call {
	return &Factory_CreateTraceWriter_Call{Call: _e.mock.On("CreateTraceWriter")}
}

func (_c *Factory_CreateTraceWriter_Call) Run(run func()) *Factory_CreateTraceWriter_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Factory_CreateTraceWriter_Call) Return(writer tracestore.Writer, err error) *Factory_CreateTraceWriter_Call {
	_c.Call.Return(writer, err)
	return _c
}

func (_c *Factory_CreateTraceWriter_Call) RunAndReturn(run func() (tracestore.Writer, error)) *Factory_CreateTraceWriter_Call {
	_c.Call.Return(run)
	return _c
}

// NewReader creates a new instance of Reader. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewReader(t interface {
	mock.TestingT
	Cleanup(func())
}) *Reader {
	mock := &Reader{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// Reader is an autogenerated mock type for the Reader type
type Reader struct {
	mock.Mock
}

type Reader_Expecter struct {
	mock *mock.Mock
}

func (_m *Reader) EXPECT() *Reader_Expecter {
	return &Reader_Expecter{mock: &_m.Mock}
}

// FindTraceIDs provides a mock function for the type Reader
func (_mock *Reader) FindTraceIDs(ctx context.Context, query tracestore.TraceQueryParams) iter.Seq2[[]tracestore.FoundTraceID, error] {
	ret := _mock.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for FindTraceIDs")
	}

	var r0 iter.Seq2[[]tracestore.FoundTraceID, error]
	if returnFunc, ok := ret.Get(0).(func(context.Context, tracestore.TraceQueryParams) iter.Seq2[[]tracestore.FoundTraceID, error]); ok {
		r0 = returnFunc(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(iter.Seq2[[]tracestore.FoundTraceID, error])
		}
	}
	return r0
}

// Reader_FindTraceIDs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTraceIDs'
type Reader_FindTraceIDs_Call struct {
	*mock.Call
}

// FindTraceIDs is a helper method to define mock.On call
//   - ctx context.Context
//   - query tracestore.TraceQueryParams
func (_e *Reader_Expecter) FindTraceIDs(ctx interface{}, query interface{}) *Reader_FindTraceIDs_Call {
	return &Reader_FindTraceIDs_Call{Call: _e.mock.On("FindTraceIDs", ctx, query)}
}

func (_c *Reader_FindTraceIDs_Call) Run(run func(ctx context.Context, query tracestore.TraceQueryParams)) *Reader_FindTraceIDs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 tracestore.TraceQueryParams
		if args[1] != nil {
			arg1 = args[1].(tracestore.TraceQueryParams)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *Reader_FindTraceIDs_Call) Return(seq2 iter.Seq2[[]tracestore.FoundTraceID, error]) *Reader_FindTraceIDs_Call {
	_c.Call.Return(seq2)
	return _c
}

func (_c *Reader_FindTraceIDs_Call) RunAndReturn(run func(ctx context.Context, query tracestore.TraceQueryParams) iter.Seq2[[]tracestore.FoundTraceID, error]) *Reader_FindTraceIDs_Call {
	_c.Call.Return(run)
	return _c
}

// FindTraces provides a mock function for the type Reader
func (_mock *Reader) FindTraces(ctx context.Context, query tracestore.TraceQueryParams) iter.Seq2[[]ptrace.Traces, error] {
	ret := _mock.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for FindTraces")
	}

	var r0 iter.Seq2[[]ptrace.Traces, error]
	if returnFunc, ok := ret.Get(0).(func(context.Context, tracestore.TraceQueryParams) iter.Seq2[[]ptrace.Traces, error]); ok {
		r0 = returnFunc(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(iter.Seq2[[]ptrace.Traces, error])
		}
	}
	return r0
}

// Reader_FindTraces_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindTraces'
type Reader_FindTraces_Call struct {
	*mock.Call
}

// FindTraces is a helper method to define mock.On call
//   - ctx context.Context
//   - query tracestore.TraceQueryParams
func (_e *Reader_Expecter) FindTraces(ctx interface{}, query interface{}) *Reader_FindTraces_Call {
	return &Reader_FindTraces_Call{Call: _e.mock.On("FindTraces", ctx, query)}
}

func (_c *Reader_FindTraces_Call) Run(run func(ctx context.Context, query tracestore.TraceQueryParams)) *Reader_FindTraces_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 tracestore.TraceQueryParams
		if args[1] != nil {
			arg1 = args[1].(tracestore.TraceQueryParams)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *Reader_FindTraces_Call) Return(seq2 iter.Seq2[[]ptrace.Traces, error]) *Reader_FindTraces_Call {
	_c.Call.Return(seq2)
	return _c
}

func (_c *Reader_FindTraces_Call) RunAndReturn(run func(ctx context.Context, query tracestore.TraceQueryParams) iter.Seq2[[]ptrace.Traces, error]) *Reader_FindTraces_Call {
	_c.Call.Return(run)
	return _c
}

// GetOperations provides a mock function for the type Reader
func (_mock *Reader) GetOperations(ctx context.Context, query tracestore.OperationQueryParams) ([]tracestore.Operation, error) {
	ret := _mock.Called(ctx, query)

	if len(ret) == 0 {
		panic("no return value specified for GetOperations")
	}

	var r0 []tracestore.Operation
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, tracestore.OperationQueryParams) ([]tracestore.Operation, error)); ok {
		return returnFunc(ctx, query)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, tracestore.OperationQueryParams) []tracestore.Operation); ok {
		r0 = returnFunc(ctx, query)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]tracestore.Operation)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, tracestore.OperationQueryParams) error); ok {
		r1 = returnFunc(ctx, query)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Reader_GetOperations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetOperations'
type Reader_GetOperations_Call struct {
	*mock.Call
}

// GetOperations is a helper method to define mock.On call
//   - ctx context.Context
//   - query tracestore.OperationQueryParams
func (_e *Reader_Expecter) GetOperations(ctx interface{}, query interface{}) *Reader_GetOperations_Call {
	return &Reader_GetOperations_Call{Call: _e.mock.On("GetOperations", ctx, query)}
}

func (_c *Reader_GetOperations_Call) Run(run func(ctx context.Context, query tracestore.OperationQueryParams)) *Reader_GetOperations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 tracestore.OperationQueryParams
		if args[1] != nil {
			arg1 = args[1].(tracestore.OperationQueryParams)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *Reader_GetOperations_Call) Return(operations []tracestore.Operation, err error) *Reader_GetOperations_Call {
	_c.Call.Return(operations, err)
	return _c
}

func (_c *Reader_GetOperations_Call) RunAndReturn(run func(ctx context.Context, query tracestore.OperationQueryParams) ([]tracestore.Operation, error)) *Reader_GetOperations_Call {
	_c.Call.Return(run)
	return _c
}

// GetServices provides a mock function for the type Reader
func (_mock *Reader) GetServices(ctx context.Context) ([]string, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetServices")
	}

	var r0 []string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) ([]string, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) []string); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// Reader_GetServices_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetServices'
type Reader_GetServices_Call struct {
	*mock.Call
}

// GetServices is a helper method to define mock.On call
//   - ctx context.Context
func (_e *Reader_Expecter) GetServices(ctx interface{}) *Reader_GetServices_Call {
	return &Reader_GetServices_Call{Call: _e.mock.On("GetServices", ctx)}
}

func (_c *Reader_GetServices_Call) Run(run func(ctx context.Context)) *Reader_GetServices_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *Reader_GetServices_Call) Return(strings []string, err error) *Reader_GetServices_Call {
	_c.Call.Return(strings, err)
	return _c
}

func (_c *Reader_GetServices_Call) RunAndReturn(run func(ctx context.Context) ([]string, error)) *Reader_GetServices_Call {
	_c.Call.Return(run)
	return _c
}

// GetTraces provides a mock function for the type Reader
func (_mock *Reader) GetTraces(ctx context.Context, traceIDs ...tracestore.GetTraceParams) iter.Seq2[[]ptrace.Traces, error] {
	var tmpRet mock.Arguments
	if len(traceIDs) > 0 {
		tmpRet = _mock.Called(ctx, traceIDs)
	} else {
		tmpRet = _mock.Called(ctx)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for GetTraces")
	}

	var r0 iter.Seq2[[]ptrace.Traces, error]
	if returnFunc, ok := ret.Get(0).(func(context.Context, ...tracestore.GetTraceParams) iter.Seq2[[]ptrace.Traces, error]); ok {
		r0 = returnFunc(ctx, traceIDs...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(iter.Seq2[[]ptrace.Traces, error])
		}
	}
	return r0
}

// Reader_GetTraces_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTraces'
type Reader_GetTraces_Call struct {
	*mock.Call
}

// GetTraces is a helper method to define mock.On call
//   - ctx context.Context
//   - traceIDs ...tracestore.GetTraceParams
func (_e *Reader_Expecter) GetTraces(ctx interface{}, traceIDs ...interface{}) *Reader_GetTraces_Call {
	return &Reader_GetTraces_Call{Call: _e.mock.On("GetTraces",
		append([]interface{}{ctx}, traceIDs...)...)}
}

func (_c *Reader_GetTraces_Call) Run(run func(ctx context.Context, traceIDs ...tracestore.GetTraceParams)) *Reader_GetTraces_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 []tracestore.GetTraceParams
		var variadicArgs []tracestore.GetTraceParams
		if len(args) > 1 {
			variadicArgs = args[1].([]tracestore.GetTraceParams)
		}
		arg1 = variadicArgs
		run(
			arg0,
			arg1...,
		)
	})
	return _c
}

func (_c *Reader_GetTraces_Call) Return(seq2 iter.Seq2[[]ptrace.Traces, error]) *Reader_GetTraces_Call {
	_c.Call.Return(seq2)
	return _c
}

func (_c *Reader_GetTraces_Call) RunAndReturn(run func(ctx context.Context, traceIDs ...tracestore.GetTraceParams) iter.Seq2[[]ptrace.Traces, error]) *Reader_GetTraces_Call {
	_c.Call.Return(run)
	return _c
}

// NewWriter creates a new instance of Writer. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewWriter(t interface {
	mock.TestingT
	Cleanup(func())
}) *Writer {
	mock := &Writer{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// Writer is an autogenerated mock type for the Writer type
type Writer struct {
	mock.Mock
}

type Writer_Expecter struct {
	mock *mock.Mock
}

func (_m *Writer) EXPECT() *Writer_Expecter {
	return &Writer_Expecter{mock: &_m.Mock}
}

// WriteTraces provides a mock function for the type Writer
func (_mock *Writer) WriteTraces(ctx context.Context, td ptrace.Traces) error {
	ret := _mock.Called(ctx, td)

	if len(ret) == 0 {
		panic("no return value specified for WriteTraces")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, ptrace.Traces) error); ok {
		r0 = returnFunc(ctx, td)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// Writer_WriteTraces_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WriteTraces'
type Writer_WriteTraces_Call struct {
	*mock.Call
}

// WriteTraces is a helper method to define mock.On call
//   - ctx context.Context
//   - td ptrace.Traces
func (_e *Writer_Expecter) WriteTraces(ctx interface{}, td interface{}) *Writer_WriteTraces_Call {
	return &Writer_WriteTraces_Call{Call: _e.mock.On("WriteTraces", ctx, td)}
}

func (_c *Writer_WriteTraces_Call) Run(run func(ctx context.Context, td ptrace.Traces)) *Writer_WriteTraces_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 ptrace.Traces
		if args[1] != nil {
			arg1 = args[1].(ptrace.Traces)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *Writer_WriteTraces_Call) Return(err error) *Writer_WriteTraces_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *Writer_WriteTraces_Call) RunAndReturn(run func(ctx context.Context, td ptrace.Traces) error) *Writer_WriteTraces_Call {
	_c.Call.Return(run)
	return _c
}
