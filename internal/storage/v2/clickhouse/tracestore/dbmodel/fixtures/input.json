[{"Name": "Timestamp", "Data": {"Data": [1703498029000000000], "Location": null, "Precision": 9, "PrecisionSet": true}}, {"Name": "TraceID", "Data": {"Values": ["01020300000000000000000000000000"]}}, {"Name": "SpanID", "Data": {"Values": ["0102030000000000"]}}, {"Name": "ParentSpanID", "Data": {"Values": ["0102040000000000"]}}, {"Name": "TraceState", "Data": {"Values": ["trace state"]}}, {"Name": "SpanName", "Data": {"Values": ["call db"]}}, {"Name": "SpanKind", "Data": {"Values": ["Internal"]}}, {"Name": "Duration", "Data": {"Data": [1703498089000000000], "Location": null, "Precision": 9, "PrecisionSet": true}}, {"Name": "StatusCode", "Data": {"Values": ["Error"]}}, {"Name": "StatusMessage", "Data": {"Values": ["error"]}}, {"Name": "SpanAttributesStrKey", "Data": {"Offsets": [1], "Data": {"Values": ["app.payment.id"]}}}, {"Name": "SpanAttributesStrValue", "Data": {"Offsets": [1], "Data": {"Values": ["123456789"]}}}, {"Name": "SpanAttributesBytesKey", "Data": {"Offsets": [1], "Data": {"Values": ["span.test.bytes.value"]}}}, {"Name": "SpanAttributesBytesValue", "Data": {"Offsets": [1], "Data": {"Values": ["AQIDBAUG"]}}}, {"Name": "SpanAttributesBoolKey", "Data": {"Offsets": [2], "Data": {"Values": ["app.payment.card_valid", "app.payment.charged"]}}}, {"Name": "SpanAttributesBoolValue", "Data": {"Offsets": [2], "Data": [true, true]}}, {"Name": "SpanAttributesDoubleKey", "Data": {"Offsets": [1], "Data": {"Values": ["app.payment.amount"]}}}, {"Name": "SpanAttributesDoubleValue", "Data": {"Offsets": [1], "Data": [99.99]}}, {"Name": "SpanAttributesIntKey", "Data": {"Offsets": [1], "Data": {"Values": ["app.payment.count"]}}}, {"Name": "SpanAttributesIntValue", "Data": {"Offsets": [1], "Data": [5]}}, {"Name": "ScopeName", "Data": {"Values": ["io.opentelemetry.contrib.clickhouse"]}}, {"Name": "ScopeVersion", "Data": {"Values": ["1.0.0"]}}, {"Name": "ScopeAttributesStrKey", "Data": {"Offsets": [2], "Data": {"Values": ["library.language", "library.version"]}}}, {"Name": "ScopeAttributesStrValue", "Data": {"Offsets": [2], "Data": {"Values": ["go", "v2.2.2"]}}}, {"Name": "ScopeAttributesBytesKey", "Data": {"Offsets": [1], "Data": {"Values": ["scope.test.bytes.value"]}}}, {"Name": "ScopeAttributesBytesValue", "Data": {"Offsets": [1], "Data": {"Values": ["AQIDBA=="]}}}, {"Name": "ScopeAttributesBoolKey", "Data": {"Offsets": [2], "Data": {"Values": ["library.feature.async_processing_enabled", "library.security.data_masking_active"]}}}, {"Name": "ScopeAttributesBoolValue", "Data": {"Offsets": [2], "Data": [true, false]}}, {"Name": "ScopeAttributesDoubleKey", "Data": {"Offsets": [1], "Data": {"Values": ["component.config.sampling.ratio"]}}}, {"Name": "ScopeAttributesDoubleValue", "Data": {"Offsets": [1], "Data": [0.75]}}, {"Name": "ScopeAttributesIntKey", "Data": {"Offsets": [2], "Data": {"Values": ["component.max_workers", "component.min_workers"]}}}, {"Name": "ScopeAttributesIntValue", "Data": {"Offsets": [2], "Data": [10, 2]}}, {"Name": "ResourceAttributesBoolKey", "Data": {"Offsets": [1], "Data": {"Values": ["browser.mobile"]}}}, {"Name": "ResourceAttributesBoolValue", "Data": {"Offsets": [1], "Data": [true]}}, {"Name": "ResourceAttributesDoubleKey", "Data": {"Offsets": [1], "Data": {"Values": ["host.memory.swap"]}}}, {"Name": "ResourceAttributesDoubleValue", "Data": {"Offsets": [1], "Data": [2048]}}, {"Name": "ResourceAttributesIntKey", "Data": {"Offsets": [3], "Data": {"Values": ["faas.max_memory", "process.parent.pid", "process.pid"]}}}, {"Name": "ResourceAttributesIntValue", "Data": {"Offsets": [3], "Data": [134217728, 111, 1234]}}, {"Name": "ResourceAttributesStrKey", "Data": {"Offsets": [2], "Data": {"Values": ["service.names", "service.instance.id"]}}}, {"Name": "ResourceAttributesStrValue", "Data": {"Offsets": [2], "Data": {"Values": ["clickhouse", "627cc493-f310-47de-96bd-71410b7dec09"]}}}, {"Name": "ResourceAttributesBytesKey", "Data": {"Offsets": [1], "Data": {"Values": ["oci.manifest.digest"]}}}, {"Name": "ResourceAttributesBytesValue", "Data": {"Offsets": [1], "Data": {"Values": ["c2hhMjU2OmU0Y2E2MmMwZDYyZjNlODg2ZTY4NDgwNmRmZTlkNGUwY2RhNjBkNTQ5ODY4OTgxNzNjMTA4Mzg1NmNmZGEwZjQ="]}}}, {"Name": "EventsName", "Data": {"Offsets": [1], "Data": {"Values": ["event1"]}}}, {"Name": "EventsTimestamp", "Data": {"Offsets": [1], "Data": {"Data": [1703498029000000000], "Location": null, "Precision": 9, "PrecisionSet": true}}}, {"Name": "EventAttributesBoolKey", "Data": {"Offsets": [1], "Data": {"Offsets": [2], "Data": {"Values": ["inventory.available", "payment.successful"]}}}}, {"Name": "EventAttributesBoolValue", "Data": {"Offsets": [1], "Data": {"Offsets": [2], "Data": [true, true]}}}, {"Name": "EventAttributesDoubleKey", "Data": {"Offsets": [1], "Data": {"Offsets": [2], "Data": {"Values": ["product.price", "order.discount.rate"]}}}}, {"Name": "EventAttributesDoubleValue", "Data": {"Offsets": [1], "Data": {"Offsets": [2], "Data": [6.04, 0.04]}}}, {"Name": "EventAttributesIntKey", "Data": {"Offsets": [1], "Data": {"Offsets": [1], "Data": {"Values": ["order.quantity"]}}}}, {"Name": "EventAttributesIntValue", "Data": {"Offsets": [1], "Data": {"Offsets": [1], "Data": [2]}}}, {"Name": "EventAttributesStrKey", "Data": {"Offsets": [1], "Data": {"Offsets": [2], "Data": {"Values": ["order.id", "product.id"]}}}}, {"Name": "EventAttributesStrValue", "Data": {"Offsets": [1], "Data": {"Offsets": [2], "Data": {"Values": ["123456789", "987654321"]}}}}, {"Name": "EventAttributesBytesKey", "Data": {"Offsets": [1], "Data": {"Offsets": [1], "Data": {"Values": ["event.test.bytes.value"]}}}}, {"Name": "EventAttributesBytesValue", "Data": {"Offsets": [1], "Data": {"Offsets": [1], "Data": {"Values": ["AQIDBAUG"]}}}}, {"Name": "LinksTraceId", "Data": {"Offsets": [1], "Data": {"Values": ["01020500000000000000000000000000"]}}}, {"Name": "LinksSpanId", "Data": {"Offsets": [1], "Data": {"Values": ["0102050000000000"]}}}, {"Name": "LinksTraceStatus", "Data": {"Offsets": [1], "Data": {"Values": ["test"]}}}, {"Name": "LinkAttributesBoolKey", "Data": {"Offsets": [1], "Data": {"Offsets": [1], "Data": {"Values": ["is.retry"]}}}}, {"Name": "LinkAttributesBoolValue", "Data": {"Offsets": [1], "Data": {"Offsets": [1], "Data": [true]}}}, {"Name": "LinkAttributesDoubleKey", "Data": {"Offsets": [1], "Data": {"Offsets": [1], "Data": {"Values": ["similarity.score"]}}}}, {"Name": "LinkAttributesDoubleValue", "Data": {"Offsets": [1], "Data": {"Offsets": [1], "Data": [0.85]}}}, {"Name": "LinkAttributesIntKey", "Data": {"Offsets": [1], "Data": {"Offsets": [1], "Data": {"Values": ["correlation.id"]}}}}, {"Name": "LinkAttributesIntValue", "Data": {"Offsets": [1], "Data": {"Offsets": [1], "Data": [1324141]}}}, {"Name": "LinkAttributesStrKey", "Data": {"Offsets": [1], "Data": {"Offsets": [1], "Data": {"Values": ["related.resource.id"]}}}}, {"Name": "LinkAttributesStrValue", "Data": {"Offsets": [1], "Data": {"Offsets": [1], "Data": {"Values": ["resource-123"]}}}}, {"Name": "LinkAttributesBytesKey", "Data": {"Offsets": [1], "Data": {"Offsets": [1], "Data": {"Values": ["link.test.bytes.value"]}}}}, {"Name": "LinkAttributesBytesValue", "Data": {"Offsets": [1], "Data": {"Offsets": [1], "Data": {"Values": ["AQIDBAUG"]}}}}]