{"resourceSpans": [{"resource": {"attributes": [{"key": "service.names", "value": {"stringValue": "clickhouse"}}, {"key": "service.instance.id", "value": {"stringValue": "627cc493-f310-47de-96bd-71410b7dec09"}}, {"key": "process.parent.pid", "value": {"intValue": "111"}}, {"key": "process.pid", "value": {"intValue": "1234"}}, {"key": "faas.max_memory", "value": {"intValue": "134217728"}}, {"key": "host.memory.swap", "value": {"doubleValue": 2048}}, {"key": "browser.mobile", "value": {"boolValue": true}}, {"key": "oci.manifest.digest", "value": {"bytesValue": "c2hhMjU2OmU0Y2E2MmMwZDYyZjNlODg2ZTY4NDgwNmRmZTlkNGUwY2RhNjBkNTQ5ODY4OTgxNzNjMTA4Mzg1NmNmZGEwZjQ="}}]}, "scopeSpans": [{"scope": {"name": "io.opentelemetry.contrib.clickhouse", "version": "1.0.0", "attributes": [{"key": "library.language", "value": {"stringValue": "go"}}, {"key": "library.version", "value": {"stringValue": "v2.2.2"}}, {"key": "library.feature.async_processing_enabled", "value": {"boolValue": true}}, {"key": "library.security.data_masking_active", "value": {"boolValue": false}}, {"key": "component.config.sampling.ratio", "value": {"doubleValue": 0.75}}, {"key": "component.max_workers", "value": {"intValue": "10"}}, {"key": "component.min_workers", "value": {"intValue": "2"}}, {"key": "scope.test.bytes.value", "value": {"bytesValue": "AQIDBA=="}}]}, "spans": [{"traceId": "01020300000000000000000000000000", "spanId": "0102030000000000", "traceState": "trace state", "parentSpanId": "0102040000000000", "name": "call db", "kind": 1, "startTimeUnixNano": "1703498029000000000", "endTimeUnixNano": "1703498089000000000", "attributes": [{"key": "app.payment.card_valid", "value": {"boolValue": true}}, {"key": "app.payment.charged", "value": {"boolValue": true}}, {"key": "app.payment.amount", "value": {"doubleValue": 99.99}}, {"key": "app.payment.count", "value": {"intValue": "5"}}, {"key": "app.payment.id", "value": {"stringValue": "123456789"}}, {"key": "span.test.bytes.value", "value": {"bytesValue": "AQIDBAUG"}}], "events": [{"timeUnixNano": "1703498029000000000", "name": "event1", "attributes": [{"key": "inventory.available", "value": {"boolValue": true}}, {"key": "payment.successful", "value": {"boolValue": true}}, {"key": "product.price", "value": {"doubleValue": 6.04}}, {"key": "order.discount.rate", "value": {"doubleValue": 0.04}}, {"key": "order.quantity", "value": {"intValue": "2"}}, {"key": "order.id", "value": {"stringValue": "123456789"}}, {"key": "product.id", "value": {"stringValue": "987654321"}}, {"key": "event.test.bytes.value", "value": {"bytesValue": "AQIDBAUG"}}]}], "links": [{"traceId": "01020500000000000000000000000000", "spanId": "0102050000000000", "traceState": "test", "attributes": [{"key": "is.retry", "value": {"boolValue": true}}, {"key": "similarity.score", "value": {"doubleValue": 0.85}}, {"key": "correlation.id", "value": {"intValue": "1324141"}}, {"key": "related.resource.id", "value": {"stringValue": "resource-123"}}, {"key": "link.test.bytes.value", "value": {"bytesValue": "AQIDBAUG"}}]}], "status": {"message": "error", "code": 2}}]}]}]}