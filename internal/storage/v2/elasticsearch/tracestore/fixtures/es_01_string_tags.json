{"traceID": "00000000000000010000000000000000", "spanID": "0000000000000002", "flags": 1, "operationName": "test-general-conversion", "references": [{"refType": "CHILD_OF", "traceID": "00000000000000010000000000000000", "spanID": "0000000000000003"}, {"refType": "FOLLOWS_FROM", "traceID": "00000000000000010000000000000000", "spanID": "0000000000000004"}, {"refType": "CHILD_OF", "traceID": "00000000000000ff0000000000000000", "spanID": "00000000000000ff"}], "startTime": 1485467191639875, "startTimeMillis": 1485467191639, "duration": 5, "tags": [{"key": "otel.scope.name", "type": "string", "value": "testing-library"}, {"key": "otel.scope.version", "type": "string", "value": "1.1.1"}, {"key": "peer.service", "type": "string", "value": "service-y"}, {"key": "peer.ipv4", "type": "int64", "value": "23456"}, {"key": "blob", "type": "binary", "value": "00003039"}, {"key": "temperature", "type": "float64", "value": "72.5"}, {"key": "error", "type": "bool", "value": "true"}], "logs": [{"timestamp": 1485467191639875, "fields": [{"key": "event", "type": "string", "value": "testing-event"}, {"key": "event-x", "type": "string", "value": "event-y"}]}, {"timestamp": 1485467191639875, "fields": [{"key": "x", "type": "string", "value": "y"}]}], "process": {"serviceName": "service-x", "tags": [{"key": "sdk.version", "type": "string", "value": "1.2.1"}]}}