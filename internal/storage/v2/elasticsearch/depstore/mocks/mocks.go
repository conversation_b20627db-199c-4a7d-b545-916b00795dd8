// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify
// Copyright (c) The Jaeger Authors.
// SPDX-License-Identifier: Apache-2.0
//
// Run 'make generate-mocks' to regenerate.

package mocks

import (
	"context"
	"time"

	"github.com/jaegertracing/jaeger/internal/storage/v2/elasticsearch/depstore/dbmodel"
	mock "github.com/stretchr/testify/mock"
)

// NewCoreDependencyStore creates a new instance of CoreDependencyStore. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCoreDependencyStore(t interface {
	mock.TestingT
	Cleanup(func())
}) *CoreDependencyStore {
	mock := &CoreDependencyStore{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// CoreDependencyStore is an autogenerated mock type for the CoreDependencyStore type
type CoreDependencyStore struct {
	mock.Mock
}

type CoreDependencyStore_Expecter struct {
	mock *mock.Mock
}

func (_m *CoreDependencyStore) EXPECT() *CoreDependencyStore_Expecter {
	return &CoreDependencyStore_Expecter{mock: &_m.Mock}
}

// CreateTemplates provides a mock function for the type CoreDependencyStore
func (_mock *CoreDependencyStore) CreateTemplates(dependenciesTemplate string) error {
	ret := _mock.Called(dependenciesTemplate)

	if len(ret) == 0 {
		panic("no return value specified for CreateTemplates")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string) error); ok {
		r0 = returnFunc(dependenciesTemplate)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// CoreDependencyStore_CreateTemplates_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateTemplates'
type CoreDependencyStore_CreateTemplates_Call struct {
	*mock.Call
}

// CreateTemplates is a helper method to define mock.On call
//   - dependenciesTemplate string
func (_e *CoreDependencyStore_Expecter) CreateTemplates(dependenciesTemplate interface{}) *CoreDependencyStore_CreateTemplates_Call {
	return &CoreDependencyStore_CreateTemplates_Call{Call: _e.mock.On("CreateTemplates", dependenciesTemplate)}
}

func (_c *CoreDependencyStore_CreateTemplates_Call) Run(run func(dependenciesTemplate string)) *CoreDependencyStore_CreateTemplates_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *CoreDependencyStore_CreateTemplates_Call) Return(err error) *CoreDependencyStore_CreateTemplates_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *CoreDependencyStore_CreateTemplates_Call) RunAndReturn(run func(dependenciesTemplate string) error) *CoreDependencyStore_CreateTemplates_Call {
	_c.Call.Return(run)
	return _c
}

// GetDependencies provides a mock function for the type CoreDependencyStore
func (_mock *CoreDependencyStore) GetDependencies(ctx context.Context, endTs time.Time, lookback time.Duration) ([]dbmodel.DependencyLink, error) {
	ret := _mock.Called(ctx, endTs, lookback)

	if len(ret) == 0 {
		panic("no return value specified for GetDependencies")
	}

	var r0 []dbmodel.DependencyLink
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, time.Time, time.Duration) ([]dbmodel.DependencyLink, error)); ok {
		return returnFunc(ctx, endTs, lookback)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, time.Time, time.Duration) []dbmodel.DependencyLink); ok {
		r0 = returnFunc(ctx, endTs, lookback)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]dbmodel.DependencyLink)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, time.Time, time.Duration) error); ok {
		r1 = returnFunc(ctx, endTs, lookback)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// CoreDependencyStore_GetDependencies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDependencies'
type CoreDependencyStore_GetDependencies_Call struct {
	*mock.Call
}

// GetDependencies is a helper method to define mock.On call
//   - ctx context.Context
//   - endTs time.Time
//   - lookback time.Duration
func (_e *CoreDependencyStore_Expecter) GetDependencies(ctx interface{}, endTs interface{}, lookback interface{}) *CoreDependencyStore_GetDependencies_Call {
	return &CoreDependencyStore_GetDependencies_Call{Call: _e.mock.On("GetDependencies", ctx, endTs, lookback)}
}

func (_c *CoreDependencyStore_GetDependencies_Call) Run(run func(ctx context.Context, endTs time.Time, lookback time.Duration)) *CoreDependencyStore_GetDependencies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 time.Time
		if args[1] != nil {
			arg1 = args[1].(time.Time)
		}
		var arg2 time.Duration
		if args[2] != nil {
			arg2 = args[2].(time.Duration)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *CoreDependencyStore_GetDependencies_Call) Return(dependencyLinks []dbmodel.DependencyLink, err error) *CoreDependencyStore_GetDependencies_Call {
	_c.Call.Return(dependencyLinks, err)
	return _c
}

func (_c *CoreDependencyStore_GetDependencies_Call) RunAndReturn(run func(ctx context.Context, endTs time.Time, lookback time.Duration) ([]dbmodel.DependencyLink, error)) *CoreDependencyStore_GetDependencies_Call {
	_c.Call.Return(run)
	return _c
}

// WriteDependencies provides a mock function for the type CoreDependencyStore
func (_mock *CoreDependencyStore) WriteDependencies(ts time.Time, dependencies []dbmodel.DependencyLink) error {
	ret := _mock.Called(ts, dependencies)

	if len(ret) == 0 {
		panic("no return value specified for WriteDependencies")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(time.Time, []dbmodel.DependencyLink) error); ok {
		r0 = returnFunc(ts, dependencies)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// CoreDependencyStore_WriteDependencies_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WriteDependencies'
type CoreDependencyStore_WriteDependencies_Call struct {
	*mock.Call
}

// WriteDependencies is a helper method to define mock.On call
//   - ts time.Time
//   - dependencies []dbmodel.DependencyLink
func (_e *CoreDependencyStore_Expecter) WriteDependencies(ts interface{}, dependencies interface{}) *CoreDependencyStore_WriteDependencies_Call {
	return &CoreDependencyStore_WriteDependencies_Call{Call: _e.mock.On("WriteDependencies", ts, dependencies)}
}

func (_c *CoreDependencyStore_WriteDependencies_Call) Run(run func(ts time.Time, dependencies []dbmodel.DependencyLink)) *CoreDependencyStore_WriteDependencies_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 time.Time
		if args[0] != nil {
			arg0 = args[0].(time.Time)
		}
		var arg1 []dbmodel.DependencyLink
		if args[1] != nil {
			arg1 = args[1].([]dbmodel.DependencyLink)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *CoreDependencyStore_WriteDependencies_Call) Return(err error) *CoreDependencyStore_WriteDependencies_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *CoreDependencyStore_WriteDependencies_Call) RunAndReturn(run func(ts time.Time, dependencies []dbmodel.DependencyLink) error) *CoreDependencyStore_WriteDependencies_Call {
	_c.Call.Return(run)
	return _c
}
