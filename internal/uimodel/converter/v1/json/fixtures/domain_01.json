{"spans": [{"traceId": "AAAAAAAAAAAAAAAAAAAAAQ==", "spanId": "AAAAAAAAAAI=", "operationName": "test-general-conversion", "startTime": "2017-01-26T16:46:31.639875139-05:00", "duration": "5000ns", "process": {"serviceName": "service-x"}, "logs": [{"timestamp": "2017-01-26T16:46:31.639875139-05:00", "fields": [{"key": "event", "vStr": "some-event"}]}, {"timestamp": "2017-01-26T16:46:31.639875139-05:00", "fields": [{"key": "x", "vStr": "y"}]}]}, {"traceId": "AAAAAAAAAAAAAAAAAAAAAQ==", "spanId": "AAAAAAAAAAI=", "operationName": "some-operation", "startTime": "2017-01-26T16:46:31.639875139-05:00", "duration": "5000ns", "tags": [{"key": "peer.service", "vType": "STRING", "vStr": "service-y"}, {"key": "peer.ipv4", "vType": "INT64", "vInt64": 23456}, {"key": "error", "vType": "BOOL", "vBool": true}, {"key": "temperature", "vType": "FLOAT64", "vFloat64": 72.5}, {"key": "javascript_limit", "vType": "INT64", "vInt64": 9223372036854775222}, {"key": "blob", "vType": "BINARY", "vBinary": "AAAwOQ=="}], "process": {"serviceName": "service-x"}}, {"traceId": "AAAAAAAAAAAAAAAAAAAAAQ==", "spanId": "AAAAAAAAAAM=", "references": [{"refType": "CHILD_OF", "traceId": "AAAAAAAAAAAAAAAAAAAAAQ==", "spanId": "AAAAAAAAAAI="}], "operationName": "some-operation", "startTime": "2017-01-26T16:46:31.639875139-05:00", "duration": "5000ns", "process": {"serviceName": "service-y"}}, {"traceId": "AAAAAAAAAAAAAAAAAAAAAQ==", "spanId": "AAAAAAAAAAQ=", "operationName": "reference-test", "references": [{"refType": "CHILD_OF", "traceId": "AAAAAAAAAAAAAAAAAAAA/w==", "spanId": "AAAAAAAAAP8="}, {"refType": "CHILD_OF", "traceId": "AAAAAAAAAAAAAAAAAAAAAQ==", "spanId": "AAAAAAAAAAI="}, {"refType": "FOLLOWS_FROM", "traceId": "AAAAAAAAAAAAAAAAAAAAAQ==", "spanId": "AAAAAAAAAAI="}], "startTime": "2017-01-26T16:46:31.639875139-05:00", "duration": "5000ns", "process": {"serviceName": "service-y"}, "warnings": ["some span warning"]}, {"traceId": "AAAAAAAAAAAAAAAAAAAAAQ==", "spanId": "AAAAAAAAAAU=", "operationName": "preserveParentID-test", "references": [{"refType": "CHILD_OF", "traceId": "AAAAAAAAAAAAAAAAAAAAAQ==", "spanId": "AAAAAAAAAAQ="}], "startTime": "2017-01-26T16:46:31.639875139-05:00", "duration": "4000ns", "process": {"serviceName": "service-y"}, "warnings": ["some span warning"]}], "processMap": [], "warnings": ["some trace warning"]}