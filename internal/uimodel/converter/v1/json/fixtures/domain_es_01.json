{"traceId": "AAAAAAAAAAAAAAAAAAAAAQ==", "spanId": "AAAAAAAAAAI=", "operationName": "test-general-conversion", "references": [{"refType": "CHILD_OF", "traceId": "AAAAAAAAAAAAAAAAAAAAAQ==", "spanId": "AAAAAAAAAAM="}, {"refType": "FOLLOWS_FROM", "traceId": "AAAAAAAAAAAAAAAAAAAAAQ==", "spanId": "AAAAAAAAAAQ="}, {"refType": "CHILD_OF", "traceId": "AAAAAAAAAAAAAAAAAAAA/w==", "spanId": "AAAAAAAAAP8="}], "flags": 1, "startTime": "2017-01-26T16:46:31.639875-05:00", "duration": "5000ns", "tags": [{"key": "peer.service", "vType": "STRING", "vStr": "service-y"}, {"key": "peer.ipv4", "vType": "INT64", "vInt64": 23456}, {"key": "error", "vType": "BOOL", "vBool": true}, {"key": "temperature", "vType": "FLOAT64", "vFloat64": 72.5}, {"key": "blob", "vType": "BINARY", "vBinary": "AAAwOQ=="}], "logs": [{"timestamp": "2017-01-26T16:46:31.639875-05:00", "fields": [{"key": "event", "vType": "INT64", "vInt64": 123415}]}, {"timestamp": "2017-01-26T16:46:31.639875-05:00", "fields": [{"key": "x", "vType": "STRING", "vStr": "y"}]}], "process": {"serviceName": "service-x", "tags": [{"key": "peer.ipv4", "vType": "INT64", "vInt64": 23456}, {"key": "error", "vType": "BOOL", "vBool": true}]}}