{"traceID": "0000000000000001", "spanID": "0000000000000002", "flags": 1, "operationName": "test-general-conversion", "references": [{"refType": "CHILD_OF", "traceID": "0000000000000001", "spanID": "0000000000000003"}, {"refType": "FOLLOWS_FROM", "traceID": "0000000000000001", "spanID": "0000000000000004"}, {"refType": "CHILD_OF", "traceID": "00000000000000ff", "spanID": "00000000000000ff"}], "startTime": 1485467191639875, "duration": 5, "tags": [{"key": "peer.service", "type": "string", "value": "service-y"}, {"key": "peer.ipv4", "type": "int64", "value": "23456"}, {"key": "error", "type": "bool", "value": "true"}, {"key": "temperature", "type": "float64", "value": "72.5"}, {"key": "blob", "type": "binary", "value": "00003039"}], "logs": [{"timestamp": 1485467191639875, "fields": [{"key": "event", "type": "int64", "value": "123415"}]}, {"timestamp": 1485467191639875, "fields": [{"key": "x", "type": "string", "value": "y"}]}], "process": {"serviceName": "service-x", "tags": [{"key": "peer.ipv4", "type": "int64", "value": "23456"}, {"key": "error", "type": "bool", "value": "true"}]}, "warnings": null}