{"traceID": "0000000000000001", "spans": [{"traceID": "0000000000000001", "spanID": "0000000000000002", "operationName": "test-general-conversion", "references": [], "startTime": 1485467191639875, "duration": 5, "tags": [], "logs": [{"timestamp": 1485467191639875, "fields": [{"key": "event", "type": "string", "value": "some-event"}]}, {"timestamp": 1485467191639875, "fields": [{"key": "x", "type": "string", "value": "y"}]}], "processID": "p1", "warnings": null}, {"traceID": "0000000000000001", "spanID": "0000000000000002", "operationName": "some-operation", "references": [], "startTime": 1485467191639875, "duration": 5, "tags": [{"key": "peer.service", "type": "string", "value": "service-y"}, {"key": "peer.ipv4", "type": "int64", "value": 23456}, {"key": "error", "type": "bool", "value": true}, {"key": "temperature", "type": "float64", "value": 72.5}, {"key": "javascript_limit", "type": "int64", "value": "9223372036854775222"}, {"key": "blob", "type": "binary", "value": "AAAwOQ=="}], "logs": [], "processID": "p1", "warnings": null}, {"traceID": "0000000000000001", "spanID": "0000000000000003", "operationName": "some-operation", "references": [{"refType": "CHILD_OF", "traceID": "0000000000000001", "spanID": "0000000000000002"}], "startTime": 1485467191639875, "duration": 5, "tags": [], "logs": [], "processID": "p2", "warnings": null}, {"traceID": "0000000000000001", "spanID": "0000000000000004", "operationName": "reference-test", "references": [{"refType": "CHILD_OF", "traceID": "00000000000000ff", "spanID": "00000000000000ff"}, {"refType": "CHILD_OF", "traceID": "0000000000000001", "spanID": "0000000000000002"}, {"refType": "FOLLOWS_FROM", "traceID": "0000000000000001", "spanID": "0000000000000002"}], "startTime": 1485467191639875, "duration": 5, "tags": [], "logs": [], "processID": "p2", "warnings": ["some span warning"]}, {"traceID": "0000000000000001", "spanID": "0000000000000005", "operationName": "preserveParentID-test", "references": [{"refType": "CHILD_OF", "traceID": "0000000000000001", "spanID": "0000000000000004"}], "startTime": 1485467191639875, "duration": 4, "tags": [], "logs": [], "processID": "p2", "warnings": ["some span warning"]}], "processes": {"p1": {"serviceName": "service-x", "tags": []}, "p2": {"serviceName": "service-y", "tags": []}}, "warnings": ["some trace warning"]}