services:
  opensearch:
    image: opensearchproject/opensearch:2.19.0@sha256:1f8b88245a6af61e7aa500afe0e87d43401e4b33140bb47230a919428ce3f7cb
    networks:
      - backend
    environment:
      - discovery.type=single-node
      - plugins.security.disabled=true
      - http.host=0.0.0.0
      - transport.host=127.0.0.1
      - OPENSEARCH_INITIAL_ADMIN_PASSWORD=passRT%^#234
    ports:
      - "9200:9200"
    healthcheck:
      test: [ "CMD-SHELL", "curl -f http://localhost:9200 || exit 1" ]
      interval: 10s
      timeout: 10s
      retries: 30

  jaeger:
    networks:
      backend:
        # This is the host name used in Prometheus scrape configuration.
        aliases: [ spm_metrics_source ]
    image: jaegertracing/jaeger:${JAEGER_VERSION:-latest}
    volumes:
      - "./jaeger-ui.json:/etc/jaeger/jaeger-ui.json"
      - "../../cmd/jaeger/config-spm-opensearch.yaml:/etc/jaeger/config.yml"
    command: ["--config", "/etc/jaeger/config.yml"]
    ports:
      - "16686:16686" # Jaeger UI http://localhost:16686
      - "8888:8888"
      - "8889:8889"
      - "4317:4317"
      - "4318:4318"
    depends_on:
      opensearch:
        condition: service_healthy

  microsim:
    networks:
      - backend
    image: yurishkuro/microsim:v0.5.0@sha256:b7ee2dee51d2c9fd94de08a80278cfbf5a144ad0f22efce50f3d3be15cbfa2c7
    command: "-d 24h -s 500ms"
    environment:
      - OTEL_EXPORTER_OTLP_TRACES_ENDPOINT=http://jaeger:4318/v1/traces
    depends_on:
      - jaeger

networks:
  backend:

volumes:
  esdata:
    driver: local
