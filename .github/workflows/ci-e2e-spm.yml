name: Test SPM

on:
  merge_group:
  push:
    branches: [main]

  pull_request:
    branches: [main]

concurrency:
  group: ${{ github.workflow }}-${{ (github.event.pull_request && github.event.pull_request.number) || github.ref || github.run_id }}
  cancel-in-progress: true

# See https://github.com/ossf/scorecard/blob/main/docs/checks.md#token-permissions
permissions:
  contents: read

jobs:
  spm:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        mode:
        - name: v1
          binary: all-in-one
          metricstore: prometheus
        - name: v2
          binary: jaeger
          metricstore: prometheus
        - name: v2 with ES
          binary: jaeger
          metricstore: elasticsearch
        - name: v2 with OS
          binary: jaeger
          metricstore: opensearch

    steps:
    - name: Harden Runner
      uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
      with:
        egress-policy: audit # TODO: change to 'egress-policy: block' after couple of runs

    - uses: actions/checkout@d632683dd7b4114ad314bca15554477dd762a938 # v4.2.0
      with:
        submodules: true

    - name: Fetch git tags
      run: |
        git fetch --prune --unshallow --tags

    - uses: actions/setup-go@d35c59abb061a4a6fb18e82ac0862c26744d6ab5 # v5.5.0
      with:
        go-version: 1.24.x

    - name: Setup Node.js version
      uses: ./.github/actions/setup-node.js
      
    - name: Run SPM Test
      run:  bash scripts/e2e/spm.sh -b ${{ matrix.mode.binary }} -m ${{ matrix.mode.metricstore }}